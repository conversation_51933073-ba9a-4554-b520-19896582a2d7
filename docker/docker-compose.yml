services:
  nacos-standalone:
    image: nacos/nacos-server:v3.0.2
    container_name: nacos-standalone
    environment:
      MODE: standalone
      NACOS_AUTH_TOKEN: U2VjcmV0S2V5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5
      NACOS_AUTH_ENABLE: "true"  # 开启鉴权
      NACOS_AUTH_IDENTITY_KEY: your_custom_key  # 安全密钥
      NACOS_AUTH_IDENTITY_VALUE: your_custom_value
    ports:
      - "18080:8080" # 管理页面
      - "8848:8848"
      - "9848:9848"
    volumes:
      - ~/docker/nacos/data:/home/<USER>/data
      - ~/docker/nacos/logs:/home/<USER>/logs

  dubbo-admin:
    image: apache/dubbo-admin:0.6.0
    container_name: dubbo-admin
    environment:
      admin.registry.address: nacos://nacos-standalone:8848?username=nacos&password=nacos
      admin.config-center: nacos://nacos-standalone:8848?username=nacos&password=nacos
      admin.metadata-report.address: nacos://nacos-standalone:8848?username=nacos&password=nacos
      admin.root.user.name: root
      admin.root.user.password: root
    ports:
      - "38080:38080" # 管理页面
    depends_on:
      - nacos-standalone