package com.chinaunicom.qos.dedbearer.utils;

import com.chinaunicom.qos.common.enums.*;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.DurationCheck;
import com.chinaunicom.qos.common.utils.MatcherCheck;
import com.chinaunicom.qos.dedbearer.api.enums.DedBearerRespCodeEnum;
import com.chinaunicom.qos.common.enums.DedOperTypeEnum;
import com.chinaunicom.qos.dedbearer.exception.DedBearerException;

import com.chinaunicom.qos.dedbearer.infrastructure.config.TargetIpCntConfig;
import com.chinaunicom.qos.snapshot.api.dto.CmServiceCheckConfigDTO;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidateUtil {

    @SuppressWarnings("java:S3776")
    public static void ipAddressCheck(String publicIp, String privateIp, List<String> targetIps) {
        int code = DedBearerRespCodeEnum.PARAM_INVALID.getCode();
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append(DedBearerRespCodeEnum.PARAM_INVALID.getDesc()).append(": ");
        if (MatcherCheck.validIPV4(privateIp)) {
            if(StringUtils.isBlank(publicIp) || MatcherCheck.validIPV4(publicIp)) {
                for (String ip : targetIps) {
                    if(!MatcherCheck.validIPV4(ip)) {
                        log.info("公私网IP为IPv4地址，目标IP不是合法的IPv4地址：{}", ip);
                        errorMsg.append("目标IP与公私网IP格式不一致");
                        throw new DedBearerException(code, errorMsg.toString());
                    }
                }
            }
            else {
                log.info("私网IP为IPv4地址，公网IP不是合法的IPv4地址");
                errorMsg.append("公私网IP地址格式不一致");
                throw new DedBearerException(code, errorMsg.toString());
            }
        }
        else if (MatcherCheck.validIPV6(privateIp)) {
            if(StringUtils.isBlank(publicIp) || MatcherCheck.validIPV6(publicIp)) {
                for (String ip : targetIps) {
                    if(!MatcherCheck.validIPV6(ip)) {
                        log.info("公私网IP为IPv6地址，目标IP不是合法的IPv6地址：{}", ip);
                        errorMsg.append("目标IP与公私网IP格式不一致");
                        throw new DedBearerException(code, errorMsg.toString());
                    }
                }
            }
            else {
                log.info("私网IP{}为IPv6地址，公网IP{}不是合法的IPv6地址", privateIp, publicIp);
                errorMsg.append("公私网IP地址格式不一致");
                throw new DedBearerException(code, errorMsg.toString());
            }
        }
        else {
            log.info("私网IP地址非法：{}", privateIp);
            errorMsg.append("私网IP地址格式错误");
            throw new DedBearerException(code, errorMsg.toString());
        }
    }

    public static void targetIpCountCheck(List<String> targetIps, TargetIpCntConfig targetIpCntConfig) {
        if((MatcherCheck.validIPV4(targetIps.getFirst()) && targetIps.size() > targetIpCntConfig.getTargetIpMaxCntV4()) ||
                (MatcherCheck.validIPV6(targetIps.getFirst()) && targetIps.size() > targetIpCntConfig.getTargetIpMaxCntV6())) {
            throw new DedBearerException(DedBearerRespCodeEnum.TARGET_IP_EXCEED_ALLOWED_AMOUNT);
        }
    }

    public static void checkTargetIpWhiteList(List<String> targetIps, String whiteIps) {
        List<String> whiteList = getIpListFromString(whiteIps);

        for (String ip : targetIps) {
            if(!whiteList.contains(ip)) {
                throw new DedBearerException(DedBearerRespCodeEnum.TARGET_IP_INVALID.getCode(),
                        DedBearerRespCodeEnum.TARGET_IP_INVALID.getDesc()+" 目标IP白名单");
            }
        }
    }

    public static void checkTargetIpBlackList(List<String> targetIps, String blackIps) {
        List<String> blackList = getIpListFromString(blackIps);

        for (String ip : targetIps) {
            if(blackList.contains(ip)) {
                throw new DedBearerException(DedBearerRespCodeEnum.TARGET_IP_INVALID.getCode(),
                        DedBearerRespCodeEnum.TARGET_IP_INVALID.getDesc()+" 目标IP黑名单");
            }
        }
    }

    public static boolean validateRequestData (DedOperTypeEnum operType, CmsSnapshotDTO cmsSnapshot,
                                               UserInfoDTO userInfo, SubscribeInfoDTO subscribeInfo,
                                               List<String> targetIps, Integer duration) {
        //通信服务状态校验，只支持状态为“使用中”的通信服务
        if(!StatusEnum.ACTIVE.getStatus().equals(cmsSnapshot.getCmServiceStatus())) {
            log.info("通信服务状态校验失败，cmServiceId={}, status={}", cmsSnapshot.getCmServiceId(), cmsSnapshot.getCmServiceStatus());
            throw new DedBearerException(DedBearerRespCodeEnum.CMSERVICE_INVALID);
        }

        //网络模式一致性校验
        if(!cmsSnapshot.getNetworkMode().equals(NetworkModeEnum.FOUR_FIVE_GEN.getCode()) &&
                !userInfo.getSignNetworkType().equals(cmsSnapshot.getNetworkMode())) {
            log.info("网络模式一致性校验失败，通信服务网络模式={}，用户签约的网络类型={}", cmsSnapshot.getNetworkMode(), userInfo.getSignNetworkType());
            throw new DedBearerException(DedBearerRespCodeEnum.NETWORK_TYPE_MISMATCH);
        }


        //产品订购校验
        var now = new Date();
        var userEndTime = DateUtils.computeDate(now, duration);
        if(subscribeInfo.getEffectiveTime().after(now) || subscribeInfo.getExpireTime().before(userEndTime)) {
            log.info("产品订购校验失败，订购生效时间={}，订购失效时间={}，专载载期望终止时间={}，当前时间={}",
                    DateUtils.formatDate(subscribeInfo.getEffectiveTime()), DateUtils.formatDate(subscribeInfo.getExpireTime()),
                    DateUtils.formatDate(userEndTime), DateUtils.formatDate(now));
            throw new DedBearerException(DedBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH);
        }

        //产品和通信服务一致性校验
        if(DedOperTypeEnum.ADD.equals(operType) && !subscribeInfo.getCmServiceIdList().contains(cmsSnapshot.getCmServiceId())) {
            log.info("产品和通信服务一致性校验失败，产品配置的通信服务列表={}，请求的通信服务={}", subscribeInfo.getCmServiceIdList(), cmsSnapshot.getCmServiceId());
            throw new DedBearerException(DedBearerRespCodeEnum.PRODUCT_CMSERVICE_MISMATCH);
        }

        //归属省校验，只有申请需要做
        if(DedOperTypeEnum.ADD.equals(operType) && !cmsSnapshot.getHomeProvinceList().contains(ProvinceCodeEnum.CHINA.getCode2()) &&
                !cmsSnapshot.getHomeProvinceList().contains(userInfo.getHomeProvince())) {
            log.info("归属省校验失败，通信服务配置的归属省={}，用户归属省={}", cmsSnapshot.getHomeProvinceList(), userInfo.getHomeProvince());
            throw new DedBearerException(DedBearerRespCodeEnum.HOME_PROVINCE_NOT_ALLOWED);
        }

        return validateConfiguredChecks(cmsSnapshot, userInfo, duration.longValue(), targetIps);
    }

    @SuppressWarnings("java:S3776")
    public static boolean validateConfiguredChecks(CmsSnapshotDTO cmsSnapshot, UserInfoDTO userInfo, Long duration,
                                                   List<String> targetIps) {
        var allow5g = false;
        if (cmsSnapshot.getCheckConfigList() != null && !cmsSnapshot.getCheckConfigList().isEmpty()) {
            for (CmServiceCheckConfigDTO config : cmsSnapshot.getCheckConfigList()) {
                //拜访省校验（如果没有获取到拜访省也放通）
                if (CmServiceCheckKeyEnum.VISIT_PROVINCE.getKey().equals(config.getCheckKey()) && userInfo.getVisitProvince()!=null &&
                        !config.getCheckValue().contains(String.valueOf(userInfo.getVisitProvince()))) {
                    log.info("拜访省校验失败，通信服务配置的拜访省={}，用户拜访省={}", config.getCheckValue(), userInfo.getVisitProvince());
                    throw new DedBearerException(DedBearerRespCodeEnum.VISIT_PROVINCE_NOT_ALLOWED);
                }
                //时长校验
                if (CmServiceCheckKeyEnum.DURATION.getKey().equals(config.getCheckKey()) &&
                        DurationCheck.checkDurationFailed(config.getCheckValue(), duration)) {
                    log.info("时长校验失败，通信服务配置的时长规则={}，duration={}", config.getCheckValue(), duration);
                    throw new DedBearerException(DedBearerRespCodeEnum.DURATION_INVALID);
                }
                if (CmServiceCheckKeyEnum.TARGET_IP_WHITE.getKey().equals(config.getCheckKey())) {
                    //目标IP白名单校验
                    checkTargetIpWhiteList(targetIps, config.getCheckValue());
                }
                if (CmServiceCheckKeyEnum.TARGET_IP_BLACK.getKey().equals(config.getCheckKey())) {
                    //目标IP黑名单校验
                    checkTargetIpBlackList(targetIps, config.getCheckValue());
                }
                if (CmServiceCheckKeyEnum.TARGET_IP_MAX_COUNT.getKey().equals(config.getCheckKey()) &&
                        targetIps.size() > Integer.parseInt(config.getCheckValue())) {
                    log.info("目标IP个数校验失败，通信服务配置的最大允许个数={}，请求的目标IP个数={}", config.getCheckValue(), targetIps.size());
                    throw new DedBearerException(DedBearerRespCodeEnum.TARGET_IP_EXCEED_ALLOWED_AMOUNT);
                }
                if (CmServiceCheckKeyEnum.ALLOW_5G.getKey().equals(config.getCheckKey())) {
                    allow5g = true;
                }
            }
        }
        return allow5g;
    }

    private static List<String> getIpListFromString(String ipStr) {
        List<String> ipList = new ArrayList<>();
        if(StringUtils.isNotBlank(ipStr)) {
            ipList = Arrays.asList(ipStr.split(","));
        }
        return ipList;
    }
}
