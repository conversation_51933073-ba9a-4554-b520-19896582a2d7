<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chinaunicom.qos</groupId>
        <artifactId>qos-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>qos-dedbearer</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>qos-dedbearer-api</module>
        <module>qos-dedbearer-common</module>
        <module>qos-dedbearer-server</module>
        <module>qos-dedbearer-async-service</module>
        <module>qos-dedbearer-http-service</module>
    </modules>

    <properties>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <javax.el.version>3.0.1-b12</javax.el.version>
        <hibernate.validator.version>6.1.5.Final</hibernate.validator.version>
        <redission.version>3.30.0</redission.version>
        <mybatis.plus.boot.version>3.5.6</mybatis.plus.boot.version>
        <skywalking.version>9.2.0</skywalking.version>
        <spring.kafka.version>3.0.10</spring.kafka.version>
        <spring-boot.nacos.version>0.3.0-RC</spring-boot.nacos.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-dedbearer-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-dedbearer-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-snapshot-api-new</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-user-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-idgen-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-dedbearer-adapter-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-task-executor</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.qos</groupId>
                <artifactId>qos-mock-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${spring-boot.nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation.api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>${javax.el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redission.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2022.0.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>