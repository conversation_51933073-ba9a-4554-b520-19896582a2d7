#!/bin/bash

# 定义命名空间和工作节点名称
NAMESPACE="slice-qos"
WORKER_NODES=($(kubectl get po -n slice-qos | grep jmeter-worker | grep Running | awk '{print $1}'))

# 循环重启每个工作节点
for WORKER in "${WORKER_NODES[@]}"; do
    echo "正在重启 $WORKER ..."

    # 重启工作节点
    kubectl delete pod "$WORKER" -n "$NAMESPACE"

    # 等待工作节点重新启动
    echo "等待 $WORKER 重启..."
    kubectl wait --for=condition=ready pod/"$WORKER" -n "$NAMESPACE" --timeout=60s

    echo "$WORKER 重启成功。"
done

echo "所有工作节点已重启。"