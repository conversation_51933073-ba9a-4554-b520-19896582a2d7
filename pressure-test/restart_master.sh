#!/bin/bash

NAMESPACE="slice-qos"
MASTER_LABEL="jmeter-master"

# 获取运行中的 master pod
MASTER_POD=$(kubectl get po -n "$NAMESPACE" -l app="$MASTER_LABEL" -o jsonpath='{.items[0].metadata.name}')
echo "当前正在重启的 master pod: $MASTER_POD"

# 删除 master pod
kubectl delete pod "$MASTER_POD" -n "$NAMESPACE"

# 等待新 master pod 运行
echo "等待新的 master pod 启动..."
kubectl wait --for=condition=ready -l app="$MASTER_LABEL" pod -n "$NAMESPACE" --timeout=60s

# 获取新的 master pod 名称并显示
NEW_MASTER_POD=$(kubectl get po -n "$NAMESPACE" -l app="$MASTER_LABEL" -o jsonpath='{.items[0].metadata.name}')
echo "新的 master pod 已成功启动: $NEW_MASTER_POD"