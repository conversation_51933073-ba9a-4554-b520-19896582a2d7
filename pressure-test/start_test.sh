#!/bin/bash

# 如果传入了4个参数，直接使用传入的参数
if [ "$#" -ge 4 ]; then
    author="$1"
    jmx_file="$2"
    csv_file="$3"
    result_path="$4"
    worker_hosts="$5"
    echo "已检测到传入的参数，直接使用："
else
    # 如果参数不够，则提示用户输入
    echo "请输入参数：<author> <jmx文件> <csv文件> <结果路径> <压力机地址>"
    read -r author jmx_file csv_file result_path worker_hosts
fi

# 检查输入的参数数量
if [[ -z "$author" || -z "$jmx_file" || -z "$csv_file" || -z "$result_path" ]]; then
    echo "参数不全，请重新运行脚本并确保输入完整的参数。"
    exit 1
fi

# 输出用户传入的所有参数
echo "您输入的参数如下："
echo "Author: $author"
echo "JMX文件: $jmx_file"
echo "CSV文件: $csv_file"
echo "结果路径: $result_path"
echo "worker_hosts: $worker_hosts"

# 设置默认worker_hosts
if [ -z "$worker_hosts" ]; then
    worker_hosts="jmeter-worker-0.jmeter-worker-service"
fi


# 取 JMX 和 CSV 文件的文件名
jmx="$(basename "$jmx_file")"
csv="$(basename "$csv_file")"

echo "Step1 检查并创建目标文件夹"
master_pod=$(kubectl get po -n slice-qos | grep jmeter-master | grep Running | awk '{print $1}')
kubectl exec -n slice-qos -c jmeter-master "$master_pod" -- bash -c "mkdir -p /opt/jmeter/jmx/$author /opt/jmeter/jtl/$author /opt/jmeter/web/$author"

slave_pod=$(kubectl get po -n slice-qos | grep jmeter-worker | grep Running | awk '{print $1}')
for i in $slave_pod; do
    kubectl exec -n slice-qos "$i" -- bash -c "mkdir -p /opt/jmeter/jmx/$author"
done

echo "Step2 拷贝jmx至master"
kubectl cp "$jmx_file" -c jmeter-master "$master_pod:/opt/jmeter/jmx/$author/$jmx" -n slice-qos

echo "Step3 拷贝csv至slave"
for i in $slave_pod; do
    kubectl cp "$csv_file" "$i:/opt/jmeter/jmx/$author/$csv" -n slice-qos
done

echo "Step4 开启压测"
kubectl exec -ti "$master_pod" -n slice-qos -c jmeter-master -- bash run.sh \
    jmx/$author/$jmx \
    jtl/$author/test.jtl \
    web/$author/"$result_path" \
    "$worker_hosts"

echo "Step5 删除结果文件"
rm -rf ./web/$author/"$result_path"

echo "Step6 结果文件存入"
kubectl cp -c jmeter-master "$master_pod:/opt/jmeter/web/$author/$result_path" ./web/$author/"$result_path" -n slice-qos
echo "结果文件已下载到当前目录的/web/$author/$result_path 目录中"