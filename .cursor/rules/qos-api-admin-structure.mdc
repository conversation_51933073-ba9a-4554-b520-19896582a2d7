---
description: 
globs: 
alwaysApply: false
---
# qos-api-admin 模块结构规范

本规范详细定义 `qos-api-admin` 模块的内部包结构、各包职责以及其中应存放的文件类型。
`qos-api-admin` 作为管理网关服务，其主要职责是对外暴露管理后台相关的 API 接口。

## 模块根包

- **`com.chinaunicom.qos.api.admin`**: 模块的根包。

## 核心包结构及职责

### 1. `starter` 包
   - **路径**: `com.chinaunicom.qos.api.admin.starter`
   - **职责**: 负责项目启动配置和业务入口，包括接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理等工作。这是 Spring Boot 应用的典型入口和配置区域。

   #### 1.1. `starter.controller`
      - **路径**: `com.chinaunicom.qos.api.admin.starter.controller`
      - **职责**: 存放所有对外暴露的 HTTP RESTful API 接口的控制器类。
      - **文件类型**: `*Controller.java` (例如 `UserManagementController.java`, `SystemConfigController.java`)。
      - **规范**:
          - 控制器应处理 HTTP 请求的接收、参数校验（推荐使用 JSR 303/Jakarta Bean Validation）、调用应用层服务，并封装返回 HTTP 响应（通常为通用的 `Resp<T>` 结构）。
          - 网关 Controller 使用的请求模型和响应数据模型（DTO）应在此包的子包中单独定义。

      ##### 1.1.1. `starter.controller.request`
         - **路径**: `com.chinaunicom.qos.api.admin.starter.controller.request`
         - **职责**: 专门存放对应 Controller API 接口的请求模型对象。
         - **文件类型**: `*Req.java` (例如 `CreateUserReq.java`, `UpdateSettingsReq.java`)。
         - **规范**: 遵循项目命名约定，请求对象以 `Req` 结尾。

      ##### 1.1.2. `starter.controller.response`
         - **路径**: `com.chinaunicom.qos.api.admin.starter.controller.response`
         - **职责**: 专门存放对应 Controller API 接口返回的通用 `Resp<T>` 结构中的 `T` 部分的数据传输对象 (DTO)。
         - **文件类型**: `*DTO.java` (例如 `UserInfoDTO.java`, `SystemStatusDTO.java`)。
         - **规范**:
             - 这些 DTO 代表了API响应的实际业务数据结构。
             - 遵循项目命名约定，DTO 对象以 `DTO` 结尾。

   #### 1.2. `starter.config`
      - **路径**: `com.chinaunicom.qos.api.admin.starter.config`
      - **职责**: 存放应用的配置类，如 Spring Boot 的配置、Bean 定义、安全配置、跨域配置等。
      - **文件类型**: `*Config.java` (例如 `WebMvcConfig.java`, `SecurityConfig.java`, `CorsConfig.java`)。

   #### 1.3. `starter.service`
      - **路径**: `com.chinaunicom.qos.api.admin.starter.service`
      - **职责**: 在网关层面，此包可能用于一些非常轻量级的、与特定网关逻辑相关的服务实现，或者作为对 `application` 层服务的进一步封装或适配。但主要业务逻辑应在 `application` 层。
      - **文件类型**: `*Service.java`。

   #### 1.4. `starter.job`
      - **路径**: `com.chinaunicom.qos.api.admin.starter.job`
      - **职责**: 存放定时任务类。
      - **文件类型**: `*Job.java` (例如 `CacheRefreshJob.java`)。

   #### 1.5. `starter.consumer`
      - **路径**: `com.chinaunicom.qos.api.admin.starter.consumer`
      - **职责**: 存放消息队列的消费者类（例如 Kafka, RabbitMQ）。
      - **文件类型**: `*Consumer.java` (例如 `NotificationConsumer.java`)。

### 2. `application` 包
   - **路径**: `com.chinaunicom.qos.api.admin.application`
   - **职责**: 负责具体的应用服务和业务用例实现。通常串联内部领域服务接口（如果模块内有 domain 层）或调用外部微服务接口，实现流程编排、聚合查询等。

   #### 2.1. `application.service`
      - **路径**: `com.chinaunicom.qos.api.admin.application.service`
      - **职责**: 存放应用服务类，实现核心业务逻辑。Controller 层通常调用这里的服务。
      - **文件类型**: `*AppService.java` 或 `*Service.java` (例如 `UserAppService.java`, `OrderQueryService.java`)。

   #### 2.2. `application.bo` (Business Object)
      - **路径**: `com.chinaunicom.qos.api.admin.application.bo`
      - **职责**: 存放应用服务层使用的业务对象。这些对象可能用于封装应用服务方法的参数或返回值，或者在应用服务内部流转。
      - **文件类型**: `*BO.java` (例如 `UserProfileBO.java`, `ComplexQueryBO.java`)。
      - **注意**: Controller 直接使用的请求对象 (`*Req`) 和响应中业务数据部分的 DTO (`*DTO`) 分别定义在 `starter.controller.request` 和 `starter.controller.response` 包下。`application.bo` 更偏向于应用层内部的业务数据结构或复杂参数封装。

   #### 2.3. `application.enums`
      - **路径**: `com.chinaunicom.qos.api.admin.application.enums`
      - **职责**: 存放应用层及内部逻辑使用的枚举类。
      - **文件类型**: `*Enum.java` (例如 `OrderStatusEnum.java`, `UserTypeEnum.java`)。

   #### 2.4. `application.constant`
      - **路径**: `com.chinaunicom.qos.api.admin.application.constant`
      - **职责**: 存放应用层及内部逻辑使用的常量。
      - **文件类型**: `*Constants.java` (例如 `CommonConstants.java`, `CacheKeyConstants.java`)。

### 3. `dto` 包 (可选, 位于模块根路径或 `application` 下)
   - **路径**: `com.chinaunicom.qos.api.admin.dto` 或 `com.chinaunicom.qos.api.admin.application.dto`
   - **职责**: 存放通用的数据传输对象。这些 DTO 可能用于：
      -  作为 `*Req` 对象内部的复杂类型。
      -  应用层服务之间传递数据。
      -  不直接作为 Controller API 响应体主要内容的共享数据结构。
   - **文件类型**: `*DTO.java` (例如 `AddressDTO.java`, `OrderItemDTO.java`)。
   - **规范**: 遵循项目命名约定，DTO 对象以 `DTO` 结尾。与 `starter.controller.response` 中的 DTO 区分，后者特指 API 响应的业务数据部分。

### 4. `infrastructure` 包
   - **路径**: `com.chinaunicom.qos.api.admin.infrastructure`
   - **职责**: 负责技术基础设施相关的实现，如外部服务调用（RPC 客户端代理）、数据库访问封装（如果网关需要直连数据库，但不推荐）、缓存操作等。

   #### 4.1. `infrastructure.proxy` (示例)
      - **路径**: `com.chinaunicom.qos.api.admin.infrastructure.proxy`
      - **职责**: 存放调用其他微服务的代理客户端或适配器。
      - **文件类型**: `*Proxy.java` 或 `*Client.java` (例如 `UserServiceProxy.java`)。

   #### 4.2. `infrastructure.cache` (示例)
      - **路径**: `com.chinaunicom.qos.api.admin.infrastructure.cache`
      - **职责**: 封装缓存相关的操作。
      - **文件类型**: `*Cache.java` (例如 `UserSessionCache.java`)。

### 5. `exception` 包
   - **路径**: `com.chinaunicom.qos.api.admin.exception`
   - **职责**: 存放自定义的业务异常类和全局异常处理器。
   - **文件类型**:
      - `*Exception.java` (例如 `InvalidInputException.java`, `ResourceNotFoundException.java`)。
      - `GlobalExceptionHandler.java` (使用 `@ControllerAdvice` 注解)。

### 6. `utils` 包
   - **路径**: `com.chinaunicom.qos.api.admin.utils`
   - **职责**: 存放通用的工具类。
   - **文件类型**: `*Util.java` 或 `*Utils.java` (例如 `DateUtil.java`, `ValidationUtils.java`)。

## 启动类
- **文件**: `com.chinaunicom.qos.api.admin.ApiAdminApplication.java`
- **职责**: Spring Boot 应用的启动入口类，包含 `main` 方法和 `@SpringBootApplication` 注解。

## 注意事项
- 本规范基于对 `qos-api-admin` 模块的现有结构探查和项目通用规范的结合。
- 实际开发中，如果模块职责简单，某些包（如 `domain` 层）可能不存在。
- Controller 的请求模型 (`*Req.java`) 和响应业务数据模型 (`*DTO.java`) 分别位于 `starter.controller.request` 和 `starter.controller.response` 包下。
- 所有命名和结构应遵循项目已有的代码约定和开发规范。



