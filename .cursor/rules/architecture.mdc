---
description: 
globs: 
alwaysApply: false
---
# 切片QoS平台架构

## 服务分层架构

切片QoS平台采用分层架构设计:

1. **API层**：负责对外暴露服务接口
   - xxx-api 模块: 定义对外接口、请求/响应模型

2. **服务层**：负责业务实现
   - xxx-server 模块: 项目启动配置和业务入口
   - 包含 starter、application、domain、infrastructure 等包

3. **基础设施层**：提供技术支持
   - 数据访问、缓存、消息等基础服务

## 模块命名和职责

每个模块通常包含以下子模块：
- **xxx-api**: 包含请求/响应模型和服务接口定义
- **xxx-server**: 具体服务实现
- **xxx-common**: 公共组件和工具类

## 代码组织结构

```
模块
├── xxx-api        // 服务接口定义
│   ├── request    // 请求对象 (xxxReq)
│   ├── response   // 响应对象 (xxxResp)
│   ├── dto        // 数据传输对象 (xxxDTO)
│   ├── enums      // 枚举
│   └── service    // 服务接口
├── xxx-server     // 服务实现
│   ├── starter    // 启动配置
│   │   ├── service    // 服务实现类
│   │   ├── controller // HTTP接口
│   │   ├── job // 任务类
│   │   ├── consumer // 消费者类
│   │   ├── config     // 配置
│   ├── application    // 应用服务
│   │   ├── constant     // 常量
│   │   ├── enums     // 枚举
│   ├── infrastructure // 基础设施
│   │   ├── config        // 基础设施配置类
│   │   ├── dal        // 数据访问
│   │   │     ├── mapper    //mybatis映射
│   │   │     ├── po    //持久化对象
│   │   │     ├── repo    //仓储接口实现
│   │   ├── proxy      // 外部服务代理
│   │   ├── cache      // 缓存
│   └── exception      // 异常定义
│   └── utils      // 工具
└── xxx-common     // 公共组件
```

## 对象命名规范

- **xxxReq**: API请求参数对象
- **xxxResp**: API响应结果对象
- **xxxDTO**: 数据传输对象
- **xxxBO**: 业务对象
- **xxxDO**: 领域对象
- **xxxPO**: 持久化对象












 