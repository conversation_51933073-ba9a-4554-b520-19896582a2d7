---
description: 
globs: 
alwaysApply: false
---
# 切片QoS平台开发规范

## 代码结构规范

### 1. 服务分层

|  **模块**  |  **包**  |  **职责**  |
| --- | --- | --- |
|  xxx-api  |  request、response、service  |  负责服务对外暴露的接口定义  |
|  xxx-server  |  starter  |  负责项目启动配置和业务入口,包括接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理等工作  |
|  |  application  |  负责用例实现,通常串联内部领域服务接口或外部服务接口,实现流程编排、聚合查询等工作流操作  |
|  |  domain  |  负责业务逻辑实现,通常抽象出领域对象,来表达业务概念,承载业务行为和规则。业务逻辑不复杂,可以没有  |
|  |  infrastructure  |  通常负责外部调用,比如数据库的CRUD、搜索引擎、文件系统、分布式服务的RPC等  |

### 2. 包和对象命名规范
|  **模块**  |  **包**  |  |  **命名规范**  |  **职责说明**  |
| --- | --- | --- | --- | --- |
|  api  |  request  |  |  xxxReq  |  API接口模型中的请求参数对象  |
|  |  response  |  |  xxxResp  |  API接口模型中的返回结果对象  |
|  |  dto  |  |  xxxDTO  |  API接口模型中的传输对象  |
|  |  enums  |  |  xxxEnum  |  API中需要使用方感知的枚举对象  |
|  |  service  |  |  xxxService  |  服务接口  |
|  server  |  \--  |  \--  |  xxxApplication  |  启动器类  |
|  |  starter  |  service  |  xxxService  |  服务接口实现  |
|  |  |  job  |  xxxJob  |  定时任务  |
|  |  |  controller  |  xxxController  |  HTTP接口  |
|  |  |  consumer  |  xxxConsumer  |  Kafka的消费者  |
|  |  |  config  |  xxxConfig  |  服务配置信息  |
|  |  |  aop  |  xxxAop  |  切面类  |
|  |  application  |  model  |  xxxBO  |  可选,应用接口模型中的请求对象和返回对象  |
|  |  |  \--  |  xxxAppService  |  应用服务  |
|  |  domain  |  model  |  xxxDO  |  可选,领域对象,包括聚合根、实体和值对象  |
|  |  |  service  |  xxxDomainService  |  可选,领域服务  |
|  |  |  constant  |  xxxConstant  |  常量。如果没有domain,可放applicaiton包里  |
|  |  |  enums  |  xxxEnum  |  枚举。如果没有domain,可放applicaiton包里  |
|  |  infrastructure  |  dal  |  repo.xxxRepo mapper.xxxMapper po.xxxPO  |  数据访问层。PO是持久化对象,与数据库表对应。数据映射类命令后缀为Mapper。Repo用于对mapper进行封装,提供仓储服务  |
|  |  |  proxy  |  xxxProxy  |  外部服务代理封装  |
|  |  |  es  |  xxxEs  |  搜索  |
|  |  |  cache  |  xxxCache  |  对缓存客户端的封装  |
|  |  |  config  |  xxxConfig  |  业务配置  |
|  |  |  producer  |  xxxProducer  |  消息发布  |
|  |  exception  |  \--  |  xxxException  |  业务自定义异常  |
|  |  utils  |  \--  |  xxxUtil  |  工具类  |

## 最佳实践

### 1. 代码组织
- 遵循上述分层架构和命名规范
- 保持代码整洁,避免重复代码
- 单一职责原则:一个类只负责一项功能

### 2. 错误处理
- 使用自定义异常类表达业务异常
- 合理处理和记录异常
- 避免吞掉异常

### 3. API设计
- 符合RESTful设计规范
- 正确使用HTTP方法和状态码
- 请求和响应结构一致

### 4. 数据库访问
- 使用合适的事务隔离级别
- 避免大事务
- 注意SQL性能优化
- 获取单个对象的方法用 get 做前缀。
- 获取多个对象的方法用 list 做前缀,复数结尾,如:listObjects。
- 获取统计值的方法用 count 做前缀。
- 插入的方法用 save / insert 做前缀。
- 删除的方法用 remove / delete 做前缀。
- 修改的方法用 update 做前缀。
- mybatis的xml中语句的大于和小于号使用&gt;&lt;来替换

### 5.注释规范
- 类注释:位于类定义的上方,使用/** */格式的多行注释。 如
/**
 * 取号服务
 *
 * <AUTHOR>
 * @date 2025-06-04 9:53
 */
- 方法注释:位于方法定义的上方,使用/** */格式的多行注释,复杂方法需要注释,简单方法无须注释。如
/**
 * 获取号卡基础信息
 */
- 所有生成注释的作者均写成pengb7
- 不要使用行尾注释

### 6.日志级别设置
- ERROR:用于记录严重的错误,通常表示程序错误影响到实际业务,需要人工介入的情况。
- WARN:用于记录不太严重但仍需要关注的情况,可能表示潜在的问题或异常。
- INFO:用于记录重要的业务流程和关键操作的信息。
- DEBUG:用于在开发和调试阶段记录详细的调试信息,在生产环境中通常应关闭。

### 7.方法
- 理想情况下,单方法的长度应控制在 20 - 100 行代码,行数不超过 100 ,宽度不超过 120。
- 对于复杂的逻辑,可以分解为多个私有辅助方法,每个辅助方法专注于一个特定的子任务。



