---
description: 
globs: 
alwaysApply: false
---
# 切片QoS平台代码约定

## 技术栈约定

本项目使用以下主要技术栈:
- JDK 21
- Spring Boot 3.1.2
- Dubbo 3.3.0
- MySQL 8.0.33
- MyBatis
- Redis (通过Redisson)
- Kafka

## 命名约定

### 1. 通用命名规则
- 类名:使用大驼峰命名法,如`UserService`
- 方法名:使用小驼峰命名法,如`getUserById`
- 常量:全部大写,单词间用下划线分隔,如`MAX_RETRY_COUNT`
- 包名:全部小写,如`com.chinaunicom.qos.user`

### 2. 特定对象命名规则
- API请求对象:以`Req`结尾,如`CreateUserReq`
- API响应对象:以`Resp`结尾,如`UserInfoResp`
- 数据传输对象:以`DTO`结尾,如`UserDTO`
- 业务对象:以`BO`结尾,如`UserBO`
- 领域对象:以`DO`结尾,如`UserDO`
- 持久化对象:以`PO`结尾,如`UserPO`
- 枚举类:以`Enum`结尾,如`UserStatusEnum`
- 常量类:以`Constants`结尾,如`CommonConstants`
- 数据mybatis映射类:以`Mapper`结尾,如`QosTaskDefbearerMapper`
- 仓储接口类:以`Repo`结尾,开头增加前缀`I`代表接口,如`IQosTaskDefbearerRepo`
- 仓储接口实现类:以`RepoImpl`结尾,开头增加前缀`I`代表接口,如`IQosTaskDefbearerRepo`

## 代码风格约定

### 1. 缩进和格式
- 使用4个空格进行缩进
- 单行最大长度为120个字符
- 花括号使用K&R风格(左花括号不换行)

### 2. 导入声明
- 不使用通配符导入
- 导入顺序:Java核心库 > 第三方库 > 项目内部类

### 3. 注释规范
- 类、方法应有相应的JavaDoc注释
- 复杂逻辑应有必要的行内注释
- TODO、FIXME等特殊注释应明确指出问题和责任人

### 4. 异常处理
- 不捕获通用异常(Exception),应捕获特定异常
- 捕获异常后应适当处理,不应忽略
- 自定义异常应继承自合适的异常基类

## 数据库规范

### 1. 表命名
- 使用下划线命名法
- 表名应使用小写字母
- 使用复数形式命名,如`users`、`orders`

### 2. 字段命名
- 使用下划线命名法
- 字段名应使用小写字母
- 主键一般命名为`id`
- 外键一般命名为`{关联表名}_id`
- 创建和更新时间字段分别命名为`create_time`和`update_time`

### 3. SQL编写
- 避免使用SELECT * 
- 大写SQL关键字
- 适当使用索引优化查询性能

### 4. 数据访问层
- 数据库查询,必须创建对应的Repo仓储接口进行访问,访问路径为:调用类->仓储接口->仓储接口实现类->数据mybatis映射类






