---
description: 
globs: 
alwaysApply: false
---
# 切片QoS平台模块导航

## 核心业务模块

### 1. 默载模块
- **qos-defbearer**: 默载相关功能
  - qos-defbearer-api: 接口定义
  - qos-defbearer-server: 服务实现
  - qos-defbearer-async-service: 异步处理服务
  - qos-defbearer-http-service: HTTP服务

### 2. 专载模块
- **qos-dedbearer**: 专载相关功能
  - qos-dedbearer-api: 接口定义
  - qos-dedbearer-server: 服务实现
  - qos-dedbearer-async-service: 异步处理服务
  - qos-dedbearer-http-service: HTTP服务

### 3. 适配器模块
- **qos-dedbearer-adapter**: 专载适配器
- **qos-defbearer-adapter**: 默载适配器

### 4. 用户模块
- **qos-user**: 用户管理相关功能
  - qos-user-api: 接口定义
  - qos-user-server: 服务实现
  - qos-user-consumer: 消费者服务
  - qos-user-sync: 同步服务

### 5. 批量处理模块
- **qos-batch**: 批量处理相关功能
  - qos-batch-api: 接口定义
  - qos-batch-service: 服务实现
  - qos-batch-async: 异步处理服务
  - qos-batch-task: 任务处理服务

### 6. 任务处理模块
- **qos-task**: 任务处理相关功能
  - qos-task-api: 接口定义
  - qos-task-executor: 执行器
  - qos-task-delay-async: 延迟异步服务
  - qos-task-delay-dispatch: 延迟调度服务
  - qos-task-monitor: 监控服务

### 7. 5GA处理模块
- **qos-5ga**: 5GA处理相关功能
  - qos-5ga-api: 接口定义
  - qos-5ga-async: 5GA异步服务
  - qos-5ga-task: 任务处理服务
  - qos-5ga-server: 服务实现


## 基础支撑模块

### 1. 公共模块
- **qos-common**: 公共组件和工具类

### 2. API模块
- **qos-api**: API服务
  - qos-api-admin: 管理网关服务
  - qos-api-b: B端对接网关服务
  - qos-api-user: 用户网关服务
  - qos-api-dedbearer: 专载网关服务
  - qos-api-defbearer: 默载网关服务
  - qos-api-general: 文件处理网关服务
  - qos-api-common: 通用网关服务
  

### 3. 其他辅助模块
- **qos-snapshot**: 快照服务
- **qos-idgen**: ID生成服务
- **qos-sms**: 短信服务
- **qos-mock**: 模拟服务



