---
description: 
globs: 
alwaysApply: true
---
# 工程结构规范

# 一、服务分层

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/BMyQAak5iBbjOjQy/7a29b6b3faca4bdf95e69f81b89ad4be0483.png)

|  **模块**  |  **包**  |  **职责**  |
| --- | --- | --- |
|  xxx-api  |  request、response、service  |  负责服务对外暴露的接口定义  |
|  xxx-server  |  starter  |  负责项目启动配置和业务入口，包括接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理等工作  |
|  |  application  |  负责用例实现，通常串联内部领域服务接口或外部服务接口，实现流程编排、聚合查询等工作流操作  |
|  |  domain  |  负责业务逻辑实现，通常抽象出领域对象，来表达业务概念，承载业务行为和规则。业务逻辑不复杂，可以没有  |
|  |  infrastructure  |  通常负责外部调用，比如数据库的CRUD、搜索引擎、文件系统、分布式服务的RPC等  |

# 二、包和对象模型定义

|  **模块**  |  **包**  |  |  **命名规范**  |  **职责说明**  |
| --- | --- | --- | --- | --- |
|  api  |  request  |  |  xxxReq  |  API接口模型中的请求参数对象  |
|  |  response  |  |  xxxResp  |  API接口模型中的返回结果对象  |
|  |  dto  |  |  xxxDTO  |  API接口模型中的传输对象  |
|  |  enums  |  |  xxxEnum  |  API中需要使用方感知的枚举对象  |
|  |  service  |  |  xxxService  |  服务接口  |
|  server  |  \--  |  \--  |  xxxApplication  |  启动器类  |
|  |  starter  |  service  |  xxxService  |  服务接口实现  |
|  |  |  job  |  xxxJob  |  定时任务  |
|  |  |  controller  |  xxxController  |  HTTP接口  |
|  |  |  consumer  |  xxxConsumer  |  Kafka的消费者  |
|  |  |  config  |  xxxConfig  |  服务配置信息  |
|  |  |  aop  |  xxxAop  |  切面类  |
|  |  application  |  model  |  xxxBO  |  可选，应用接口模型中的请求对象和返回对象  |
|  |  |  \--  |  xxxAppService  |  应用服务  |
|  |  domain  |  model  |  xxxDO  |  可选，领域对象，包括聚合根、实体和值对象  |
|  |  |  service  |  xxxDomainService  |  可选，领域服务  |
|  |  |  constant  |  xxxConstant  |  常量。如果没有domain，可放applicaiton包里  |
|  |  |  enums  |  xxxEnum  |  枚举。如果没有domain，可放applicaiton包里  |
|  |  infrastructure  |  dal  |  repo.xxxRepo mapper.xxxMapper po.xxxPO  |  数据访问层。PO是持久化对象，与数据库表对应。数据映射类命令后缀为Mapper。Repo用于对mapper进行封装，提供仓储服务  |
|  |  |  proxy  |  xxxProxy  |  外部服务代理封装  |
|  |  |  es  |  xxxEs  |  搜索  |
|  |  |  cache  |  xxxCache  |  对缓存客户端的封装  |
|  |  |  config  |  xxxConfig  |  业务配置  |
|  |  |  producer  |  xxxProducer  |  消息发布  |
|  |  exception  |  \--  |  xxxException  |  业务自定义异常  |
|  |  utils  |  \--  |  xxxUtil  |  工具类  |

*   k8s文件：置于项目内k8s目录
    
*   Docerfile文件：置于项目一级目录下
    

# 三、样例参考

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4EZlwA3BGbg9qxAY/img/01f6a8c0-ebe1-4216-98f8-2cf18b7c1fc6.png)