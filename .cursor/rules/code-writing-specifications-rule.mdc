---
description: 
globs: 
alwaysApply: true
---
# 代码编写规范

好的编码习惯，可以帮助我们 **1）快速定位问题；2）避免频繁修改**

定位难原因：代码看不懂；日志找不到

频繁改原因：程序出问题；需求老变更

所以，对我们的代码编写提出下述要求：

1.  代码的可读性，代码易懂逻辑清晰（命名规范、注解规范、工程结构）
    
2.  代码的可追踪，出问题有日志可查（日志打印）
    
3.  代码的健壮性，错误异常仍能运行（异常处理）
    
4.  代码的扩展性，需求变更轻松应对（方法规范）
    

## 一、命名规范

所有类、方法、参数、变量的命名，**做到望文知义**。名称应准确反映其用途和功能，避免有歧义、过短的名称，保持名称简洁而有足够的描述性

### 1.1 类和接口命名

1.  使用名词，采用大驼峰命名法。例如：`StudentService`、`ProductRepository`。
    
2.  避免使用缩写，除非是被广泛认可的缩写，如 `DTO`（Data Transfer Object）、`PO`（Persistent Object）。
    

### 1.2 方法命名

1.  采用小驼峰命名法，动词开头。例如：`getStudentById`、`saveProduct`。
    
2.  反映方法的行为，如 `find`、`update`、`delete`、`create`等。
    

**1.3 变量命名**

1.  小驼峰命名法。
    
2.  局部变量应简洁明了，反映其用途。例如：`count`、`name`。
    

### 1.4 常量命名

1.  全部大写，单词之间用下划线分隔。例如：`MAX_COUNT`、`DEFAULT_VALUE`。
    

### 1.5 参数命名

1.  与变量命名规则相同，清晰表达参数的含义。
    
2.  避免使用过于通用的名称，如 `arg`、`param`。
    

### 1.6 数组命名

1.  采用复数形式。例如：`students`、`products`。
    

### 1.7 异常枚举命名

1.  以`Exception`、`Enum`结尾。例如：`ResourceNotFoundException`、 `ExceptionCodeEnum`。
    

### 1.8 包命名

1.  基于域名的反序，例如公司域名是`example.com`，包名可以是`com.example.project.module`。
    
2.  反映包的功能层次，如`com.example.util`（工具类）、`com.example.controller`（控制器类）。
    

### 1.9 集合命名

1.  列表：以 `list` 结尾，例如：`customerList` 。
    
2.  集合：以 `set` 结尾，例如：`productSet` 。
    
3.  映射：以 `map` 结尾，例如：`userMap` 
    

### 1.10 命名的一致性

在整个项目中保持命名的一致性，遵循相同的规则和风格：

各层命名规约：

A）**Service / DAO 层方法命名规约**：

1）获取单个对象的方法用 get 做前缀。

2）获取多个对象的方法用 list 做前缀，复数结尾，如：listObjects。

3）获取统计值的方法用 count 做前缀。

4）插入的方法用 save / insert 做前缀。

5）删除的方法用 remove / delete 做前缀。

6）修改的方法用 update 做前缀。

B）**领域模型命名规约**：

1）展示对象：xxxVO，xxx 一般为网页名称。

> 如果展示对象和数据传输对象字段一致，可以直接使用 xxxDTO 数据传输对象

2）数据传输对象：xxxDTO，xxx 为业务领域相关的名称。

3）应用服务对象：xxxBO，xxx 为业务应用服务相关的名称。

4）领域对象：xxxDO，xxx 为业务领域相关的名称。

5）数据持久化对象：xxxPO，xxx 即为数据表名。

6）POJO 是 VO / DTO / BO / DO / PO 的统称，禁止命名成 xxxPOJO。

详细参考：

## 二、注释规范

### 2.1 总体原则

1.  注释应简洁明了，避免冗长和不必要的描述。
    
2.  保持注释的准确性和及时性，当代码修改时，相应的注释也要更新。
    
3.  避免注释与代码不一致的情况，以免造成混淆。
    
4.  只注释关键和复杂的部分，显而易见的代码不要注释。
    

### 2.2 类注释

1.  位于类定义的上方，使用 `/** */` 格式的多行注释。
    
2.  包含类的简要描述、作者、创建日期信息 
    

```java
/**
 * 取号服务
 *
 * <AUTHOR>
 * @date 2024-5-24 9:53
 */
@Slf4j
@Component
public class MobileProxy {
  // 类的具体实现
}
```

### 2.3 方法注释

1.  位于方法定义的上方，使用 `/** */` 格式的多行注释。
    
2.  描述方法的功能、参数（可选）、返回值（可选）和可能抛出的异常。
    
3.  **接口、抽象方法要求有注释**，逻辑复杂的普通方法建议注释。
    

```java
/**
 * 获取号卡基础信息
 *
 * @param userInfoReq 用户请求参数
 * @return 用户基础信息
 */
Resp<UserInfoDTO> queryUserInfo(UserInfoReq userInfoReq);
```

### 2.4 行内注释

1.  使用 `//` 进行注释，用于解释某一行或一小段代码的作用。
    

```java
// 增加查询老系统的订单，新系统如果已经有数据了，则不用查询老系统
List pccOrders = pccService.getOrders(msisdn);
```

## 三、工程规范

具体参考：.cursor/rules/structure-specification-rule.mdc

【强制】：网关 api controller 接口定义不要使用内部服务的接口模型，需要单独定义 

## 四、日志打印

日志的主要作用：1）调试排查问题；2）统计用户行为；3）监控和告警；4）撕逼和甩锅利器

要求：对程序运行情况能够记录和监控，必要时可了解程序内部的运行状态

### 4.1 日志级别的使用

1.  `ERROR`：用于记录严重的错误，通常表示**程序错误影响到实际业务，需要人工介入**的情况。
    
2.  `WARN`：用于记录不太严重但仍需要关注的情况，可能表示潜在的问题或异常。
    
3.  `INFO`：用于记录重要的业务流程和关键操作的信息。
    
4.  `DEBUG`：用于在开发和调试阶段记录详细的调试信息，在生产环境中通常应关闭。
    

### 4.2 日志内容

1.  包含足够的上下文信息，如相关的类名、方法名、输入参数和关键的中间结果。
    
2.  对于异常，记录完整的异常堆栈信息。
    
3.  避免无效、重复日志输出。
    

### 4.3  日志打印注意事项

1、核心方法要有日志记录，方便问题排查

```java
public void consumeMessage(ConsumerRecord<String, String> message) {
  log.info("接收消息成功，开始处理，消息内容:{}", message.value());
   // 业务逻辑
}
```

2、核心逻辑遇到if...else...等条件时，首行尽量打印日志，方便排查问题（注意记录重要参数，出问题时知道进了哪个分支）**；**

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/r4mlQgwzR3ABqxow/img/54cdd890-2273-4cec-9d37-5e76041177ab.jpg)

3、使用占位符，而不是+号进行字符串拼接，提高可读性，减少性能损耗；

```java
// 使用了大括号{}来作为日志中的占位符，比使用+操作符，更加优雅简洁
logger.info("Processing trade with id: {} and symbol : {} ", id, symbol); 

// 使用+操作符进行字符串的拼接，有一定的性能损耗
logger.info("Processing trade with id: " + id + " and symbol: " + symbol);

```

4、异常日志不要打一半，要输出全部错误信息；

```java
try {
    //业务代码处理
} catch (Exception e) {
    // 异常e都没有打印出来，压根不知道出了什么类型的异常
    log.error('你的程序有异常啦');

    //不会记录详细的堆栈异常信息，只会记录错误基本描述信息，不利于排查问题
    log.error('你的程序有异常啦', e.getMessage());

    // 正确方式
    log.error('你的程序有异常啦', e);
}
```

5、不要既打印异常，又抛出异常，这样会重复输出日志

```java
// 通常会把栈信息打印两次，因为捕获了MyException异常的地方，还会再打印一次
log.error("IO exception", e);
throw new MyException(e);
```

6、避免打印日志造成业务流程阻断，比如：info.getNumber()

```java
//如果info为null，此时会报错，阻断业务流程
log.info("info number={} ", info.getNumber());
```

7、建议核心数据写操作成功后，打上日志。（生产上出错大部分都是数据问题）

## 五、异常处理

### 5.1 总体原则

1.  异常处理应该是为了增强程序的健壮性和可维护性，而不是隐藏问题；
    
2.  只处理能够被有效解决的异常，避免无意义的捕获和处理；
    
3.  尽早抛出异常，尽晚捕获异常。
    

### 5.2 捕获具体异常类型

1.  优先捕获特定的异常子类和自定义异常，如 `FileNotFoundException` 、 `UserServiceException` 等，而不是直接捕获 `Exception` 。
    
2.  对可能抛出多种异常的代码块，分别捕获并处理每种异常，避免将不同类型的异常混在一起处理。
    
3.  自定义异常必须记录`message`
    

### 5.3 异常处理的层次

1.  绝大部分场景，不允许捕获异常，都抛出去，到业务层处理，遵循早 throw，晚 catch
    
2.  底层模块的异常处理应尽量简单，专注于将异常传递给上层模块，上层模块根据业务逻辑进行更全面和统一的处理。
    

### 5.4 早抛出晚捕获

1.  如果当前方法无法完全处理异常，应使用 `throw` 关键字将异常向上抛出给调用者。
    
2.  在重新抛出异常时，可以包装原始异常为新的异常，以添加更多上下文信息，但不要丢失原始异常的堆栈跟踪。
    

```Java
public void wrapException(String input) throws UserServiceException {    
  try {
   // do something
  } catch (Exception e) {
    // 会丢失异常
    throw new UserServiceException("出错了");
    // 不推荐下面方式, 统一在业务层打印日志
    log.error("出错了"， e)；
    throw new UserServiceException("出错了");
     // 正确处理
    throw new UserServiceException("出错了"，e);
  }
}
```

### 5.5 统一错误码

1.  在某些情况下，可以结合使用错误码和异常。例如，对于一些可预期的、可以通过重试或其他方式恢复的错误；对于严重的、不可恢复的错误，抛出异常。
    
2.  确保错误码和异常的使用在整个项目中具有一致性和清晰的定义。
    

### 5.6  资源清理

1.  在 `try-catch` 块中，如果使用了需要手动释放的资源（如文件流、数据库连接、网络连接等），在 `finally` 块中确保资源被正确关闭和释放。
    
2.  使用 `try-with-resources` 语句来自动管理资源的释放，以简化代码并减少资源泄漏的风险。
    

### 5.7 异常处理的性能

1.  避免在异常处理中进行过于复杂或耗时的操作，以免影响程序的性能。
    
2.  尽量减少用try监控的代码块范围
    

### 5.8 日志记录

1.  在捕获异常时，详细记录异常的信息，包括异常的类型、消息、堆栈跟踪以及相关的上下文信息（如当前操作的对象、输入参数等）。
    
2.  使用合适的日志级别，严重的异常使用 `ERROR` 级别，不太严重但仍需关注的异常使用 `WARN` 级别。
    
3.  抛出异常的时候，不要打印日志，防止重复打印
    

### 5.9 不要忽略异常

1.  捕获异常后必须进行有意义的处理，如记录日志、进行错误恢复或向上抛出。
    
2.  严禁使用空的 `catch` 块来忽略异常，除非在特殊情况下经过谨慎评估并添加明确的注释说明
    

## 六、方法规范

方法编写指导原则是抽**象和封装**，适应可能的变化，记住代码是给人看的，编写出非开发人员能看懂的代码，这是我们的追求

### 6.1 方法签名

1.  方法名使用小驼峰命名法，动词开头，准确且简洁地描述方法的行为，如 `processData`、`validateInput` ；
    
2.  每个参数都要有明确的数据类型和有意义的参数名，例如 `int age, String name` ；
    
3.  不要出现和业务无关的参数，方法只需要一个 userId，不要直接传 User 实体；
    
4.  避免使用Map，Json这些复杂对象作为参数和结果；
    
5.  参数个数尽量不超过 5 个，如果参数过多，考虑封装成一个对象。
    

### 6.2 方法长度

1.  **理想情况下，方法的长度应控制在 20 - 50 行代码，行数不超过 100 ，宽度不超过 120。**
    
2.  对于复杂的逻辑，可以分解为多个私有辅助方法，每个辅助方法专注于一个特定的子任务。
    

### 6.3 方法逻辑

1.  遵循单一职责原则，一个方法只做一件事情，并把它做好。
    
2.  避免过度嵌套的控制结构，如超过 3 层的 `if-else` 或嵌套的循环。
    
3.  保持逻辑清晰，使用临时变量来存储中间结果，提高代码的可读性。
    
4.  合理的利用换行符，防止大片代码放一起，增加理解成本（同理，每个类字段中间需要换行）。
    

### 6.4 返回值

1.  明确方法的返回值类型，确保返回值与方法的预期功能一致。
    
2.  对于返回集合或数组的方法，确保在返回前不为 `null` ，如果没有数据，返回空集合或数组。
    
3.  如果方法可能返回 `null`，最好注释说明，并在调用方进行适当的空值检查。
    

### 6.5 方法可见性

合理设置方法的可见性，遵循最小暴露原则，遵循 static，public，protect，包内可见，private 顺序排列

1.  `public` 方法：用于提供给外部类或模块调用的重要功能。
    
2.  `protected` 方法：用于被子类继承和重写的方法。
    
3.  `private` 方法：仅在当前类内部使用的辅助方法。
    

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/r4mlQgwzR3ABqxow/img/29564b89-05fe-4d4d-a55a-29106d480528.png)

## 七、特殊规范

此规范主要针对SLICE-QOS项目

1.   controller 校验统一使用jakarta.validation相关方法