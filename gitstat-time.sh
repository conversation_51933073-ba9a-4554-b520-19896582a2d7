#!/bin/bash
# 统计master分支代码行数（按提交时间统计）
# 执行前，运行 git fetch 命令，确保 origin/master 和 origin/release 分支代码最新

if [ $# -gt 1 ]; then
  echo "【ERROR】参数错误，请重新输入: [<before-day>], eg: 2024-07-26"
  exit 1
fi

before_day=$1

function cal_before_day() {
    # 获取当前年月日
    current_year=$(date +%Y)
    current_month=$(date +%m)
    current_day=$(date +%d)

    # 移除前导的0（如果有的话）
    current_month="${current_month#0}"

    # 如果在当月26日之前，那么仍在上一个周期，减去一个月份
    # 注意：如果当前月份是1月，需要调整为上一年的12月
    if [ $current_day -lt 26 ]; then
        ((current_month--))
    fi

    if [ $current_month -lt 1 ]; then
        ((current_month += 12))
        ((current_year--))
    fi

    if [ $current_month -lt 10 ]; then
        current_month="0$current_month"
    fi

    echo $current_year-$current_month-26
}

echo -----------------------------------------------------------------------

# 按执行时间查找前一个周期（比如6.26～7.25）
if [ -z "$before_day" ]; then
  before_day=$(cal_before_day)
fi

before_time=$before_day" 00:00:00"
current_time=$(date "+%Y-%m-%d %H:%M:%S")

echo "统计周期: $before_time ～ $current_time"
echo -----------------------------------------------------------------------

authors=("欧龙" "彭博" "喻星" "易思绍" "杨仕祥")

for name in "${authors[@]}"; do
    echo -en "$name\t";
    git log --author="$name" --pretty=tformat: --after="$before_time" --before="$current_time" --numstat | \
    awk '
    $1 - $2 > -100 && $1 - $2 < 10000 { cnt +=1; add += $1; subs += $2; loc += $1 - $2; }
    END { printf "commit cnt: %d. added lines: %d, removed lines: %d, total lines: %d\n", cnt, add, subs, loc;}
    '
done

echo -----------------------------------------------------------------------
git log --pretty=tformat: --after="$before_time" --before="$current_time" --numstat | \
awk '
$1 - $2 > -100 && $1 - $2 < 10000 { total += $1 - $2; }
END { printf "total lines: %d\n", total; }
'

echo -----------------------------------------------------------------------

# windows环境，退出兼容
# read -p "press enter to end"