package com.chinaunicom.qos.test.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 测试配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api")
public class TestConfig {
    
    private String baseUrl;
    
    /**
     * 认证配置
     */
    private Auth auth = new Auth();
    
    @Data
    public static class Auth {
        /**
         * 默认测试用户名
         */
        private String username;
        
        /**
         * 默认测试密码
         */
        private String password;
        
        /**
         * 是否记住登录
         */
        private boolean remember = false;
        
        /**
         * Token过期时间（毫秒）
         */
        private long tokenExpireTime = 24 * 60 * 60 * 1000L; // 24小时
    }
} 