package com.chinaunicom.qos.test.utils;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * REST API测试工具类
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class RestAssuredUtils {

    @Getter
    private static String baseUrl;
    
    private static String token;

    /**
     * 设置基础URL
     */
    public static void setBaseUrl(String url) {
        baseUrl = url;
        RestAssured.baseURI = url;
        log.info("设置基础URL: {}", url);
    }

    /**
     * 设置认证令牌
     */
    public static void setToken(String authToken) {
        token = authToken;
        log.info("设置认证Token: {}", authToken != null ? "已设置" : "已清空");
    }

    /**
     * 获取当前Token
     */
    public static String getToken() {
        return token;
    }

    /**
     * 清空Token
     */
    public static void clearToken() {
        token = null;
        log.info("已清空认证Token");
    }

    /**
     * 创建基础请求规范
     */
    private static RequestSpecification getBaseSpec() {
        return RestAssured.given()
                .contentType(ContentType.JSON)
                .accept(ContentType.JSON);
    }

    /**
     * 创建带认证的请求规范
     */
    private static RequestSpecification getAuthSpec() {
        RequestSpecification spec = getBaseSpec();
        if (token != null && !token.isEmpty()) {
            // 支持两种token传递方式
            spec.header("token", token)
                .header("Authorization", "Bearer " + token);
        }
        return spec;
    }

    /**
     * 执行GET请求
     */
    public static Response get(String path) {
        return get(path, null, false);
    }

    /**
     * 执行带认证的GET请求
     */
    public static Response getWithAuth(String path) {
        return get(path, null, true);
    }

    /**
     * 执行带参数的GET请求
     */
    public static Response get(String path, Map<String, ?> queryParams, boolean withAuth) {
        RequestSpecification spec = withAuth ? getAuthSpec() : getBaseSpec();

        if (queryParams != null) {
            spec.queryParams(queryParams);
        }

        Response response = spec.get(path);
        logResponse(response);
        return response;
    }

    /**
     * 执行POST请求
     */
    public static Response post(String path, Object body) {
        return post(path, body, false);
    }

    /**
     * 执行带认证的POST请求
     */
    public static Response postWithAuth(String path, Object body) {
        return post(path, body, true);
    }

    /**
     * 执行POST请求
     */
    public static Response post(String path, Object body, boolean withAuth) {
        RequestSpecification spec = withAuth ? getAuthSpec() : getBaseSpec();

        if (body != null) {
            spec.body(body);
        }

        Response response = spec.post(path);
        logResponse(response);
        return response;
    }

    /**
     * 执行PUT请求
     */
    public static Response put(String path, Object body, boolean withAuth) {
        RequestSpecification spec = withAuth ? getAuthSpec() : getBaseSpec();

        if (body != null) {
            spec.body(body);
        }

        Response response = spec.put(path);
        logResponse(response);
        return response;
    }

    /**
     * 执行DELETE请求
     */
    public static Response delete(String path, boolean withAuth) {
        RequestSpecification spec = withAuth ? getAuthSpec() : getBaseSpec();
        Response response = spec.delete(path);
        logResponse(response);
        return response;
    }

    /**
     * 记录响应信息
     */
    private static void logResponse(Response response) {
        log.info("Response Status Code: {}", response.getStatusCode());
        log.debug("Response Body: {}", response.getBody().asString());
    }
}