spring:
  config:
    activate:
      on-profile: test
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ***********************************************************************************
#    username: slice_dynamic
#    password: krreA?Kp3eF3EQr8

# 测试环境配置
api:
  base-url: http://**************:58008
  # 可以根据需要配置不同环境的URL
  # dev-url: http://dev-api.example.com
  # staging-url: http://staging-api.example.com
  
  # 认证配置
  auth:
    username: slice-dev  # 默认测试用户名
    password: '{qoscipher}e5f32023918ebd2061a5f0b97d7e9a30206dc7ddcb1ff24e2a2d5cd9f00805d5'  # 默认测试密码
    remember: false  # 是否记住登录
    token-expire-time: 86400000  # Token过期时间（毫秒），24小时
  
# Dubbo配置
dubbo:
  registry:
    address: nacos://**************:38848
    username: ${nacos.username}
    password: ${nacos.password}
  protocol:
    name: dubbo
    port: -1
  application:
    name: qos-test
    qos-enable: false

nacos:
  username: slice-qos
  password: '{qoscipher}3df53086db1edbb3a5a50e956b10b8821f6dbbe663ddee049433c5f105044f19'

ip:
  target: ********