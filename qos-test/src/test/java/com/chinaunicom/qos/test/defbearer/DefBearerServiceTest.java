package com.chinaunicom.qos.test.defbearer;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.defbearer.api.dto.AddDefBearerDTO;
import com.chinaunicom.qos.defbearer.api.request.AddDefBearerReq;
import com.chinaunicom.qos.defbearer.api.request.TerminateDefBearerByMsisdnReq;
import com.chinaunicom.qos.defbearer.api.request.TerminateDefBearerReq;
import com.chinaunicom.qos.defbearer.api.service.DefBearerService;
import com.chinaunicom.qos.test.base.BaseQosTest;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.Date;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Epic("默载服务模块")
@SpringBootTest
public class DefBearerServiceTest extends BaseQosTest {
    @DubboReference
    private DefBearerService defbearerService;

    private Long orderId;
    private static final String MSISDN = "18510108719";
    private static final String CM_SERVICE_ID = "3211023630";
    private static final String ORDER_SOURCE = "qos-test";

    @BeforeMethod
    public void beforeEach() {
        AddDefBearerReq req = new AddDefBearerReq();
        req.setMsisdn(MSISDN);
        req.setMessageId("MSG-testAdd");
        req.setOrderSource(ORDER_SOURCE);
        req.setQosProductId(880081);
        req.setCmServiceId(CM_SERVICE_ID);
        req.setUserEndTime(DateUtils.formatDate(DateUtils.computeDate(new Date(), 60)));
        Resp<AddDefBearerDTO> resp = defbearerService.add(req);
        orderId = resp.getData().getOrderId();
    }

    @Test
    @Feature("默载按手机号终止")
    @Story("根据手机号终止默载")
    @Description("测试根据手机号终止默载的功能")
    public void testTerminateByMsisdn() {
        TerminateDefBearerByMsisdnReq req = new TerminateDefBearerByMsisdnReq();
        req.setMsisdn(MSISDN);
        req.setCmServiceId(CM_SERVICE_ID);
        req.setMessageId("MSG-testTerminateByMsisdn");
        req.setOrderSource(ORDER_SOURCE);
        Resp<Void> resp = defbearerService.terminateByMsisdn(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }

    @Test
    @Feature("默载系统终止")
    @Story("系统终止默载")
    @Description("测试默载系统终止功能")
    public void testTerminateBySystem() {
        TerminateDefBearerReq req = new TerminateDefBearerReq();
        req.setMsisdn(MSISDN);
        req.setOrderId(orderId);
        req.setMessageId("MSG-testTerminateBySystem");
        req.setOrderSource(ORDER_SOURCE);
        Resp<Void> resp = defbearerService.terminateBySystem(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }
}
