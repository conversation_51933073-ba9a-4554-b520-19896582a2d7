package com.chinaunicom.qos.test.base;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.chinaunicom.qos.test.config.TestConfig;
import com.chinaunicom.qos.test.utils.RestAssuredUtils;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;

import java.util.Date;

/**
 * QoS测试基类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public abstract class BaseQosTest extends AbstractTestNGSpringContextTests {

    @Autowired
    protected TestConfig testConfig;

    /**
     * 在所有测试执行前的初始化
     */
    @BeforeClass
    public void setUp() {
        log.info("开始初始化测试环境");
        
        // 配置RestAssured
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());

        // 设置基础URL
        RestAssuredUtils.setBaseUrl(testConfig.getBaseUrl());

        // 执行登录并获取token
        String token = performLogin();
        RestAssuredUtils.setToken(token);
        
        log.info("测试环境初始化完成");
    }

    /**
     * 每个测试方法执行前的准备
     */
    @BeforeMethod
    public void beforeEach() {
        // 每个测试执行前的准备工作
    }

    /**
     * 执行登录获取token
     */
    protected String performLogin() {
        log.info("开始执行登录，用户名: {}", testConfig.getAuth().getUsername());
        final long expireTime =  24 * 60 * 60 * 1000L;
        Date date = new Date(System.currentTimeMillis() + expireTime);
        Algorithm algorithm = Algorithm.HMAC256(testConfig.getAuth().getPassword());
        String systemIdKey = "systemId";
        return JWT.create()
                .withClaim(systemIdKey, testConfig.getAuth().getUsername())
                .withExpiresAt(date)
                .sign(algorithm);
    }
} 