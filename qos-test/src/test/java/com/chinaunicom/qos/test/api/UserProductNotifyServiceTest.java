package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.user.api.dto.ProductDTO;
import com.chinaunicom.qos.user.api.request.ReplaceNotifyReq;
import com.chinaunicom.qos.user.api.service.UserProductNotifyService;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.annotations.Test;

import java.util.Date;
import java.util.List;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Epic("产品订购通知")
@SpringBootTest
public class UserProductNotifyServiceTest extends BaseQosTest {
    @DubboReference
    private UserProductNotifyService userProductNotifyService;


    @Test
    @Feature("产品退订替换通知")
    @Story("产品被互斥替换时通知北向平台")
    @Description("测试产品退订替换通知功能")
    public void testTerminateByMsisdn() {
        ProductDTO highLevelProduct = new ProductDTO();
        highLevelProduct.setQosProductId(880084);
        highLevelProduct.setProductName("守护宝-3元包月自动续订");
        ProductDTO lowLevelProduct = new ProductDTO();
        lowLevelProduct.setQosProductId(880083);
        lowLevelProduct.setProductName("省分流量经营-广东");

        ReplaceNotifyReq req = new ReplaceNotifyReq();
        req.setMsisdn("18510108719");
        req.setExpireTime(new Date());
        req.setHighLeveProduct(highLevelProduct);
        req.setProductPOList(List.of(lowLevelProduct));
        Resp<Void> resp = userProductNotifyService.replaceNotify(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }
}
