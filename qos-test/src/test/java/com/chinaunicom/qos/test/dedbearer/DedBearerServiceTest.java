package com.chinaunicom.qos.test.dedbearer;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.dedbearer.api.dto.AddDedBearerDTO;
import com.chinaunicom.qos.dedbearer.api.dto.DedBearerOrderDTO;
import com.chinaunicom.qos.dedbearer.api.request.AddDedBearerReq;
import com.chinaunicom.qos.dedbearer.api.request.QueryDedBearerReq;
import com.chinaunicom.qos.dedbearer.api.request.TerminateDedBearerReq;
import com.chinaunicom.qos.dedbearer.api.service.DedBearerService;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.user.api.dto.UserPrivateIpInfoDTO;
import com.chinaunicom.qos.user.api.service.UserInfoService;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.List;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Epic("专载服务模块")
@SpringBootTest
public class DedBearerServiceTest extends BaseQosTest {
    @Value("${ip.target:********}")
    private String targetIp;
    @DubboReference
    private DedBearerService dedbearerService;
    @DubboReference
    private UserInfoService userInfoService;

    private Long orderId;
    private static final String MSISDN = "18510108719";

    @BeforeMethod
    public void beforeEach() {

        Resp<List<UserPrivateIpInfoDTO>> userInfoResp = userInfoService.listUserPrivateIpInfo(List.of(MSISDN));


        AddDedBearerReq req = new AddDedBearerReq();
        req.setMsisdn(MSISDN);
        req.setMessageId("MSG-testAdd");
        req.setOrderSource("qos-test");
        req.setQosProductId(880085);
        req.setCmServiceId("3211023863");
        req.setPrivateIp(userInfoResp.getData().getFirst().getUserIp());
        req.setTargetIps(List.of(targetIp));
        req.setDuration(60);
        Resp<AddDedBearerDTO> resp = dedbearerService.add(req);
        orderId = resp.getData().getOrderId();
    }


    @Test
    @Feature("查询有效的专载订单")
    @Story("根据手机号查询有效的专载订单")
    @Description("测试查询有效的专载订单的功能")
    public void testTerminateByMsisdn() {
        QueryDedBearerReq req = new QueryDedBearerReq();
        req.setMsisdn(MSISDN);
        Resp<List<DedBearerOrderDTO>> resp = dedbearerService.queryValidOrders(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }

    @Test
    @Feature("专载系统终止")
    @Story("系统终止专载")
    @Description("测试专载系统终止功能")
    public void testTerminateBySystem() {
        TerminateDedBearerReq req = new TerminateDedBearerReq();
        req.setOrderId(orderId);
        req.setMessageId("MSG-testTerminateBySystem");
        req.setOrderSource("qos-test");
        Resp<Void> resp = dedbearerService.terminateBySystem(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }
}
