package com.chinaunicom.qos.test.dedbearer;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.dedbearer.api.request.EventNotifyReq;
import com.chinaunicom.qos.dedbearer.api.service.EventHandleService;
import com.chinaunicom.qos.test.base.BaseQosTest;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.annotations.Test;

import static com.chinaunicom.qos.dedbearer.api.enums.EventTypeEnum.NET_BREAK;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Epic("事件上报")
@SpringBootTest
public class EventHandleServiceTest extends BaseQosTest {
    @DubboReference
    private EventHandleService eventHandleService;


    @Test
    @Feature("事件上报")
    @Story("网元上报会话事件")
    @Description("测试事件上报功能")
    public void testTerminateByMsisdn() {
        EventNotifyReq req = new EventNotifyReq();
        req.setCorrelationId("1231234444");
        req.setEventType(NET_BREAK);
        Resp<Void> resp = eventHandleService.eventNotify(req);
        // 验证结果
        assertNotNull(resp, "响应不应为空");
        assertEquals(resp.getCode(), 0, "状态码应为0表示成功");
    }
}
