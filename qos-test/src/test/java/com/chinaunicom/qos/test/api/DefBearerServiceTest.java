package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.test.utils.RestAssuredUtils;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.testng.annotations.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.testng.Assert.assertEquals;

@Epic("默载网关")
@SpringBootTest
public class DefBearerServiceTest extends BaseQosTest {
    private static final String DEF_BEARER_ENDPOINT = "/qos/defbearer";

    private static final String VALID_CM_SERVICE_ID = "3211023630";
    
    private static final String KEY_PRODUCTID = "qosProductId";
    private static final String KEY_MESSAGE_ID = "messageId";
    private static final String KEY_CM_SERVICE_ID = "cmServiceId";

    private static final String HTTP_RESULT_OK = "状态码应为200";
    private static final String CODE_RESULT_OK = "状态码应为0表示成功";


    @Test
    @Feature("默载申请版本1.1")
    @Story("按结束时间申请默载")
    @Description("测试按结束时间申请默载功能")
    public void testAddDefBearerByEndTime() {
        Map<String, Object> params = new HashMap<>();
        params.put("msisdn", "18510108719");
        params.put(KEY_MESSAGE_ID, "testAddDefBearerByEndTime");
        params.put(KEY_PRODUCTID, 880081);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put("userEndTime", DateUtils.formatDate(DateUtils.computeDate(new Date(), 60)));
        Response response = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + "/add/1.1", params);
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载按手机号终止")
    @Story("默载按手机号终止")
    @Description("测试默载按手机号终止功能")
    public void testTerminateByMsisdn() {
        Map<String, Object> params = new HashMap<>();
        params.put("msisdn", "18510108719");
        params.put(KEY_MESSAGE_ID, "testAddDefBearerByMsisdn");
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        Response response = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + "/terminateByMsisdn/1.0", params);
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载批量申请版本1.1")
    @Story("按结束时间批量申请默载")
    @Description("测试按结束时间批量申请默载功能")
    public void testAddByOrderEndTime() {
        Map<String, Object> params = new HashMap<>();
        params.put("msisdnFilePath", "/qos/upload/22217232757535010000049237.csv");
        params.put(KEY_MESSAGE_ID, "testAddDefBearerByEndTime");
        params.put(KEY_PRODUCTID, 880081);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put("orderEndTime", DateUtils.formatDate(DateUtils.computeDate(new Date(), 60)));
        params.put("conditions", Map.of("expectStartTime", DateUtils.formatDate((new Date())),"homeProvince",11));
        params.put("subscribeInfo", Map.of(KEY_PRODUCTID, 880081));
        Response response = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + "/batch-apply/1.1", params);
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }
}