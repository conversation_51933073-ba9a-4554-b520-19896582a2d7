package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.batch.api.request.DefBearerBatchAddNotifyReq;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchAddPlanNotifyReq;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchTerminateNotifyReq;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchTerminatePlanNotifyReq;
import com.chinaunicom.qos.batch.api.service.DefBearerBatchNotifyService;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.test.base.BaseQosTest;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.test.context.SpringBootTest;
import org.testng.annotations.Test;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Epic("默载批量通知")
@SpringBootTest
public class DefBearerBatchNotifyServiceTest extends BaseQosTest {
    @DubboReference
    private DefBearerBatchNotifyService defBearerBatchNotifyService;

    private static final String ORIGIN_REQ_SOURCE = "20250318102851au";

    private static final String RESULT_NOT_NULL = "响应不应为空";
    private static final String RESULT_CODE_OK = "状态码应为0表示成功";

    @Test
    @Feature("默载批量申请计划通知")
    @Story("把默载批量申请计划通知给调用方")
    @Description("测试默载批量申请计划通知功能")
    public void testAddPlanNotify() {
        DefBearerBatchAddPlanNotifyReq req = new DefBearerBatchAddPlanNotifyReq();
        req.setPlanId(1L);
        req.setCode(0);
        req.setOriginReqSource(ORIGIN_REQ_SOURCE);
        Resp<Void> resp = defBearerBatchNotifyService.addPlanNotify(req);
        // 验证结果
        assertNotNull(resp, RESULT_NOT_NULL);
        assertEquals(resp.getCode(), 0, RESULT_CODE_OK);
    }

    @Test
    @Feature("默载批量申请结果通知")
    @Story("把默载批量申请结果通知给调用方")
    @Description("测试默载批量申请结果通知功能")
    public void testAddResultNotify() {
        DefBearerBatchAddNotifyReq req = new DefBearerBatchAddNotifyReq();
        req.setPlanId(1L);
        req.setCode(0);
        req.setOriginReqSource(ORIGIN_REQ_SOURCE);
        Resp<Void> resp = defBearerBatchNotifyService.addResultNotify(req);
        // 验证结果
        assertNotNull(resp, RESULT_NOT_NULL);
        assertEquals(resp.getCode(), 0, RESULT_CODE_OK);
    }

    @Test
    @Feature("默载批量终止计划通知")
    @Story("把默载批量终止计划通知给调用方")
    @Description("测试默载批量终止计划通知功能")
    public void testTerminatePlanNotify() {
        DefBearerBatchTerminatePlanNotifyReq req = new DefBearerBatchTerminatePlanNotifyReq();
        req.setPlanId(1L);
        req.setCode(0);
        req.setOriginReqSource(ORIGIN_REQ_SOURCE);
        Resp<Void> resp = defBearerBatchNotifyService.terminatePlanNotify(req);
        // 验证结果
        assertNotNull(resp, RESULT_NOT_NULL);
        assertEquals(resp.getCode(), 0, RESULT_CODE_OK);
    }

    @Test
    @Feature("默载批量终止结果通知")
    @Story("把默载批量终止结果通知给调用方")
    @Description("测试默载批量终止结果通知功能")
    public void testTerminateResultNotify() {
        DefBearerBatchTerminateNotifyReq req = new DefBearerBatchTerminateNotifyReq();
        req.setPlanId(1L);
        req.setCode(0);
        req.setOriginReqSource(ORIGIN_REQ_SOURCE);
        Resp<Void> resp = defBearerBatchNotifyService.terminateResultNotify(req);
        // 验证结果
        assertNotNull(resp, RESULT_NOT_NULL);
        assertEquals(resp.getCode(), 0, RESULT_CODE_OK);
    }
}
