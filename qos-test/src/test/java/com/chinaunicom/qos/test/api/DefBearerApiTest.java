package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.test.utils.RestAssuredUtils;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.AfterClass;
import org.testng.annotations.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.testng.Assert.assertEquals;

/**
 * 默载服务API测试类
 */
@Slf4j
@Epic("默载服务模块")
@SpringBootTest
public class DefBearerApiTest extends BaseQosTest {
    
    private static final String DEF_BEARER_ENDPOINT = "/qos/defbearer";
    private static final String PRODUCT_SUBSCRIPTION_ENDPOINT = "/qos/user/product";
    private static final String DEF_ADD_ENDPOINT = "/add/1.0";
    private static final String DEF_BATCH_APPLY_ENDPOINT = "/batch-apply/1.0";

    // 测试数据常量
    private static final String UNICOM_MSISDN = "13012345678";
    private static final Integer VALID_QOS_PRODUCT_ID = 880081;
    private static final Integer INVALID_QOS_PRODUCT_ID = 999999;
    private static final String VALID_CM_SERVICE_ID = "3211023630";
    private static final String INVALID_CM_SERVICE_ID = "9999999999";
    private static final String VALID_FILE_PATH = "/update/" + "test.csv";
    private static final String INVALID_FILE_PATH = "nonexistent_file.csv";
    private static final Integer VALID_HOME_PROVINCE = 11;
    private static final Integer INVALID_HOME_PROVINCE = 999;

    private static final String KEY_MSISDN = "msisdn";
    private static final String KEY_PRODUCTID = "qosProductId";
    private static final String KEY_MESSAGE_ID = "messageId";
    private static final String KEY_CM_SERVICE_ID = "cmServiceId";
    private static final String KEY_MSISDN_FILE = "msisdnFilePath";
    private static final String KEY_DURATION = "duration";
    private static final String KEY_EXPECT_TIME = "expectStartTime";
    private static final String KEY_HOME_PROVINCE = "homeProvince";
    private static final String KEY_CONDITIONS = "conditions";
    private static final String KEY_SUBSCRIBE = "subscribeInfo";

    private static final String HTTP_RESULT_OK = "状态码应为200";
    private static final String CODE_RESULT_OK = "接口返回码应为0表示成功";

    /**
     * 测试类启动前的准备工作：订购产品
     */
    @BeforeClass
    public void setupProductSubscription() {
        log.info("开始为默载测试准备产品订购数据");
        
        try {
            // 先清理可能存在的旧数据
            cleanupProductSubscription();
            
            // 订购测试所需的产品
            subscribeTestProduct();
            
            log.info("产品订购准备完成");
        } catch (Exception e) {
            log.error("产品订购准备失败", e);
        }
    }

    /**
     * 测试类结束后的清理工作：退订产品
     */
    @AfterClass
    public void cleanupProductSubscription() {
        log.info("开始清理默载测试的产品订购数据");
        
        try {
            // 退订测试产品
            unsubscribeTestProduct();
            
            log.info("产品订购数据清理完成");
        } catch (Exception e) {
            log.warn("产品订购数据清理失败", e);
        }
    }

    /**
     * 订购测试所需的产品
     */
    private void subscribeTestProduct() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_MESSAGE_ID, "defbearer_test_setup_" + System.currentTimeMillis());
        params.put("effectiveTime", DateUtils.formatDate(new Date()));
        params.put("expireTime", DateUtils.formatDate(DateUtils.computeDate(new Date(), 600)));
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + "/subscribe/1.0", params);
        
        log.info("产品订购结果 - 状态码: {}, 返回码: {}, 手机号: {}, 产品ID: {}", 
                response.getStatusCode(), 
                response.jsonPath().getInt("code"), 
                UNICOM_MSISDN, 
                VALID_QOS_PRODUCT_ID);
        
        // 如果返回码不是0（成功），记录详细信息但不抛异常，因为可能已经订购过了
        if (response.jsonPath().getInt("code") != 0) {
            log.warn("产品订购返回非成功码: {}, 消息: {}", 
                    response.jsonPath().getInt("code"), 
                    response.jsonPath().getString("message"));
        }
    }

    /**
     * 退订测试产品
     */
    private void unsubscribeTestProduct() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_MESSAGE_ID, "defbearer_test_cleanup_" + System.currentTimeMillis());
        params.put("expireTime", DateUtils.formatDate(new Date()));
        
        try {
            Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + "/unsubscribe/1.0", params);
            
            log.info("产品退订结果 - 状态码: {}, 返回码: {}, 手机号: {}, 产品ID: {}", 
                    response.getStatusCode(), 
                    response.jsonPath().getInt("code"), 
                    UNICOM_MSISDN, 
                    VALID_QOS_PRODUCT_ID);
        } catch (Exception e) {
            log.warn("产品退订失败", e);
        }
    }

    @Test
    @Feature("默载申请")
    @Story("用例编号010")
    @Description("验证用户默载申请成功")
    public void testDefBearerApplySuccess() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MESSAGE_ID, "testDefBearerApply_" + System.currentTimeMillis());
        params.put(KEY_DURATION, 30);

        Response response = getResponse(DEF_ADD_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载申请")
    @Story("用例编号011")
    @Description("验证用户申请未订购的产品")
    public void testApplyUnsubscribedProduct() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, INVALID_QOS_PRODUCT_ID);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MESSAGE_ID, "testUnsubscribedProduct_" + System.currentTimeMillis());
        params.put(KEY_DURATION, 60);

        Response response = getResponse(DEF_ADD_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 110000, "接口返回码应为110000表示未查询到用户的产品订购信息");
    }

    @Test
    @Feature("默载申请")
    @Story("用例编号012")
    @Description("验证用户申请的时长不在订购有效期")
    public void testApplyOutsideValidPeriod() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MESSAGE_ID, "testOutsideValidPeriod_" + System.currentTimeMillis());
        params.put(KEY_DURATION, 3600);

        Response response = getResponse(DEF_ADD_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 202002, "接口返回码应为202002表示调用时间不在产品订购有效期范围内");
    }

    @Test
    @Feature("默载申请")
    @Story("用例编号013")
    @Description("验证用户申请的通信服务不存在")
    public void testApplyMismatchedService() {
        Map<String, Object> mismatchedParams = new HashMap<>();
        mismatchedParams.put(KEY_MSISDN, UNICOM_MSISDN);
        mismatchedParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        mismatchedParams.put(KEY_CM_SERVICE_ID, INVALID_CM_SERVICE_ID);
        mismatchedParams.put(KEY_MESSAGE_ID, "testMismatchedService_" + System.currentTimeMillis());
        mismatchedParams.put(KEY_DURATION, 60);

        Response response = getResponse(DEF_ADD_ENDPOINT, mismatchedParams);
        assertEquals(response.jsonPath().getInt("code"), 129404, "接口返回码应为129404表示请求的通信服务不存在");
    }

    @Test
    @Feature("默载终止")
    @Story("用例编号014")
    @Description("验证用户默载终止成功")
    public void testDefBearerTerminateSuccess() {
        // 前置条件：先申请默载获取订单号
        Map<String, Object> applyParams = new HashMap<>();
        applyParams.put(KEY_MSISDN, UNICOM_MSISDN);
        applyParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        applyParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        applyParams.put(KEY_MESSAGE_ID, "testApplyForTerminate_" + System.currentTimeMillis());
        applyParams.put(KEY_DURATION, 60);
        
        Response applyResponse = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + DEF_ADD_ENDPOINT, applyParams);
        Long orderId = applyResponse.jsonPath().getLong("data.orderId");
        
        // 终止默载
        Map<String, Object> terminateParams = new HashMap<>();
        terminateParams.put("orderId", orderId);
        terminateParams.put(KEY_MESSAGE_ID, "testTerminate_" + System.currentTimeMillis());
        terminateParams.put(KEY_MSISDN, UNICOM_MSISDN);

        Response response = getResponse("/terminate/1.0", terminateParams);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载终止")
    @Story("用例编号015")
    @Description("验证用户终止无效订单")
    public void testTerminateInvalidOrder() {
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", -1L);
        params.put(KEY_MESSAGE_ID, "testInvalidOrder_" + System.currentTimeMillis());
        params.put(KEY_MSISDN, UNICOM_MSISDN);

        Response response = getResponse("/terminate/1.0", params);
        assertEquals(response.jsonPath().getInt("code"), 201003, "接口返回码应为201003表示订单ID已无效");
    }

    @Test
    @Feature("默载批量申请")
    @Story("用例编号016")
    @Description("验证用户默载批量申请成功")
    public void testDefBearerBatchApplySuccess() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MESSAGE_ID, "testBatchApply_" + System.currentTimeMillis());
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MSISDN_FILE, VALID_FILE_PATH);
        params.put(KEY_DURATION, 60); // 申请时长，单位为秒
        
        // 调用条件信息
        fillConditions(VALID_HOME_PROVINCE, params);

        // 订购开通信息
        fillSubInfo(VALID_QOS_PRODUCT_ID, params);

        Response response = getResponse(DEF_BATCH_APPLY_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载批量申请")
    @Story("用例编号017")
    @Description("验证用户使用了无效的归属省")
    public void testBatchApplyInvalidProvince() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MESSAGE_ID, "testInvalidProvince_" + System.currentTimeMillis());
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MSISDN_FILE, VALID_FILE_PATH);
        params.put(KEY_DURATION, 60);
        
        // 调用条件信息 - 使用无效的归属省
        fillConditions(INVALID_HOME_PROVINCE, params);

        // 订购开通信息
        fillSubInfo(VALID_QOS_PRODUCT_ID, params);

        Response response = getResponse(DEF_BATCH_APPLY_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 222003, "接口返回码应为222003表示归属省无法转换");
    }

    @Test
    @Feature("默载批量申请")
    @Story("用例编号018")
    @Description("验证用户使用了无效的号码文件路径")
    public void testBatchApplyInvalidFilePath() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MESSAGE_ID, "testInvalidFilePath_" + System.currentTimeMillis());
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MSISDN_FILE, INVALID_FILE_PATH);
        params.put(KEY_DURATION, 60);
        
        // 调用条件信息
        fillConditions(VALID_HOME_PROVINCE, params);

        // 订购开通信息
        fillSubInfo(VALID_QOS_PRODUCT_ID, params);

        Response response = getResponse(DEF_BATCH_APPLY_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 222000, "接口返回码应为222000表示文件不存在");
    }

    @Test
    @Feature("默载批量申请")
    @Story("用例编号019")
    @Description("验证用户使用了无效的通信服务")
    public void testBatchApplyInvalidService() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MESSAGE_ID, "testInvalidService_" + System.currentTimeMillis());
        params.put(KEY_CM_SERVICE_ID, INVALID_CM_SERVICE_ID);
        params.put(KEY_MSISDN_FILE, VALID_FILE_PATH);
        params.put(KEY_DURATION, 60);
        
        // 调用条件信息
        fillConditions(VALID_HOME_PROVINCE, params);

        // 订购开通信息
        fillSubInfo(VALID_QOS_PRODUCT_ID, params);

        Response response = getResponse(DEF_BATCH_APPLY_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 129404, "接口返回码应为129404表示通信服务不存在");
    }

    @Test
    @Feature("默载批量申请")
    @Story("用例编号020")
    @Description("验证用户使用了无效的Qos产品")
    public void testBatchApplyInvalidProduct() {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MESSAGE_ID, "testInvalidProduct_" + System.currentTimeMillis());
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MSISDN_FILE, VALID_FILE_PATH);
        params.put(KEY_DURATION, 60);
        
        // 调用条件信息
        fillConditions(VALID_HOME_PROVINCE, params);

        // 订购开通信息 - 使用无效的产品ID
        fillSubInfo(INVALID_QOS_PRODUCT_ID, params);

        Response response = getResponse(DEF_BATCH_APPLY_ENDPOINT, params);
        assertEquals(response.jsonPath().getInt("code"), 110001, "接口返回码应为110001表示产品不存在");
    }

    private static void fillSubInfo(Integer qosProductId, Map<String, Object> params) {
        Map<String, Object> subscribeInfo = new HashMap<>();
        subscribeInfo.put(KEY_PRODUCTID, qosProductId);
        params.put(KEY_SUBSCRIBE, subscribeInfo);
    }

    @NotNull
    private static Response getResponse(String defBatchApplyEndpoint, Map<String, Object> params) {
        Response response = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + defBatchApplyEndpoint, params);

        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        return response;
    }

    private static void fillConditions(Integer validHomeProvince, Map<String, Object> params) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put(KEY_EXPECT_TIME, DateUtils.formatDate(new Date()));
        conditions.put(KEY_HOME_PROVINCE, validHomeProvince);
        params.put(KEY_CONDITIONS, conditions);
    }

    @Test
    @Feature("默载批量终止")
    @Story("用例编号021")
    @Description("验证用户默载批量终止成功")
    public void testDefBearerBatchTerminateSuccess() {
        // 前置条件：先执行批量申请获取计划ID
        Map<String, Object> batchApplyParams = new HashMap<>();
        batchApplyParams.put(KEY_DURATION, 60);
        batchApplyParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        batchApplyParams.put(KEY_MSISDN_FILE, VALID_FILE_PATH);
        fillSubInfo(VALID_QOS_PRODUCT_ID, batchApplyParams);
        batchApplyParams.put(KEY_MESSAGE_ID, "testBatchApplyForTerminate_" + System.currentTimeMillis());
        fillConditions(VALID_HOME_PROVINCE, batchApplyParams);

        Response batchApplyResponse = RestAssuredUtils.postWithAuth(DEF_BEARER_ENDPOINT + DEF_BATCH_APPLY_ENDPOINT, batchApplyParams);
        Long planId = batchApplyResponse.jsonPath().getLong("data.planId");
        
        // 批量终止
        Map<String, Object> terminateParams = new HashMap<>();
        terminateParams.put("planId", planId != null ? planId : 123456789L);
        terminateParams.put(KEY_MESSAGE_ID, "testBatchTerminate_" + System.currentTimeMillis());

        Response response = getResponse("/batch-terminate/1.0", terminateParams);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("默载批量终止")
    @Story("用例编号022")
    @Description("验证用户使用无效的计划ID")
    public void testBatchTerminateInvalidBatchId() {
        Map<String, Object> params = new HashMap<>();
        params.put("planId", -1L); // 使用无效的计划ID
        params.put(KEY_MESSAGE_ID, "testInvalidPlanId_" + System.currentTimeMillis());

        Response response = getResponse("/batch-terminate/1.0", params);
        assertEquals(response.jsonPath().getInt("code"), 222005, "接口返回码应为222005表示计划ID不存在或状态异常");
    }
}