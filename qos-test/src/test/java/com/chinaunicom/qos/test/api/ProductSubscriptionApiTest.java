package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.test.utils.RestAssuredUtils;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.List;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

/**
 * 产品订购API测试类
 */
@Slf4j
@Epic("产品订购模块")
@SpringBootTest
public class ProductSubscriptionApiTest extends BaseQosTest {
    
    private static final String PRODUCT_SUBSCRIPTION_ENDPOINT = "/qos/user/product";
    private static final String PRODUCT_SUB_ENDPOINT = "/subscribe/1.0";
    private static final String PRODUCT_UNSUB_ENDPOINT = "/unsubscribe/1.0";
    
    // 测试数据常量
    private static final String UNICOM_MSISDN = "13012345678"; // 联通手机号
    private static final String NON_UNICOM_MSISDN = "15912345678"; // 非联通手机号
    private static final Integer VALID_QOS_PRODUCT_ID = 880081; // 合法的QoS产品ID
    private static final Integer INVALID_QOS_PRODUCT_ID = 999999; // 不存在的QoS产品ID
    private static final Integer HIGH_PRIORITY_PRODUCT_ID = 880082; // 高优先级产品ID
    private static final Integer LOW_PRIORITY_PRODUCT_ID = 880083; // 低优先级产品ID

    private static final String KEY_MSISDN = "msisdn";
    private static final String KEY_PRODUCTID = "qosProductId";
    private static final String KEY_MESSAGE_ID = "messageId";
    private static final String KEY_EXPIRE_TIME = "expireTime";
    private static final String KEY_EFFECTIVE_TIME = "effectiveTime";

    private static final String HTTP_RESULT_OK = "状态码应为200";
    private static final String CODE_RESULT_OK = "接口返回码应为0表示成功";
    // 需要清理的产品ID列表
    private static final List<Integer> TEST_PRODUCT_IDS = Arrays.asList(
        VALID_QOS_PRODUCT_ID, 
        HIGH_PRIORITY_PRODUCT_ID, 
        LOW_PRIORITY_PRODUCT_ID
    );
    
    // 需要清理的手机号列表
    private static final List<String> TEST_MSISDNS = Arrays.asList(
        UNICOM_MSISDN,
        NON_UNICOM_MSISDN
    );

    /**
     * 每个测试方法执行前的数据清理
     */
    @BeforeMethod
    public void cleanupBeforeTest() {
        log.info("开始执行测试前数据清理");
        cleanupTestData();
        log.info("测试前数据清理完成");
    }

    /**
     * 每个测试方法执行后的数据清理
     */
    @AfterMethod
    public void cleanupAfterTest() {
        log.info("开始执行测试后数据清理");
        cleanupTestData();
        log.info("测试后数据清理完成");
    }

    /**
     * 清理测试数据
     * 为每个测试手机号和产品ID组合执行退订操作
     */
    private void cleanupTestData() {
        for (String msisdn : TEST_MSISDNS) {
            for (Integer productId : TEST_PRODUCT_IDS) {
                try {
                    unsubscribeProduct(msisdn, productId);
                } catch (Exception e) {
                    // 忽略清理过程中的异常，因为可能产品本来就没有订购
                    log.debug("清理数据时发生异常，手机号: {}, 产品ID: {}, 异常: {}", 
                             msisdn, productId, e.getMessage());
                }
            }
        }
    }

    /**
     * 退订指定手机号的指定产品
     * @param msisdn 手机号
     * @param productId 产品ID
     */
    private void unsubscribeProduct(String msisdn, Integer productId) {
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, msisdn);
        params.put(KEY_PRODUCTID, productId);
        params.put(KEY_MESSAGE_ID, "cleanup_" + System.currentTimeMillis() + "_" + msisdn + "_" + productId);
        params.put(KEY_EXPIRE_TIME, DateUtils.formatDate(new Date()));
        
        try {
            Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_UNSUB_ENDPOINT, params);
            log.debug("清理数据 - 手机号: {}, 产品ID: {}, 响应码: {}, 返回码: {}", 
                     msisdn, productId, response.getStatusCode(), 
                     response.jsonPath().getInt("code"));
        } catch (Exception e) {
            log.debug("清理数据异常 - 手机号: {}, 产品ID: {}, 异常: {}", msisdn, productId, e.getMessage());
        }
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号001")
    @Description("验证新联通用户订购成功")
    public void testUnicomUserSubscribeSuccess() {
        // 准备测试数据
        Map<String, Object> successParams = new HashMap<>();
        successParams.put(KEY_MSISDN, UNICOM_MSISDN);
        successParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        successParams.put(KEY_MESSAGE_ID, "testUnicomUserSubscribe_" + System.currentTimeMillis());
        successParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        successParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30))); // 30天后失效
        
        // 调用订购接口
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, successParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号002")
    @Description("验证用户订购不存在的Qos产品")
    public void testSubscribeNonExistentProduct() {
        // 准备测试数据
        Map<String, Object> noProductParams = new HashMap<>();
        noProductParams.put(KEY_MSISDN, UNICOM_MSISDN);
        noProductParams.put(KEY_PRODUCTID, INVALID_QOS_PRODUCT_ID);
        noProductParams.put(KEY_MESSAGE_ID, "testNonExistentProduct_" + System.currentTimeMillis());
        noProductParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        noProductParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30)));
        
        // 调用订购接口
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, noProductParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110001, "接口返回码应为110001表示产品不存在");
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号003")
    @Description("验证非联通用户订购失败")
    public void testNonUnicomUserSubscribeFail() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, NON_UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_MESSAGE_ID, "testNonUnicomUser_" + System.currentTimeMillis());
        params.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        params.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30)));
        
        // 调用订购接口
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, params);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110011, "接口返回码应为110011表示非联通用户");
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号004")
    @Description("验证产品互斥冲突")
    public void testProductMutualExclusionConflict() {
        // 前置条件：先订购高优先级产品
        subPreProduct(HIGH_PRIORITY_PRODUCT_ID, "testHighPriority_");

        // 尝试订购低优先级产品
        Map<String, Object> lowPriorityParams = new HashMap<>();
        lowPriorityParams.put(KEY_MSISDN, UNICOM_MSISDN);
        lowPriorityParams.put(KEY_PRODUCTID, LOW_PRIORITY_PRODUCT_ID);
        lowPriorityParams.put(KEY_MESSAGE_ID, "testLowPriority_" + System.currentTimeMillis());
        lowPriorityParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        lowPriorityParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30)));
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, lowPriorityParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110002, "接口返回码应为110002表示产品冲突");
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号005")
    @Description("验证产品互斥替换")
    public void testProductMutualExclusionReplacement() {
        // 前置条件：先订购低优先级产品
        subPreProduct(LOW_PRIORITY_PRODUCT_ID, "testLowPriorityFirst_");

        // 订购高优先级产品，应该成功并替换低优先级产品
        Map<String, Object> highPriorityParams = new HashMap<>();
        highPriorityParams.put(KEY_MSISDN, UNICOM_MSISDN);
        highPriorityParams.put(KEY_PRODUCTID, HIGH_PRIORITY_PRODUCT_ID);
        highPriorityParams.put(KEY_MESSAGE_ID, "testHighPriorityReplace_" + System.currentTimeMillis());
        highPriorityParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        highPriorityParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30)));
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, highPriorityParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
        assertNotNull(response.jsonPath().get("data"), "data字段应返回被互斥退订的产品信息");
    }

    @Test
    @Feature("产品订购")
    @Story("用例编号006")
    @Description("验证续订产品时失效时间比原有失效时间早")
    public void testRenewWithEarlierExpireTime() {
        // 前置条件：先订购产品
        subPreProduct(VALID_QOS_PRODUCT_ID, "testInitialSubscribe_");

        // 尝试续订，但失效时间比原有时间早
        Map<String, Object> renewParams = new HashMap<>();
        renewParams.put(KEY_MSISDN, UNICOM_MSISDN);
        renewParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        renewParams.put(KEY_MESSAGE_ID, "testRenewEarlier_" + System.currentTimeMillis());
        renewParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        renewParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 15))); // 15天后失效，比原有时间早
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, renewParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110005, "接口返回码应为110005表示产品失效时间错误");
    }

    @Test
    @Feature("产品退订")
    @Story("用例编号007")
    @Description("验证用户退订产品成功")
    public void testUnsubscribeSuccess() {
        // 前置条件：先订购产品
        subPreProduct(VALID_QOS_PRODUCT_ID, "testSubscribeForUnsubscribe_");

        // 退订产品
        Map<String, Object> unsubscribeParams = new HashMap<>();
        unsubscribeParams.put(KEY_MSISDN, UNICOM_MSISDN);
        unsubscribeParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        unsubscribeParams.put(KEY_MESSAGE_ID, "testUnsubscribe_" + System.currentTimeMillis());
        unsubscribeParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(new Date()));
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_UNSUB_ENDPOINT, unsubscribeParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 0, CODE_RESULT_OK);
    }

    @Test
    @Feature("产品退订")
    @Story("用例编号008")
    @Description("验证用户退订未订购的产品")
    public void testUnsubscribeNotSubscribedProduct() {
        // 准备测试数据 - 退订未订购的产品
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_MESSAGE_ID, "testUnsubscribeNotSubscribed_" + System.currentTimeMillis());
        params.put(KEY_EXPIRE_TIME, DateUtils.formatDate(new Date()));
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_UNSUB_ENDPOINT, params);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110003, "接口返回码应为110003表示退订错误，用户未订购");
    }

    @Test
    @Feature("产品退订")
    @Story("用例编号009")
    @Description("验证用户退订产品时失效时间比原有失效时间晚")
    public void testUnsubscribeWithLaterExpireTime() {
        // 前置条件：先订购产品
        subPreProduct(VALID_QOS_PRODUCT_ID, "testSubscribeForLaterUnsubscribe_");

        // 尝试退订，但失效时间比原有时间晚
        Map<String, Object> unsubscribeParams = new HashMap<>();
        unsubscribeParams.put(KEY_MSISDN, UNICOM_MSISDN);
        unsubscribeParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        unsubscribeParams.put(KEY_MESSAGE_ID, "testUnsubscribeLater_" + System.currentTimeMillis());
        unsubscribeParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 45))); // 45天后失效，比原有时间晚
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_UNSUB_ENDPOINT, unsubscribeParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110005, "接口返回码应为110005表示产品失效时间错误");
    }

    private static void subPreProduct(Integer validQosProductId, String messageId) {
        Map<String, Object> subscribeParams = new HashMap<>();
        subscribeParams.put(KEY_MSISDN, UNICOM_MSISDN);
        subscribeParams.put(KEY_PRODUCTID, validQosProductId);
        subscribeParams.put(KEY_MESSAGE_ID, messageId + System.currentTimeMillis());
        subscribeParams.put(KEY_EFFECTIVE_TIME, DateUtils.formatDate(new Date()));
        subscribeParams.put(KEY_EXPIRE_TIME, DateUtils.formatDate(DateUtils.computeDate(new Date(), 30))); // 30天后失效

        RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + PRODUCT_SUB_ENDPOINT, subscribeParams);
    }
}