package com.chinaunicom.qos.test.api;

import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.test.base.BaseQosTest;
import com.chinaunicom.qos.test.utils.RestAssuredUtils;
import io.qameta.allure.Description;
import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Story;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.*;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

/**
 * 专载服务API测试类
 */
@Slf4j
@Epic("专载服务模块")
@SpringBootTest
public class DedBearerApiTest extends BaseQosTest {
    @Value("${ip.target:********}")
    private String targetIp;

    private final List<String> validTargetIps = new ArrayList<>();
    
    private static final String DED_BEARER_ENDPOINT = "/qos/dedbearer";
    private static final String PRODUCT_SUBSCRIPTION_ENDPOINT = "/qos/user/product";
    private static final String USER_QUERY_ENDPOINT = "/qos/manage/user/single";
    private static final String DED_ADD_ENDPOINT = "/add/1.0";
    private static final String DED_MODIFY_ENDPOINT = "/modify/1.0";
    
    private static final String KEY_MSISDN = "msisdn";
    private static final String KEY_PRODUCTID = "qosProductId";
    private static final String KEY_MESSAGE_ID = "messageId";
    private static final String KEY_CM_SERVICE_ID = "cmServiceId";
    private static final String KEY_PRIVATE_IP = "privateIp";
    private static final String KEY_DURATION = "duration";
    private static final String KEY_TARGET_IPS = "targetIps";
    private static final String KEY_ORDERID = "orderId";
    private static final String KEY_DATA_ORDERID = "data.orderId";
    
    private static final String HTTP_RESULT_OK = "状态码应为200";
    // 测试数据常量
    private static final String UNICOM_MSISDN = "16652288250"; // 联通手机号
    private static final Integer VALID_QOS_PRODUCT_ID = 880085; // 合法的QoS产品ID
    private static final Integer INVALID_QOS_PRODUCT_ID = 999999; // 无效的QoS产品ID
    private static final String VALID_CM_SERVICE_ID = "3211023863"; // 有效的通信服务ID
    private static final String INVALID_CM_SERVICE_ID = "9999999999"; // 无效的通信服务ID
    
    // 专载服务特有参数
    private String dynamicPrivateIp; // 动态获取的私有IP
    private static final Integer VALID_DURATION = 60; // 有效的持续时间（分钟）

    /**
     * 测试类启动前的准备工作：订购产品和获取私网IP
     */
    @BeforeClass
    public void setupProductSubscription() {
        log.info("开始为专载测试准备产品订购数据");
        validTargetIps.add(targetIp);
        
        try {
            // 先清理可能存在的旧数据
            cleanupProductSub();
            
            // 获取用户私网IP
            getUserPrivateIp();
            
            // 订购测试所需的产品
            subscribeTestProduct();
            
            log.info("产品订购准备完成");
        } catch (Exception e) {
            log.error("产品订购准备失败", e);
        }
    }

    /**
     * 测试类结束后的清理工作：退订产品
     */
    @AfterClass
    public void cleanupProductSub() {
        log.info("开始清理专载测试的产品订购数据");
        
        try {
            // 退订测试产品
            unsubscribeTestProduct();
            
            log.info("产品订购数据清理完成");
        } catch (Exception e) {
            log.warn("产品订购数据清理失败", e);
        }
    }

    /**
     * 订购测试所需的产品
     */
    private void subscribeTestProduct() {
        Map<String, Object> subParams = new HashMap<>();
        subParams.put(KEY_MSISDN, UNICOM_MSISDN);
        subParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        subParams.put(KEY_MESSAGE_ID, "dedbearer_test_setup_" + System.currentTimeMillis());
        subParams.put("effectiveTime", DateUtils.formatDate(new Date()));
        subParams.put("expireTime", DateUtils.formatDate(DateUtils.computeDate(new Date(), 600))); // 600天后失效，确保有足够的时间
        
        Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + "/subscribe/1.0", subParams);
        
        log.info("产品订购结果 - 状态码: {}, 返回码: {}, 手机号: {}, 产品ID: {}", 
                response.getStatusCode(), 
                response.jsonPath().getInt("code"), 
                UNICOM_MSISDN, 
                VALID_QOS_PRODUCT_ID);
        
        // 如果返回码不是0（成功），记录详细信息但不抛异常，因为可能已经订购过了
        if (response.jsonPath().getInt("code") != 0) {
            log.warn("产品订购返回非成功码: {}, 消息: {}", 
                    response.jsonPath().getInt("code"), 
                    response.jsonPath().getString("message"));
        }
    }

    /**
     * 退订测试产品
     */
    private void unsubscribeTestProduct() {
        Map<String, Object> unsubParams = new HashMap<>();
        unsubParams.put(KEY_MSISDN, UNICOM_MSISDN);
        unsubParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        unsubParams.put(KEY_MESSAGE_ID, "dedbearer_test_cleanup_" + System.currentTimeMillis());
        unsubParams.put("expireTime", DateUtils.formatDate(new Date()));
        
        try {
            Response response = RestAssuredUtils.postWithAuth(PRODUCT_SUBSCRIPTION_ENDPOINT + "/unsubscribe/1.0", unsubParams);
            
            log.info("产品退订结果 - 状态码: {}, 返回码: {}, 手机号: {}, 产品ID: {}", 
                    response.getStatusCode(), 
                    response.jsonPath().getInt("code"), 
                    UNICOM_MSISDN, 
                    VALID_QOS_PRODUCT_ID);
        } catch (Exception e) {
            log.warn("产品退订失败", e);
        }
    }

    /**
     * 获取用户私网IP地址
     */
    private void getUserPrivateIp() {
        log.info("开始获取用户私网IP地址");
        
        try {
            Map<String, Object> params = new HashMap<>();
            params.put(KEY_MSISDN, UNICOM_MSISDN);
            params.put(KEY_MESSAGE_ID, "getUserPrivateIp_" + System.currentTimeMillis());
            
            Response response = RestAssuredUtils.postWithAuth(USER_QUERY_ENDPOINT + "/getUserPrivateIp/1.0", params);
            
            if (response.getStatusCode() == HttpStatus.OK.value() && response.jsonPath().getInt("code") == 0) {
                // 尝试获取 userIp 字段（根据实际接口返回结构）
                String privateIp = response.jsonPath().getString("data.userIp");
                if (privateIp != null && !privateIp.isEmpty()) {
                    this.dynamicPrivateIp = privateIp;
                    log.info("成功获取用户私网IP: {}", privateIp);
                }
            }
        } catch (Exception e) {
            log.warn("获取私网IP异常", e);
        }
    }

    /**
     * 获取当前使用的私网IP
     */
    private String getCurrentPrivateIp() {
        return dynamicPrivateIp;
    }

    @Test
    @Feature("专载申请")
    @Story("用例编号023")
    @Description("验证用户申请专载成功")
    public void testDedBearerApplySuccess() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        params.put(KEY_MESSAGE_ID, "testDedBearerApply_" + System.currentTimeMillis());
        
        // 专载服务特有参数
        params.put(KEY_PRIVATE_IP, getCurrentPrivateIp()); // 动态获取的私有IP地址
        params.put(KEY_DURATION, VALID_DURATION); // 持续时间（分钟）
        params.put(KEY_TARGET_IPS, validTargetIps); // 目标IP地址列表
        
        // 调用专载申请接口
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, params);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        // 309000表示补开户成功，309001表示需断网重连，309107表示调用时间不在产品订购有效期范围内，309113表示失败重试次数达到阈值，这些都是成功状态
        int responseCode = response.jsonPath().getInt("code");
        assertTrue(responseCode == 0 , "接口返回码应为0（成功）");
    }

    @Test
    @Feature("专载申请")
    @Story("用例编号024")
    @Description("验证用户申请未订购的产品")
    public void testApplyUnsubscribedProduct() {
        // 准备测试数据 - 申请未订购的产品
        Map<String, Object> unsubParams = new HashMap<>();
        unsubParams.put(KEY_MSISDN, UNICOM_MSISDN);
        unsubParams.put(KEY_PRODUCTID, INVALID_QOS_PRODUCT_ID);
        unsubParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        unsubParams.put(KEY_MESSAGE_ID, "testUnsubscribedProduct_" + System.currentTimeMillis());
        
        // 专载服务特有参数
        unsubParams.put(KEY_PRIVATE_IP, getCurrentPrivateIp());
        unsubParams.put(KEY_DURATION, VALID_DURATION);
        unsubParams.put(KEY_TARGET_IPS, validTargetIps);
        
        // 调用专载申请接口
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, unsubParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 110000, "接口返回码应为110000表示未查询到用户的产品订购信息");
    }

    @Test
    @Feature("专载申请")
    @Story("用例编号025")
    @Description("验证用户申请的时长不在订购有效期")
    public void testApplyOutsideValidPeriod() {
        // 准备测试数据 - 申请时长超出订购有效期
        Map<String, Object> validPeriodParams = new HashMap<>();
        validPeriodParams.put(KEY_MSISDN, UNICOM_MSISDN);
        validPeriodParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        validPeriodParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        validPeriodParams.put(KEY_MESSAGE_ID, "testOutsideValidPeriod_" + System.currentTimeMillis());
        
        // 专载服务特有参数
        validPeriodParams.put(KEY_PRIVATE_IP, getCurrentPrivateIp());
        validPeriodParams.put(KEY_DURATION, 3600);
        validPeriodParams.put(KEY_TARGET_IPS, validTargetIps);
        
        // 调用专载申请接口
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, validPeriodParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 309107, "接口返回码应为309107表示调用时间不在产品订购有效期范围内");
    }

    @Test
    @Feature("专载申请")
    @Story("用例编号026")
    @Description("验证用户申请的通信服务无效")
    public void testApplyMismatchedService() {
        // 准备测试数据 - 通信服务与QoS产品不匹配
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_MSISDN, UNICOM_MSISDN);
        params.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        params.put(KEY_CM_SERVICE_ID, INVALID_CM_SERVICE_ID); // 使用不匹配的通信服务ID
        params.put(KEY_MESSAGE_ID, "testMismatchedService_" + System.currentTimeMillis());
        
        // 专载服务特有参数
        params.put(KEY_PRIVATE_IP, getCurrentPrivateIp());
        params.put(KEY_DURATION, VALID_DURATION);
        params.put(KEY_TARGET_IPS, validTargetIps);
        
        // 调用专载申请接口
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, params);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 129404, "接口返回码应为129404表示请求的通信服务ID不存在");
    }

    @Test
    @Feature("专载修改")
    @Story("用例编号027")
    @Description("验证用户专载修改成功")
    public void testDedBearerModifySuccess() {
        // 前置条件：先申请专载获取订单号
        Map<String, Object> applyParams = new HashMap<>();
        applyParams.put(KEY_MSISDN, UNICOM_MSISDN);
        applyParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        applyParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        applyParams.put(KEY_MESSAGE_ID, "testApplyForModify_" + System.currentTimeMillis());
        applyParams.put(KEY_PRIVATE_IP, getCurrentPrivateIp());
        applyParams.put(KEY_DURATION, VALID_DURATION);
        applyParams.put(KEY_TARGET_IPS, validTargetIps);
        
        Response applyResponse = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, applyParams);
        
        // 获取真实的订单ID
        Long orderId = getOrderId(applyResponse, "成功获取订单ID用于修改测试: {}", "专载申请失败，无法进行修改测试，状态码: {}, 返回码: {}");
        if (orderId == null) return;

        // 修改专载
        Response response = getModifyResponse(orderId, "testModify_", 120);

        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        // 修改操作可能返回0（成功）或309202（订单ID已无效，如果订单已过期）
        int responseCode = response.jsonPath().getInt("code");
        assertTrue(responseCode == 0 || responseCode == 309202, 
                "接口返回码应为0（成功）或309202（订单ID已无效），实际返回码：" + responseCode);
    }

    @Test
    @Feature("专载修改")
    @Story("用例编号028")
    @Description("验证用户修改不存在的订单")
    public void testModifyNonExistentOrder() {
        // 准备测试数据 - 使用无效的订单号
        Response response = getModifyResponse(999999999L, "testInvalidOrder_", 120);

        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 309202, "接口返回码应为309202表示订单ID已无效");
    }

    @Test
    @Feature("专载修改")
    @Story("用例编号029")
    @Description("验证用户修改时长不在Qos订购有效期")
    public void testModifyOutsideValidPeriod() {
        // 前置条件：先申请专载获取订单号
        Response applyResponse = preApply();

        // 获取真实的订单ID
        Long orderId = getOrderId(applyResponse, "成功获取订单ID用于修改超出有效期测试: {}", "专载申请失败，无法进行修改测试，状态码: {}, 返回码: {}");
        if (orderId == null) return;

        // 修改专载，但时长超出订购有效期
        Response response = getModifyResponse(orderId, "testModifyOutside_", 525600);

        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        // 可能返回309107（超出有效期）或309202（订单ID已无效）
        int responseCode = response.jsonPath().getInt("code");
        assertTrue(responseCode == 309107 || responseCode == 309202, 
                "接口返回码应为309107（调用时间不在产品订购有效期范围内）或309202（订单ID已无效），实际返回码：" + responseCode);
    }

    private Response getModifyResponse(Long orderId, String messageId, int duration) {
        Map<String, Object> modifyParams = new HashMap<>();
        modifyParams.put(KEY_ORDERID, orderId);
        modifyParams.put(KEY_MESSAGE_ID, messageId + System.currentTimeMillis());
        modifyParams.put(KEY_TARGET_IPS, new String[]{targetIp});
        modifyParams.put(KEY_DURATION, duration);

        return RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_MODIFY_ENDPOINT, modifyParams);
    }

    @Nullable
    private static Long getOrderId(Response applyResponse, String s, String s1) {
        Long orderId = null;
        if (applyResponse.getStatusCode() == HttpStatus.OK.value() && applyResponse.jsonPath().getInt("code") == 0) {
            orderId = applyResponse.jsonPath().getLong(KEY_DATA_ORDERID);
            log.info(s, orderId);
        } else {
            log.warn(s1,
                    applyResponse.getStatusCode(), applyResponse.jsonPath().getInt("code"));
            // 如果申请失败，跳过修改测试
            return null;
        }
        return orderId;
    }

    private Response preApply() {
        Map<String, Object> applyParams = new HashMap<>();
        applyParams.put(KEY_MSISDN, UNICOM_MSISDN);
        applyParams.put(KEY_PRODUCTID, VALID_QOS_PRODUCT_ID);
        applyParams.put(KEY_CM_SERVICE_ID, VALID_CM_SERVICE_ID);
        applyParams.put(KEY_MESSAGE_ID, "testApplyForModifyOutside_" + System.currentTimeMillis());
        applyParams.put(KEY_PRIVATE_IP, getCurrentPrivateIp());
        applyParams.put(KEY_DURATION, VALID_DURATION);
        applyParams.put(KEY_TARGET_IPS, validTargetIps);

        return RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + DED_ADD_ENDPOINT, applyParams);
    }

    @Test
    @Feature("专载终止")
    @Story("用例编号030")
    @Description("验证用户专载终止成功")
    public void testDedBearerTerminateSuccess() {
        // 前置条件：先申请专载获取订单号
        Response applyResponse = preApply();
        
        // 获取真实的订单ID
        Long orderId = getOrderId(applyResponse, "成功获取订单ID用于终止测试: {}", "专载申请失败，无法进行终止测试，状态码: {}, 返回码: {}");
        if (orderId == null) return;

        // 终止专载
        Map<String, Object> terminateParams = new HashMap<>();
        terminateParams.put(KEY_ORDERID, orderId);
        terminateParams.put(KEY_MESSAGE_ID, "testTerminate_" + System.currentTimeMillis());
        
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + "/terminate/1.0", terminateParams);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        // 终止操作可能返回0（成功）或309202（订单ID已无效，如果订单已过期）
        int responseCode = response.jsonPath().getInt("code");
        assertTrue(responseCode == 0 || responseCode == 309202, 
                "接口返回码应为0（成功）或309202（订单ID已无效），实际返回码：" + responseCode);
    }

    @Test
    @Feature("专载终止")
    @Story("用例编号031")
    @Description("验证用户终止无效订单")
    public void testTerminateInvalidOrder() {
        // 准备测试数据 - 使用无效的订单号
        Map<String, Object> params = new HashMap<>();
        params.put(KEY_ORDERID, 999999999L); // 无效的订单ID
        params.put(KEY_MESSAGE_ID, "testInvalidOrder_" + System.currentTimeMillis());
        
        // 调用终止接口
        Response response = RestAssuredUtils.postWithAuth(DED_BEARER_ENDPOINT + "/terminate/1.0", params);
        
        // 验证结果
        assertEquals(response.getStatusCode(), HttpStatus.OK.value(), HTTP_RESULT_OK);
        assertEquals(response.jsonPath().getInt("code"), 309202, "接口返回码应为309202表示订单ID已无效");
    }
}