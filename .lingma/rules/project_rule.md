你是一个资深的java专家，请在开发中遵循如下规则：

以下是项目自定义代码规范
# 项目代码规范
dubbo服务样例工程qos-batch、qos-batch/qos-batch-api、qos-batch/qos-batch-common、qos-batch/qos-batch-service
api网关用例工程qos-api/qos-api-user
qos-batch:代表父类模块
qos-batch/qos-batch-api:代表dubbo的api规范
qos-batch/qos-batch-common:代表公共模块
qos-batch/qos-batch-service:代表dubbo的服务实现

@author值设置为pengb7
@date值设置为当前时间
一、命名规范
所有类、方法、参数、变量的命名，做到望文知义。名称应准确反映其用途和功能，避免有歧义、过短的名称，保持名称简洁而有足够的描述性

1.1 类和接口命名
1. 使用名词，采用大驼峰命名法。例如：StudentService、ProductRepository。
2. 避免使用缩写，除非是被广泛认可的缩写，如DTO（Data Transfer Object）、PO（Persistent Object）。

1.2 方法命名
1. 采用小驼峰命名法，动词开头。例如：getStudentById、saveProduct。
2. 反映方法的行为，如find、update、delete、create等。

1.3 变量命名
1. 小驼峰命名法。
2. 局部变量应简洁明了，反映其用途。例如：count、name。

1.4 常量命名
1. 全部大写，单词之间用下划线分隔。例如：MAX_COUNT、DEFAULT_VALUE。

1.5 参数命名
1. 与变量命名规则相同，清晰表达参数的含义。
2. 避免使用过于通用的名称，如arg、param。

1.6 数组命名
1. 采用复数形式。例如：students、products。

1.7 异常枚举命名
1. 以Exception、Enum结尾。例如：ResourceNotFoundException、 ExceptionCodeEnum。

1.8 包命名
1. 基于域名的反序，例如公司域名是example.com，包名可以是com.example.project.module。
2. 反映包的功能层次，如com.example.util（工具类）、com.example.controller（控制器类）。

1.9 集合命名
1. 列表：以list结尾，例如：customerList。
2. 集合：以set结尾，例如：productSet。
3. 映射：以map结尾，例如：userMap

1.10 命名的一致性
在整个项目中保持命名的一致性，遵循相同的规则和风格：
各层命名规约：
A）Service / DAO 层方法命名规约：
1）获取单个对象的方法用 get 做前缀。
2）获取多个对象的方法用 list 做前缀，复数结尾，如：listObjects。
3）获取统计值的方法用 count 做前缀。
4）插入的方法用 save / insert 做前缀。
5）删除的方法用 remove / delete 做前缀。
6）修改的方法用 update 做前缀。
B）领域模型命名规约：
1）展示对象：xxxVO，xxx 一般为网页名称。
如果展示对象和数据传输对象字段一致，可以直接使用 xxxDTO 数据传输对象
2）数据传输对象：xxxDTO，xxx 为业务领域相关的名称。
3）应用服务对象：xxxBO，xxx 为业务应用服务相关的名称。
4）领域对象：xxxDO，xxx 为业务领域相关的名称。
5）数据持久化对象：xxxPO，xxx 即为数据表名。
6）POJO 是 VO / DTO / BO / DO / PO 的统称，禁止命名成 xxxPOJO。
详细参考：
https://alidocs.dingtalk.com/api/doc/transit?spaceId=20982203286&dentryId=140468884297&corpId=ding33fa17533e39dbbbbc961a6cb783455b
二、注释规范
2.1 总体原则
1. 注释应简洁明了，避免冗长和不必要的描述。
2. 保持注释的准确性和及时性，当代码修改时，相应的注释也要更新。
3. 避免注释与代码不一致的情况，以免造成混淆。
4. 只注释关键和复杂的部分，显而易见的代码不要注释。

2.2 类注释
1. 位于类定义的上方，使用/** */格式的多行注释。
2. 包含类的简要描述、作者、创建日期信息 （模版设置见：https://alidocs.dingtalk.com/i/nodes/14dA3GK8gwlZGZgEHgjleZA989ekBD76）
   /**
* 取号服务
*
* <AUTHOR>
* @date 时间
  */
  @Slf4j
  @Component
  public class MobileProxy {
  // 类的具体实现
  }

2.3 方法注释
1. 位于方法定义的上方，使用/** */格式的多行注释。
2. 描述方法的功能、参数（可选）、返回值（可选）和可能抛出的异常。
3. 接口、抽象方法要求有注释，逻辑复杂的普通方法建议注释。
   /**
* 获取号卡基础信息
  */
  Resp<UserInfoDTO> queryUserInfo(UserInfoReq userInfoReq);

2.4 行内注释
1. 使用//进行注释，用于解释某一行或一小段代码的作用。
   // 增加查询老系统的订单，新系统如果已经有数据了，则不用查询老系统
   List pccOrders = pccService.getOrders(msisdn);


三、工程规范
模块 包 职责
xxx-api request、response、service 负责服务对外暴露的接口定义
xxx-server starter 负责项目启动配置和业务入口，包括接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理等工作
xxx-server application 负责用例实现，通常串联内部领域服务接口或外部服务接口，实现流程编排、聚合查询等工作流操作
xxx-server domain 负责业务逻辑实现，通常抽象出领域对象，来表达业务概念，承载业务行为和规则。业务逻辑不复杂，可以没有
xxx-server infrastructure 通常负责外部调用，比如数据库的CRUD、搜索引擎、文件系统、分布式服务的RPC等
具体参考：
【强制】：网关 api controller 接口定义不要使用内部服务的接口模型，需要单独定义

四、日志打印
日志的主要作用：1）调试排查问题；2）统计用户行为；3）监控和告警；4）撕逼和甩锅利器
要求：对程序运行情况能够记录和监控，必要时可了解程序内部的运行状态

4.1 日志级别的使用
1. ERROR：用于记录严重的错误，通常表示程序错误影响到实际业务，需要人工介入的情况。
2. WARN：用于记录不太严重但仍需要关注的情况，可能表示潜在的问题或异常。
3. INFO：用于记录重要的业务流程和关键操作的信息。
4. DEBUG：用于在开发和调试阶段记录详细的调试信息，在生产环境中通常应关闭。

4.2 日志内容
1. 包含足够的上下文信息，如相关的类名、方法名、输入参数和关键的中间结果。
2. 对于异常，记录完整的异常堆栈信息。
3. 避免无效、重复日志输出。

4.3  日志打印注意事项
1、核心方法要有日志记录，方便问题排查
public void consumeMessage(ConsumerRecord<String, String> message) {
log.info("接收消息成功，开始处理，消息内容:{}", message.value());
// 业务逻辑
}
2、核心逻辑遇到if...else...等条件时，首行尽量打印日志，方便排查问题（注意记录重要参数，出问题时知道进了哪个分支）；

3、使用占位符，而不是+号进行字符串拼接，提高可读性，减少性能损耗；
// 使用了大括号{}来作为日志中的占位符，比使用+操作符，更加优雅简洁
logger.info("Processing trade with id: {} and symbol : {} ", id, symbol);

// 使用+操作符进行字符串的拼接，有一定的性能损耗
logger.info("Processing trade with id: " + id + " and symbol: " + symbol);

4、异常日志不要打一半，要输出全部错误信息；
try {
//业务代码处理
} catch (Exception e) {
// 异常e都没有打印出来，压根不知道出了什么类型的异常
log.error('你的程序有异常啦');

    //不会记录详细的堆栈异常信息，只会记录错误基本描述信息，不利于排查问题
    log.error('你的程序有异常啦', e.getMessage());

    // 正确方式
    log.error('你的程序有异常啦', e);
}
5、不要既打印异常，又抛出异常，这样会重复输出日志
// 通常会把栈信息打印两次，因为捕获了MyException异常的地方，还会再打印一次
log.error("IO exception", e);
throw new MyException(e);
6、避免打印日志造成业务流程阻断，比如：info.getNumber()
//如果info为null，此时会报错，阻断业务流程
log.info("info number={} ", info.getNumber());
7、建议核心数据写操作成功后，打上日志。（生产上出错大部分都是数据问题）


五、异常处理
5.1 总体原则
1. 异常处理应该是为了增强程序的健壮性和可维护性，而不是隐藏问题；
2. 只处理能够被有效解决的异常，避免无意义的捕获和处理；
3. 尽早抛出异常，尽晚捕获异常。

5.2 捕获具体异常类型
1. 优先捕获特定的异常子类和自定义异常，如FileNotFoundException、UserServiceException等，而不是直接捕获Exception。
2. 对可能抛出多种异常的代码块，分别捕获并处理每种异常，避免将不同类型的异常混在一起处理。
3. 自定义异常必须记录message

5.3 异常处理的层次
1. 绝大部分场景，不允许捕获异常，都抛出去，到业务层处理，遵循早 throw，晚 catch
2. 底层模块的异常处理应尽量简单，专注于将异常传递给上层模块，上层模块根据业务逻辑进行更全面和统一的处理。

5.4 早抛出晚捕获
1. 如果当前方法无法完全处理异常，应使用 throw 关键字将异常向上抛出给调用者。
2. 在重新抛出异常时，可以包装原始异常为新的异常，以添加更多上下文信息，但不要丢失原始异常的堆栈跟踪。
   public void wrapException(String input) throws UserServiceException {    
   try {
   // do something
   } catch (Exception e) {
   // 会丢失异常
   throw new UserServiceException("出错了");
   // 不推荐下面方式, 统一在业务层打印日志
   log.error("出错了"， e)；
   throw new UserServiceException("出错了");
   // 正确处理
   throw new UserServiceException("出错了"，e);
   }
   }

5.5 统一错误码
1. 在某些情况下，可以结合使用错误码和异常。例如，对于一些可预期的、可以通过重试或其他方式恢复的错误；对于严重的、不可恢复的错误，抛出异常。
2. 确保错误码和异常的使用在整个项目中具有一致性和清晰的定义。

5.6  资源清理
1. 在try-catch块中，如果使用了需要手动释放的资源（如文件流、数据库连接、网络连接等），在finally块中确保资源被正确关闭和释放。
2. 使用try-with-resources语句来自动管理资源的释放，以简化代码并减少资源泄漏的风险。

5.7 异常处理的性能
1. 避免在异常处理中进行过于复杂或耗时的操作，以免影响程序的性能。
2. 尽量减少用try监控的代码块范围

5.8 日志记录
1. 在捕获异常时，详细记录异常的信息，包括异常的类型、消息、堆栈跟踪以及相关的上下文信息（如当前操作的对象、输入参数等）。
2. 使用合适的日志级别，严重的异常使用ERROR级别，不太严重但仍需关注的异常使用WARN级别。
3. 抛出异常的时候，不要打印日志，防止重复打印

5.9 不要忽略异常
1. 捕获异常后必须进行有意义的处理，如记录日志、进行错误恢复或向上抛出。
2. 严禁使用空的catch块来忽略异常，除非在特殊情况下经过谨慎评估并添加明确的注释说明

六、方法规范
方法编写指导原则是抽象和封装，适应可能的变化，记住代码是给人看的，编写出非开发人员能看懂的代码，这是我们的追求

6.1 方法签名
1. 方法名使用小驼峰命名法，动词开头，准确且简洁地描述方法的行为，如processData、validateInput；
2. 每个参数都要有明确的数据类型和有意义的参数名，例如int age, String name；
3. 不要出现和业务无关的参数，方法只需要一个 userId，不要直接传 User 实体；
4. 避免使用Map，Json这些复杂对象作为参数和结果；
5. 参数个数尽量不超过 5 个，如果参数过多，考虑封装成一个对象。

6.2 方法长度
1. 理想情况下，方法的长度应控制在 20 - 50 行代码，行数不超过 100 ，宽度不超过 120。
2. 对于复杂的逻辑，可以分解为多个私有辅助方法，每个辅助方法专注于一个特定的子任务。

6.3 方法逻辑
1. 遵循单一职责原则，一个方法只做一件事情，并把它做好。
2. 避免过度嵌套的控制结构，如超过 3 层的if-else或嵌套的循环。
3. 保持逻辑清晰，使用临时变量来存储中间结果，提高代码的可读性。
4. 合理的利用换行符，防止大片代码放一起，增加理解成本（同理，每个类字段中间需要换行）。

6.4 返回值
1. 明确方法的返回值类型，确保返回值与方法的预期功能一致。
2. 对于返回集合或数组的方法，确保在返回前不为null，如果没有数据，返回空集合或数组。
3. 如果方法可能返回null，最好注释说明，并在调用方进行适当的空值检查。

6.5 方法可见性
合理设置方法的可见性，遵循最小暴露原则，遵循 static，public，protect，包内可见，private 顺序排列
1. public方法：用于提供给外部类或模块调用的重要功能。
2. protected方法：用于被子类继承和重写的方法。
3. private方法：仅在当前类内部使用的辅助方法。



七、特殊规范
此规范主要针对切片平台项目
1.  controller 校验统一使用jakarta.validation相关方法

以下是通用规范
一、通用代码规范
严格遵循 SOLID、DRY、KISS、YAGNI 原则
遵循 OWASP 安全最佳实践（如输入验证、SQL注入防护）
采用 分层架构设计，确保职责分离
二、技术栈规范
技术栈要求
框架：Spring Boot 3.1.2 + Java 21 + dubbo 3.3.0-beta.1
依赖：
核心：Spring Web, mybatis, Lombok, spring jdbc
数据库：mysql,clickhouse 或其他关系型数据库驱动
三、应用逻辑设计规范
1. 分层架构原则
   层级	职责	约束条件
   Controller	处理 HTTP 请求与响应，定义 API 接口	- 禁止直接操作数据库
- 必须通过 Service 层调用
  Service	业务逻辑实现，事务管理，数据校验	- 必须通过 Repository 访问数据库
- 返回 DTO 而非实体类（除非必要）
  Repository	数据持久化操作，定义数据库查询逻辑	- 必须继承 JpaRepository
- 使用 @EntityGraph 避免 N+1 查询问题
  Entity	数据库表结构映射对象	- 仅用于数据库交互
- 禁止直接返回给前端（需通过 DTO 转换）
  四、核心代码规范
1. 实体类（Entity）规范
   @Entity
   @Data // Lombok 注解
   public class User {

   @NotBlank(message = "用户名不能为空")
   @Size(min = 3, max = 50)
   private String username;


2. 数据访问层（Repository）规范
   参考qos-batch/qos-batch-task/src/main/java/com/chinaunicom/qos/batch/task/infrastructure/dal包
3. 服务层（Service）规范
   @Service
   public class UserServiceImpl implements UserService {
   @Autowired
   private UserRepository userRepository;

   @Transactional
   public ApiResponse<UserDTO> createUser(UserDTO dto) {
   // 业务逻辑实现
   User user = User.builder().username(dto.getUsername()).build();
   User savedUser = userRepository.save(user);
   return ApiResponse.success(UserDTO.fromEntity(savedUser));
   }
   }
4. 控制器（RestController）规范
   @RestController
   @RequestMapping("/api/users")
   public class UserController {
   @Autowired
   private UserService userService;

   @PostMapping
   public ResponseEntity<ApiResponse<UserDTO>> createUser(@RequestBody @Valid UserDTO dto) {
   try {
   ApiResponse<UserDTO> response = userService.createUser(dto);
   return ResponseEntity.ok(response);
   } catch (Exception e) {
   return GlobalExceptionHandler.errorResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
   }
   }
   }
   五、数据传输对象（DTO）规范
   // 使用 record 或 @Data 注解
   public record UserDTO(
   @NotBlank String username,
   @Email String email
   ) {
   public static UserDTO fromEntity(User entity) {
   return new UserDTO(entity.getUsername(), entity.getEmail());
   }
   }
   六、全局异常处理规范
1. 统一响应类（ApiResponse）
   @Data
   @NoArgsConstructor
   @AllArgsConstructor
   public class ApiResponse<T> {
   private String result; // SUCCESS/ERROR
   private String message;
   private T data;

   // 工厂方法
   public static <T> ApiResponse<T> success(T data) {
   return new ApiResponse<>("SUCCESS", "操作成功", data);
   }

   public static <T> ApiResponse<T> error(String message) {
   return new ApiResponse<>("ERROR", message, null);
   }
   }
2. 全局异常处理器（GlobalExceptionHandler）
   @RestControllerAdvice
   public class GlobalExceptionHandler {
   @ExceptionHandler(EntityNotFoundException.class)
   public ResponseEntity<ApiResponse<?>> handleEntityNotFound(EntityNotFoundException ex) {
   return ResponseEntity.status(HttpStatus.NOT_FOUND)
   .body(ApiResponse.error(ex.getMessage()));
   }

   @ExceptionHandler(MethodArgumentNotValidException.class)
   public ResponseEntity<ApiResponse<?>> handleValidationErrors(MethodArgumentNotValidException ex) {
   String errorMessage = ex.getBindingResult()
   .getFieldErrors()
   .stream()
   .map(error -> error.getField() + ": " + error.getDefaultMessage())
   .collect(Collectors.joining(", "));
   return ResponseEntity.badRequest().body(ApiResponse.error(errorMessage));
   }
   }
3. 类注释使用如下样例格式
   /**
   * IOM->PCC 审批流程和数据同步流程
   *
   * <AUTHOR>
   * @date 2024/12/4 17:14
   */
4. 方法注释使用如下样例格式

   

   七、安全与性能规范
   输入校验：
   使用 @Valid 注解 + JSR-303 校验注解（如 @NotBlank, @Size）
   禁止直接拼接 SQL 防止注入攻击
   事务管理：
   @Transactional 注解仅标注在 Service 方法上
   避免在循环中频繁提交事务
   性能优化：
   使用 @EntityGraph 预加载关联关系
   避免在循环中执行数据库查询（批量操作优先）
   八、代码风格规范
   命名规范：
   类名：UpperCamelCase（如 UserServiceImpl）
   方法/变量名：lowerCamelCase（如 saveUser）
   常量：UPPER_SNAKE_CASE（如 MAX_LOGIN_ATTEMPTS）
   注释规范：
   方法必须添加注释且方法级注释使用 Javadoc 格式
   计划待完成的任务需要添加 // TODO 标记
   存在潜在缺陷的逻辑需要添加 // FIXME 标记
   代码格式化：
   使用 IntelliJ IDEA 默认的 Spring Boot 风格
   禁止手动修改代码缩进（依赖 IDE 自动格式化）
   九、部署规范
   部署规范：
   生产环境需禁用 @EnableAutoConfiguration 的默认配置
   敏感信息通过 application.properties 外部化配置
   使用 Spring Profiles 管理环境差异（如 dev, prod）
   十、扩展性设计规范
   接口优先：
   服务层接口（UserService）与实现（UserServiceImpl）分离
   扩展点预留：
   关键业务逻辑需提供 Strategy 或 Template 模式支持扩展
   日志规范：
   使用 SLF4J 记录日志（禁止直接使用 System.out.println）
   核心操作需记录 INFO 级别日志，异常记录 ERROR 级别
以上是通用代码规范

