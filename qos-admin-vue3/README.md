# qos-admin-vue3

基于 Vue 3 + TypeScript + Vite + pnpm 的管理后台前端项目。

## 项目简介

本项目为 slice-qos 平台的管理后台，提供可视化操作界面，支持多环境部署，具备良好的可维护性和扩展性。**本项目强制使用 pnpm 作为包管理工具，以确保依赖一致性。**

## 技术栈

- [Vue 3](https://vuejs.org/)
- [Vite](https://vitejs.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [pnpm](https://pnpm.io/)
- [Vue Router](https://router.vuejs.org/)
- [Element Plus](https://element-plus.org/)
- 其他见 package.json

## 目录结构

```text
├── .vscode/          # VSCode 编辑器配置
├── public/           # 静态资源
├── src/
│   ├── api/          # 接口请求封装
│   ├── assets/       # 图片、样式等静态资源
│   ├── components/   # 通用组件
│   ├── composables/  # 业务逻辑复用
│   ├── config/       # 配置文件
│   ├── router/       # 路由配置
│   ├── types/        # TypeScript 类型定义
│   ├── utils/        # 工具函数
│   ├── views/        # 页面视图
│   └── App.vue       # 入口组件
├── k8s/              # k8s 部署文件
├── .eslintrc.cjs     # ESLint 配置文件
├── .gitignore        # Git 忽略文件
├── .prettierrc.json  # Prettier 配置文件
├── package.json      # 项目依赖
├── pnpm-lock.yaml    # pnpm 锁定文件
├── tsconfig.json     # TypeScript 配置文件
├── vite.config.ts    # Vite 配置
└── README.md         # 项目说明
```

## 快速开始

### 1. 环境准备

请确保已安装 [Node.js](https://nodejs.org/) (>=18.0.0) 和 [pnpm](https://pnpm.io/installation)。

### 2. 安装依赖

```bash
pnpm install
```

### 3. 本地开发

```bash
pnpm dev
```

### 4. 构建生产包

```bash
pnpm build
```

### 5. 预览生产包

```bash
pnpm preview
```

### 6. 部署

构建后 `dist/` 目录为可部署静态资源。可通过 nginx、k8s 等方式部署，详见 `k8s/` 目录下示例。

## 主要命令

- `pnpm dev`: 启动本地开发服务
- `pnpm build`: 构建生产环境代码
- `pnpm build:test`: 构建测试环境代码
- `pnpm build:prod`: 构建生产环境代码
- `pnpm preview`: 本地预览生产构建产物
- `pnpm lint`: 检查代码规范
- `pnpm format`: 格式化代码

## 代码规范

- **强制使用 ESLint + Prettier 统一代码风格**。
- 提交代码前，请务必执行 `pnpm lint` 并解决所有错误。
- 推荐在 VSCode 中安装 ESLint 插件，并开启保存时自动修复。
- 组件、变量、方法命名遵循 camelCase/PascalCase 规范。
- 业务逻辑建议抽离到 composables，提升复用性。

## 常见问题

- **依赖问题**: 严禁使用 npm 或 yarn，必须使用 pnpm。如遇依赖问题，请尝试执行 `rm -rf node_modules && pnpm install`。
- **端口冲突**: 修改 `vite.config.ts` 中的 `server.port`。
- **其他问题**: 请提 issue 或联系维护者。

## 贡献指南

欢迎提 issue 或 PR，建议先沟通需求和方案。

## 维护者

- Jerry4Pan（<EMAIL>）

---

> 本项目为公司内部项目，禁止外泄。发现问题请及时反馈，别等出事了才来找人背锅！
