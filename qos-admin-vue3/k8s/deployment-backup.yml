apiVersion: apps/v1
kind: Deployment
metadata:
  name: qos-admin-vue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qos-admin-vue
  template:
    metadata:
      labels:
        app: qos-admin-vue
    spec:
      containers:
        - name: qos-admin-vue
          image: 'vpc-zhongyuan-registry.cucloud.cn/qos/qos-admin-vue:#{Build.BuildNumber}#'
          imagePullPolicy: Always
          resources:
            limits:
              cpu: 5
              memory: 10G
            requests:
              cpu: 1
              memory: 1G
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: docker-secret