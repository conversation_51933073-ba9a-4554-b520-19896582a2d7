<template>
  <div class="login-container">
    <!-- 背景装饰组件 -->
    <BackgroundDecoration />

    <div class="login-box">
      <!-- 步骤指示器 -->
      <div class="step-indicator">
        <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <span class="step-number">1</span>
          <span class="step-text">身份验证</span>
        </div>
        <div class="step-line" :class="{ completed: currentStep > 1 }"></div>
        <div class="step" :class="{ active: currentStep >= 2 }">
          <span class="step-number">2</span>
          <span class="step-text">短信验证</span>
        </div>
      </div>

      <div class="login-header">
        <img src="../../assets/logo.svg" alt="Logo" class="logo" />
        <h1 class="title">动态编排底座管理后台</h1>
        <p class="subtitle">
          {{ currentStep === 1 ? '请输入您的登录信息' : '请输入短信验证码' }}
        </p>
      </div>

      <!-- 第一步：用户名密码 + 图形验证码 -->
      <transition name="slide" mode="out-in">
        <el-form
          v-if="currentStep === 1"
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          key="step1"
        >
          <el-form-item prop="userName">
            <el-input
              v-model="loginForm.userName"
              placeholder="用户名"
              prefix-icon="User"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              prefix-icon="Lock"
              size="large"
              show-password
              @keyup.enter="handleFirstStep"
            />
          </el-form-item>

          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="图形验证码"
                prefix-icon="Picture"
                size="large"
                style="flex: 1; margin-right: 10px"
                @keyup.enter="handleFirstStep"
              />
              <div class="captcha-wrapper">
                <img
                  :src="captchaUrl"
                  alt="验证码"
                  class="captcha-image"
                  @click="refreshCaptcha"
                  title="点击刷新验证码"
                />
              </div>
            </div>
          </el-form-item>

          <div class="login-options">
            <el-checkbox v-model="loginForm.remember" class="remember-checkbox">
              记住我
            </el-checkbox>
          </div>

          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              size="large"
              class="login-button"
              @click="handleFirstStep"
            >
              下一步
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 第二步：短信验证码 -->
        <el-form
          v-else-if="currentStep === 2"
          ref="smsFormRef"
          :model="smsForm"
          :rules="smsRules"
          class="login-form"
          key="step2"
        >
          <div class="sms-info">
            <el-icon class="info-icon"><Message /></el-icon>
            <p>短信验证码将发送至：<strong>{{ maskedPhone }}</strong></p>
            <p class="tip">请先点击发送验证码，然后在5分钟内输入验证码</p>
          </div>

          <el-form-item prop="smsCode">
            <div class="sms-input-container">
              <el-input
                v-model="smsForm.smsCode"
                placeholder="请输入6位短信验证码"
                prefix-icon="Message"
                size="large"
                maxlength="6"
                @keyup.enter="handleSecondStep"
                class="sms-input"
              />
              <el-button
                :disabled="countdown > 0"
                @click="sendSms"
                class="send-sms-btn-inline"
                :loading="smsLoading"
                type="primary"
              >
                {{ countdown > 0 ? `${countdown}s` : '发送' }}
              </el-button>
            </div>
          </el-form-item>

          <div class="button-group">
            <el-form-item class="main-button-item">
              <el-button
                :loading="loading"
                type="primary"
                size="large"
                class="login-button"
                @click="handleSecondStep"
              >
                登录
              </el-button>
            </el-form-item>

            <div class="secondary-actions">
              <el-button
                class="back-button-text"
                @click="backToFirstStep"
                text
              >
                <el-icon><ArrowLeft /></el-icon>
                返回上一步
              </el-button>
            </div>
          </div>
        </el-form>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts" name="Login">
import { ElForm, ElMessage, ElIcon } from 'element-plus'
import { Message, ArrowLeft } from '@element-plus/icons-vue'
import { reactive, ref, onMounted, onUnmounted } from 'vue'
import useAuth from '../../composables/useAuth'
import { sendSmsCode, getCaptcha } from '../../api/auth'
import BackgroundDecoration from '../../components/BackgroundDecoration.vue'

// 当前步骤
const currentStep = ref(1)

// 表单ref
const loginFormRef = ref<InstanceType<typeof ElForm>>()
const smsFormRef = ref<InstanceType<typeof ElForm>>()

// 第一步表单数据
const loginForm = reactive({
  userName: '',
  password: '',
  captcha: '',
  sessionId: '',
  remember: false,
})

// 第二步表单数据
const smsForm = reactive({
  smsCode: '',
})

// 验证码图片URL和脱敏手机号
const captchaUrl = ref('')
const maskedPhone = ref('')

// 倒计时和短信状态
const countdown = ref(0)
const smsLoading = ref(false)
const smsSent = ref(false)
let countdownTimer: NodeJS.Timeout | null = null

const captchaRefreshTime = ref(0)
const CAPTCHA_REFRESH_INTERVAL = 1000

let refreshTimer: NodeJS.Timeout | null = null
const loginRules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度应为2-20个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' },
  ],
  captcha: [
    { required: true, message: '请输入图形验证码', trigger: 'blur' },
    { len: 4, message: '请输入4位验证码', trigger: 'blur' },
  ],
}

const smsRules = {
  smsCode: [
    { required: true, message: '请输入短信验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
  ],
}

// 使用认证hook
const { login, loading } = useAuth()

// 生成会话ID
const generateSessionId = () => {
  return `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`
}

const refreshCaptcha = () => {
  const now = Date.now()
  if (now - captchaRefreshTime.value < CAPTCHA_REFRESH_INTERVAL) {
    ElMessage.warning('请勿频繁刷新验证码')
    return
  }

  if (refreshTimer) {
    clearTimeout(refreshTimer)
  }

  refreshTimer = setTimeout(() => {
    captchaRefreshTime.value = now
    loginForm.sessionId = generateSessionId()
    captchaUrl.value = getCaptcha(loginForm.sessionId)
  }, 100)
}

// 第一步：用户名密码验证
const handleFirstStep = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const result = await login({
          userName: loginForm.userName,
          password: loginForm.password,
          captcha: loginForm.captcha,
          sessionId: loginForm.sessionId,
        })

        if ('maskedPhone' in result) {
          maskedPhone.value = result.maskedPhone
          currentStep.value = 2
          smsSent.value = false // 重置短信发送状态

          ElMessage.success('验证通过，请点击发送验证码')
        }
      } catch (error: any) {
        console.debug('登录第一步失败:', error.message || error)
        refreshCaptcha()
      }
    }
  })
}

// 发送短信验证码
const sendSms = async () => {
  if (countdown.value > 0) return

  try {
    smsLoading.value = true
    await sendSmsCode({ userName: loginForm.userName })
    smsSent.value = true
    startCountdown()
    ElMessage.success('验证码发送成功')
  } catch (error: any) {
    // 如果错误已在拦截器中处理，不再重复显示
    if (!error.isHandled) {
      ElMessage.error(error.message || '发送失败')
    }
  } finally {
    smsLoading.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)
}

// 第二步：短信验证码验证
const handleSecondStep = async () => {
  if (!smsFormRef.value) return

  await smsFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await login({
          userName: loginForm.userName,
          smsCode: smsForm.smsCode,
          remember: loginForm.remember,
        })
      } catch (error: any) {
        // 错误处理已在useAuth中完成，这里记录调试信息
        console.debug('登录第二步失败:', error.message || error)
      }
    }
  })
}

// 返回第一步
const backToFirstStep = () => {
  currentStep.value = 1
  smsForm.smsCode = ''
  smsSent.value = false
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
    countdown.value = 0
  }
  refreshCaptcha()
}

// 组件挂载时初始化
onMounted(() => {
  refreshCaptcha()
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  if (refreshTimer) {
    clearTimeout(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #7c3aed;
  --success-color: #10b981;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-glass: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center; /* 恢复垂直居中，让大屏登录框居中 */
  min-height: 100vh;
  padding: 20px;
  position: relative;
  overflow: auto;
  box-sizing: border-box;
}

.login-box {
  width: 100%;
  max-width: 480px;
  padding: 48px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--shadow-glass);
  position: relative;
  animation: slideInUp 0.8s ease-out;
}

.login-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  border-radius: 24px 24px 0 0;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
}

.step.active {
  color: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

.step.completed {
  color: rgba(16, 185, 129, 0.9);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.step-number::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 50%;
}

.step.active .step-number {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.step.active .step-number::before {
  opacity: 1;
}

.step.completed .step-number {
  background: linear-gradient(135deg, var(--success-color), #059669);
  border-color: var(--success-color);
  color: white;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.step-text {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.step-line {
  width: 80px;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 24px;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.step-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, var(--success-color), #059669);
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
}

.step-line.completed::before {
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeInDown 0.8s ease-out 0.2s both;
}

.logo {
  height: 72px;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 12px rgba(79, 70, 229, 0.3));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  margin: 0 0 12px 0;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.3px;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 动画效果 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(40px) scale(0.95);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-40px) scale(0.95);
}

/* 表单样式优化 */
.login-form {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input) {
  --el-input-height: 56px;
  --el-input-border-radius: 12px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(255, 255, 255, 0.2);
  --el-input-hover-border-color: rgba(79, 70, 229, 0.6);
  --el-input-focus-border-color: #4f46e5;
  --el-input-text-color: rgba(255, 255, 255, 0.9);
  --el-input-placeholder-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-input__wrapper) {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
  transform: translateY(-1px);
}

/* 聚焦状态的输入框样式 */
.login-box .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.25) !important;
  transform: translateY(-2px) !important;
}

:deep(.el-input__inner) {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

:deep(.el-input__prefix-inner) {
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px;
}

.captcha-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.captcha-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.captcha-image {
  width: 140px;
  height: 56px;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  object-fit: cover;
}

.captcha-image:hover {
  border-color: rgba(79, 70, 229, 0.6);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
  transform: translateY(-1px);
}

.refresh-btn {
  font-size: 12px;
  padding: 0;
  color: rgba(255, 255, 255, 0.7);
}

.sms-info {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(79, 70, 229, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(79, 70, 229, 0.2);
  position: relative;
  overflow: hidden;
}

.sms-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.info-icon {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.3));
}

.sms-info p {
  margin: 8px 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.sms-info .tip {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* 短信验证码输入框容器 */
.sms-input-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.sms-input {
  flex: 1;
}

/* 内联发送按钮 */
:deep(.send-sms-btn-inline) {
  height: 56px !important;
  padding: 0 24px !important;
  font-size: 15px !important;
  font-weight: 600;
  border-radius: 12px !important;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  border: none !important;
  color: white !important;
  letter-spacing: 0.3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  min-width: 90px !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
}

:deep(.send-sms-btn-inline:hover:not(:disabled)) {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4) !important;
}

:deep(.send-sms-btn-inline:disabled) {
  background: rgba(255, 255, 255, 0.15) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  opacity: 0.7 !important;
}

:deep(.send-sms-btn-inline:active:not(:disabled)) {
  transform: translateY(0) !important;
}

/* 按钮组布局 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 32px;
}

.main-button-item {
  margin-bottom: 0;
}

.secondary-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-options {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 32px;
}

/* 简洁优雅的 Checkbox 样式 */
:deep(.remember-checkbox) {
  --el-checkbox-text-color: rgba(255, 255, 255, 0.8);
  --el-checkbox-input-border-color: rgba(255, 255, 255, 0.3);
  --el-checkbox-checked-bg-color: #4f46e5;
  --el-checkbox-checked-input-border-color: #4f46e5;
  transition: all 0.3s ease;
}

:deep(.remember-checkbox:hover) {
  --el-checkbox-text-color: rgba(255, 255, 255, 0.95);
  --el-checkbox-input-border-color: rgba(255, 255, 255, 0.5);
}

:deep(.remember-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
  animation: checkboxPulse 0.3s ease;
}

:deep(.remember-checkbox .el-checkbox__inner) {
  width: 18px;
  height: 18px;
  border-width: 2px;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

:deep(.remember-checkbox .el-checkbox__inner:hover) {
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

:deep(.remember-checkbox .el-checkbox__inner::after) {
  border-width: 2px;
  transition: all 0.2s ease;
}

:deep(.remember-checkbox .el-checkbox__label) {
  font-weight: 500;
  font-size: 15px;
  letter-spacing: 0.3px;
  margin-left: 12px;
  user-select: none;
  cursor: pointer;
}



@keyframes checkboxPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.login-button,
.back-button {
  width: 100%;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.login-button {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  color: white;
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(79, 70, 229, 0.4);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(0);
}

.back-button {
  margin-top: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 文本样式的返回按钮 */
.back-button-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  letter-spacing: 0.3px;
}

.back-button-text:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(-2px);
}

.back-button-text .el-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.back-button-text:hover .el-icon {
  transform: translateX(-2px);
}












/* 🎯 一屏适配的响应式设计 - 确保内容完整显示 */

/* 🖥️ 大屏幕 - 完美居中显示 */
@media (min-width: 1200px) {
  .login-container {
    /* 保持垂直居中，不需要额外的padding-top */
    align-items: center !important;
    padding: 40px 20px !important; /* 均匀的上下内边距 */
  }

  .login-box {
    max-width: 450px !important;
    padding: 48px !important; /* 大屏幕保持舒适的内边距 */
  }
}

/* 🎯 笔记本屏幕专门优化 - 基于屏幕高度判断 */
@media (max-height: 800px) and (min-width: 769px) {
  .login-container {
    padding-top: 15px !important; /* 很小的上方留白 */
    padding-bottom: 15px !important;
  }

  .login-box {
    max-width: 380px !important;
    padding: 20px !important; /* 大幅减少内边距 */
  }

  /* 缩小logo */
  .logo {
    height: 45px !important;
    margin-bottom: 12px !important;
  }

  /* 缩小标题 */
  .title {
    font-size: 22px !important;
    margin-bottom: 6px !important;
  }

  .subtitle {
    font-size: 12px !important;
    margin-bottom: 12px !important;
  }

  /* 缩小步骤指示器 */
  .step-indicator {
    margin-bottom: 12px !important;
  }

  .step-number {
    width: 32px !important;
    height: 32px !important;
    font-size: 12px !important;
    margin-bottom: 4px !important;
  }

  .step-line {
    width: 50px !important;
    margin: 0 12px !important;
  }

  .step-text {
    font-size: 11px !important;
  }

  /* 缩小输入框 */
  :deep(.el-input) {
    --el-input-height: 40px !important;
  }

  :deep(.el-form-item) {
    margin-bottom: 10px !important;
  }



  :deep(.el-form-item__label) {
    margin-bottom: 4px !important;
  }

  /* 缩小按钮 */
  .login-button,
  .back-button {
    height: 40px !important;
    font-size: 13px !important;
    margin-bottom: 8px !important;
  }

  :deep(.send-sms-btn-inline) {
    height: 40px !important;
    min-width: 70px !important;
    padding: 0 12px !important;
    font-size: 11px !important;
  }

  /* 缩小验证码 */
  .captcha-image {
    width: 90px !important;
    height: 40px !important;
  }

  /* 减少其他间距 */
  .login-options {
    margin-bottom: 12px !important;
  }

  /* 🎯 短信验证页面专门优化 */
  .sms-info {
    margin-bottom: 16px !important; /* 从32px减少到16px */
    padding: 7px !important; /* 从24px减少到12px */
  }

  .sms-info p {
    margin: 4px 0 !important; /* 从8px减少到4px */
    font-size: 12px !important; /* 缩小字体 */
    line-height: 1.3 !important; /* 紧凑行高 */
  }

  .sms-info .tip {
    font-size: 11px !important; /* 从14px减少到11px */
    margin-top: 2px !important;
  }

  .sms-info .info-icon {
    font-size: 16px !important; /* 缩小图标 */
    margin-bottom: 6px !important;
  }

  /* 短信输入框容器间距优化 */
  .sms-input-container {
    gap: 8px !important; /* 从16px减少到8px */
    margin-bottom: 8px !important; /* 减少与下方按钮的间距 */
  }

  /* 🎯 按钮间距优化 - 解决间距太大问题 */
  .button-group {
    gap: 0 !important; /* 进一步减少到0，让按钮更紧凑 */
    margin-top: 10px !important; /* 从32px减少到10px */
  }

  /* 调整返回按钮容器的间距 */
  .secondary-actions {
    margin-top: 2px !important; /* 减少与登录按钮的间距 */
  }

  .back-button-text {
    margin: 0 !important; /* 移除按钮自身的margin */
    padding: 4px 8px !important; /* 减少按钮内边距 */
  }


}

/* 手机端 - 极致空间优化，确保一屏完整显示 */
@media (max-width: 768px) {
  .login-container {
    padding: 20px 16px 16px 16px;
  }

  .login-box {
    max-width: 100%;
    padding: 20px 16px;
    border-radius: 12px;
    animation: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* 紧凑logo */
  .logo {
    height: 45px;
    margin-bottom: 12px;
  }

  /* 紧凑标题文字 */
  .title {
    font-size: 18px;
    margin-bottom: 6px;
    line-height: 1.2;
  }

  .subtitle {
    font-size: 12px;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  /* 紧凑步骤指示器 */
  .step-indicator {
    margin-bottom: 18px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin-bottom: 6px;
  }

  .step-line {
    width: 40px;
    margin: 0 8px;
  }

  .step-text {
    font-size: 10px;
  }

  /* 紧凑输入框 */
  :deep(.el-input) {
    --el-input-height: 40px;
  }

  :deep(.el-input__wrapper) {
    padding: 0 12px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }



  :deep(.el-form-item__label) {
    font-size: 13px;
    margin-bottom: 4px;
  }

  /* 紧凑按钮 */
  .login-button,
  .back-button {
    height: 40px;
    font-size: 13px;
    margin-bottom: 12px;
  }

  :deep(.send-sms-btn-inline) {
    height: 40px;
    min-width: 70px;
    padding: 0 12px;
    font-size: 11px;
  }

  /* 紧凑验证码图片 */
  .captcha-image {
    width: 90px;
    height: 40px;
  }

  /* 最小化其他间距 */
  .login-options {
    margin-bottom: 16px;
  }

  :deep(.remember-checkbox .el-checkbox__label) {
    font-size: 11px;
  }

  /* 紧凑错误提示 */
  :deep(.el-form-item__error) {
    font-size: 11px;
    margin-top: 2px;
  }
}

/* 加载状态优化 */
.login-box .el-button.is-loading {
  position: relative;
}

.login-box .el-button.is-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(79, 70, 229, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(79, 70, 229, 0.3);
  color: white;
}

@media (max-width: 480px) {
  .login-form {
    animation: none;
  }

  .slide-enter-active,
  .slide-leave-active {
    transition: none;
  }

  :deep(.el-input) {
    --el-input-height: 48px;
  }
}
</style>
