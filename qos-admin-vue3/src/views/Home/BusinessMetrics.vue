<template>
  <div class="business-metrics-container">
    <!-- 1. 筛选区域 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="dateShortcuts"
            :disabled-date="disabledDate"
            :first-day-of-week="1"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="通信服务">
          <el-popover
            ref="servicePopoverRef"
            placement="bottom-start"
            :width="450"
            trigger="click"
            @show="onPopoverShow"
          >
            <template #reference>
              <div
                class="custom-select-trigger"
                :class="{ 'is-active': isPopoverVisible }"
              >
                <div class="trigger-content">
                  <template
                    v-if="
                      !queryParams.communicationServices ||
                      queryParams.communicationServices.length === 0
                    "
                  >
                    <span class="placeholder">全部</span>
                  </template>
                  <template v-else>
                    <el-tag
                      v-if="queryParams.communicationServices.length === 1"
                      size="small"
                      type="info"
                      closable
                      @close.stop="
                        clearSingleService(selectedServiceNames[0]?.value)
                      "
                    >
                      {{ selectedServiceNames[0]?.label }}
                    </el-tag>
                    <el-tag v-else size="small" type="info">
                      已选择 {{ queryParams.communicationServices.length }} 项
                    </el-tag>
                  </template>
                </div>
                <el-icon
                  class="arrow-icon"
                  :class="{ 'is-reverse': isPopoverVisible }"
                  ><ArrowDown
                /></el-icon>
              </div>
            </template>
            <div class="service-select-popover-content">
              <el-input
                v-model="popoverSearchTerm"
                placeholder="搜索服务名称或ID"
                clearable
                class="popover-search-input"
              />
              <el-tabs v-model="popoverActiveTab" class="popover-tabs">
                <el-tab-pane
                  v-for="group in popoverServiceGroups"
                  :key="group.key"
                  :label="`${group.label} (${group.filteredCount})`"
                  :name="group.key"
                >
                  <el-scrollbar
                    max-height="200px"
                    class="service-list-scrollbar"
                  >
                    <el-checkbox
                      v-if="group.services.length > 0"
                      :model-value="group.isAllSelected"
                      :indeterminate="group.isIndeterminate"
                      class="select-all-checkbox"
                      @change="handleSelectAll(group.key, $event)"
                    >
                      全选 ({{ group.selectedCount }} /
                      {{ group.filteredCount }})
                    </el-checkbox>
                    <el-checkbox-group v-model="tempSelectedServices">
                      <el-checkbox
                        v-for="service in group.services"
                        :key="service.cmServiceId"
                        :value="service.cmServiceId"
                        class="service-checkbox"
                      >
                        {{ formatServiceName(service.cmServiceName) }}
                      </el-checkbox>
                    </el-checkbox-group>
                    <el-empty
                      v-if="group.services.length === 0"
                      :image-size="60"
                      description="无匹配数据"
                    />
                  </el-scrollbar>
                </el-tab-pane>
              </el-tabs>
              <div class="popover-footer">
                <el-button @click="confirmSelection(false)">清空</el-button>
                <el-button type="primary" @click="confirmSelection(true)"
                  >确定</el-button
                >
              </div>
            </div>
          </el-popover>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleQuery"
            >查询</el-button
          >
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 3. 标签页与数据表格 -->
    <el-card class="content-card" shadow="never">
      <el-tabs
        v-model="activeTab"
        class="content-tabs"
        @tab-change="handleTabChange"
      >
        <el-tab-pane label="指标总览" name="overview">
          <!-- KPI卡片 -->
          <el-row :gutter="20" class="kpi-cards">
            <el-col :span="8">
              <div class="kpi-card kpi-card-blue">
                <div class="kpi-icon-wrapper">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="kpi-content">
                  <div class="kpi-title">2C商用业务接入成功率 (周期均值)</div>
                  <div class="kpi-value">
                    {{ kpiDataAverages.overallSuccessRate }}
                    <span
                      v-if="kpiDataAverages.overallSuccessRate !== '-'"
                      class="kpi-unit"
                      >%</span
                    >
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="kpi-card kpi-card-green">
                <div class="kpi-icon-wrapper">
                  <el-icon><SuccessFilled /></el-icon>
                </div>
                <div class="kpi-content">
                  <div class="kpi-title">专载请求成功率 (周期均值)</div>
                  <div class="kpi-value">
                    {{ kpiDataAverages.dedbearerSuccessRate }}
                    <span
                      v-if="kpiDataAverages.dedbearerSuccessRate !== '-'"
                      class="kpi-unit"
                      >%</span
                    >
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="kpi-card kpi-card-orange">
                <div class="kpi-icon-wrapper">
                  <el-icon><Finished /></el-icon>
                </div>
                <div class="kpi-content">
                  <div class="kpi-title">默载请求成功率 (周期均值)</div>
                  <div class="kpi-value">
                    {{ kpiDataAverages.defbearerSuccessRate }}
                    <span
                      v-if="kpiDataAverages.defbearerSuccessRate !== '-'"
                      class="kpi-unit"
                      >%</span
                    >
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div id="trend-chart" style="width: 100%; height: 400px"></div>
          <el-divider />

          <div class="sub-nav-container">
            <el-radio-group v-model="overviewSubTab" size="large">
              <el-radio-button label="all">总览</el-radio-button>
              <el-radio-button label="dedbearer">专载指标详情</el-radio-button>
              <el-radio-button label="defbearer">默载指标详情</el-radio-button>
            </el-radio-group>
          </div>

          <el-table
            v-show="overviewSubTab === 'all'"
            v-loading="loading"
            :data="pivotedAllOverviewData.rows"
            border
            style="width: 100%"
            row-key="id"
            :cell-style="getPercentageCellStyle"
            class="sub-tab-content"
          >
            <el-table-column
              prop="metricName"
              label="指标名称"
              fixed
              width="250"
            />
            <el-table-column
              v-for="date in pivotedAllOverviewData.headers"
              :key="date"
              :prop="date"
              :label="formatDateForHeader(date)"
              width="120"
              align="center"
            />
          </el-table>
          <el-table
            v-show="overviewSubTab === 'dedbearer'"
            v-loading="loading"
            :data="pivotedDedbearerOverviewData.rows"
            style="width: 100%"
            row-key="id"
            border
            :span-method="objectSpanMethod"
            :row-style="getRowStyle"
            :cell-style="getPercentageCellStyle"
            class="sub-tab-content"
          >
            <el-table-column prop="groupName" label="分类" fixed width="150" />
            <el-table-column
              prop="metricName"
              label="指标名称"
              fixed
              width="200"
            >
              <template #default="{ row }">
                <a
                  href="#"
                  class="metric-link"
                  @click.prevent="showTrendChart(row, 'dedbearer_overview')"
                  >{{ row.metricName }}</a
                >
              </template>
            </el-table-column>
            <el-table-column
              v-for="date in pivotedDedbearerOverviewData.headers"
              :key="date"
              :prop="date"
              :label="formatDateForHeader(date)"
              width="120"
              align="center"
            >
              <template #default="{ row, column }">
                <div
                  v-if="
                    pivotedDedbearerOverviewData.meta.maxValues[column.property]
                  "
                  class="data-bar-container"
                >
                  <div
                    class="data-bar"
                    :style="{
                      width: `${(parseFloat(row[column.property]) / pivotedDedbearerOverviewData.meta.maxValues[column.property]) * 100}%`,
                    }"
                  ></div>
                  <span class="data-bar-value">{{ row[column.property] }}</span>
                </div>
                <span v-else>{{ row[column.property] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            v-show="overviewSubTab === 'defbearer'"
            v-loading="loading"
            :data="pivotedDefbearerOverviewData.rows"
            style="width: 100%"
            row-key="id"
            border
            :span-method="objectSpanMethod"
            :row-style="getRowStyle"
            :cell-style="getPercentageCellStyle"
            class="sub-tab-content"
          >
            <el-table-column prop="groupName" label="分类" fixed width="150" />
            <el-table-column
              prop="metricName"
              label="指标名称"
              fixed
              width="200"
            >
              <template #default="{ row }">
                <a
                  href="#"
                  class="metric-link"
                  @click.prevent="showTrendChart(row, 'defbearer_overview')"
                  >{{ row.metricName }}</a
                >
              </template>
            </el-table-column>
            <el-table-column
              v-for="date in pivotedDefbearerOverviewData.headers"
              :key="date"
              :prop="date"
              :label="formatDateForHeader(date)"
              width="120"
              align="center"
            />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="按服务分类" name="byService">
          <div class="sub-nav-container">
            <el-radio-group v-model="byServiceTab" size="large">
              <el-radio-button label="dedbearer">专载</el-radio-button>
              <el-radio-button label="defbearer">默载</el-radio-button>
            </el-radio-group>
          </div>

          <el-row
            v-show="byServiceTab === 'dedbearer'"
            :gutter="20"
            class="service-layout sub-tab-content"
          >
            <el-col
              v-show="isServiceSelectorVisible"
              :span="6"
              class="service-selector"
            >
              <div class="service-selector-header">
                <el-button
                  text
                  circle
                  @click="isServiceSelectorVisible = !isServiceSelectorVisible"
                >
                  <el-icon :size="20"
                    ><component :is="isServiceSelectorVisible ? Fold : Expand"
                  /></el-icon>
                </el-button>
                <div class="search-and-sort-area">
                  <el-input
                    v-model="dedServiceSearch"
                    placeholder="搜索服务名称或编码..."
                    clearable
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <div class="sort-controls-v2">
                    <div
                      class="sort-key"
                      :class="{ active: dedSortBy === 'cmServiceName' }"
                      @click="setSort('ded', 'cmServiceName')"
                    >
                      <span>名称</span>
                      <el-icon v-if="dedSortBy === 'cmServiceName'"
                        ><component
                          :is="dedSortOrder === 'asc' ? SortUp : SortDown"
                      /></el-icon>
                    </div>
                    <div
                      class="sort-key"
                      :class="{ active: dedSortBy === 'percentage' }"
                      @click="setSort('ded', 'percentage')"
                    >
                      <span>占比</span>
                      <el-icon v-if="dedSortBy === 'percentage'"
                        ><component
                          :is="dedSortOrder === 'asc' ? SortUp : SortDown"
                      /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
              <el-menu
                :default-active="activeDedServiceId"
                class="service-menu"
                @select="handleDedServiceSelect"
              >
                <el-menu-item
                  v-for="service in filteredDedServicesWithStats"
                  :key="service.cmServiceId"
                  :index="service.cmServiceId"
                >
                  <div class="service-item-content">
                    <span
                      class="service-name"
                      :title="formatServiceName(service.cmServiceName)"
                      >{{ formatServiceName(service.cmServiceName) }}</span
                    >
                    <el-tooltip
                      placement="right"
                      :disabled="!service.totalRequests"
                    >
                      <template #content>
                        占比: {{ service.percentage.toFixed(2) }}%<br />
                        分子: {{ service.totalRequests.toLocaleString() }}<br />
                        分母: {{ service.grandTotal.toLocaleString() }}
                      </template>
                      <div class="progress-ring-container">
                        <div
                          class="progress-ring"
                          :style="{
                            background: `conic-gradient(#409eff ${service.percentage}%, #f2f2f2 0)`,
                          }"
                        ></div>
                      </div>
                    </el-tooltip>
                  </div>
                </el-menu-item>
              </el-menu>
            </el-col>
            <el-col
              :span="isServiceSelectorVisible ? 18 : 24"
              class="service-details"
            >
              <div class="service-details-header">
                <el-button
                  v-show="!isServiceSelectorVisible"
                  text
                  circle
                  @click="isServiceSelectorVisible = true"
                >
                  <el-icon :size="20"><Expand /></el-icon>
                </el-button>
                <h4 v-if="selectedDedService" class="service-detail-title">
                  <el-tooltip
                    placement="top"
                    :content="`通信服务编码: ${selectedDedService.cmServiceId}`"
                  >
                    <span
                      class="clickable-service-name"
                      @click="showServiceInfo(selectedDedService.cmServiceId)"
                    >
                      {{ formatServiceName(selectedDedService.cmServiceName) }}
                    </span>
                  </el-tooltip>
                  - 指标详情
                </h4>
              </div>
              <div v-if="selectedDedService" class="table-container">
                <el-table
                  v-loading="loading"
                  :data="selectedDedServiceData.rows"
                  border
                  style="width: 100%"
                  row-key="id"
                  :span-method="objectSpanMethod"
                  :row-style="getRowStyle"
                  :cell-style="getPercentageCellStyle"
                  height="100%"
                >
                  <el-table-column
                    prop="groupName"
                    label="分类"
                    fixed
                    width="150"
                  />
                  <el-table-column
                    prop="metricName"
                    label="指标名称"
                    fixed
                    width="200"
                  >
                    <template #default="{ row }">
                      <a
                        href="#"
                        class="metric-link"
                        @click.prevent="
                          showTrendChart(row, 'dedbearer_service')
                        "
                        >{{ row.metricName }}</a
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-for="date in selectedDedServiceData.headers"
                    :key="date"
                    :prop="date"
                    :label="formatDateForHeader(date)"
                    width="120"
                    align="center"
                  >
                    <template #default="{ row, column }">
                      <el-tooltip
                        :disabled="
                          row[column.property + '_numerator'] === undefined
                        "
                        placement="top"
                      >
                        <template #content>
                          分子:
                          {{
                            row[
                              column.property + '_numerator'
                            ]?.toLocaleString()
                          }}<br />
                          分母:
                          {{
                            row[
                              column.property + '_denominator'
                            ]?.toLocaleString()
                          }}
                        </template>
                        <div>
                          <!-- Wrapper for tooltip target -->
                          <div
                            v-if="
                              selectedDedServiceData.meta.maxValues[
                                column.property
                              ]
                            "
                            class="data-bar-container"
                          >
                            <div
                              class="data-bar"
                              :style="{
                                width: `${(parseFloat(row[column.property]) / selectedDedServiceData.meta.maxValues[column.property]) * 100}%`,
                              }"
                            ></div>
                            <span class="data-bar-value">{{
                              row[column.property]
                            }}</span>
                          </div>
                          <span v-else>{{ row[column.property] }}</span>
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-empty v-else description="请从左侧选择一个服务以查看详情" />
            </el-col>
          </el-row>
          <el-row
            v-show="byServiceTab === 'defbearer'"
            :gutter="20"
            class="service-layout sub-tab-content"
          >
            <el-col
              v-show="isServiceSelectorVisible"
              :span="6"
              class="service-selector"
            >
              <div class="service-selector-header">
                <el-button
                  text
                  circle
                  @click="isServiceSelectorVisible = !isServiceSelectorVisible"
                >
                  <el-icon :size="20"
                    ><component :is="isServiceSelectorVisible ? Fold : Expand"
                  /></el-icon>
                </el-button>
                <div class="search-and-sort-area">
                  <el-input
                    v-model="defServiceSearch"
                    placeholder="搜索服务名称或编码..."
                    clearable
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <div class="sort-controls-v2">
                    <div
                      class="sort-key"
                      :class="{ active: defSortBy === 'cmServiceName' }"
                      @click="setSort('def', 'cmServiceName')"
                    >
                      <span>名称</span>
                      <el-icon v-if="defSortBy === 'cmServiceName'"
                        ><component
                          :is="defSortOrder === 'asc' ? SortUp : SortDown"
                      /></el-icon>
                    </div>
                    <div
                      class="sort-key"
                      :class="{ active: defSortBy === 'percentage' }"
                      @click="setSort('def', 'percentage')"
                    >
                      <span>占比</span>
                      <el-icon v-if="defSortBy === 'percentage'"
                        ><component
                          :is="defSortOrder === 'asc' ? SortUp : SortDown"
                      /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
              <el-menu
                :default-active="activeDefServiceId"
                class="service-menu"
                @select="handleDefServiceSelect"
              >
                <el-menu-item
                  v-for="service in filteredDefServicesWithStats"
                  :key="service.cmServiceId"
                  :index="service.cmServiceId"
                >
                  <div class="service-item-content">
                    <span
                      class="service-name"
                      :title="formatServiceName(service.cmServiceName)"
                      >{{ formatServiceName(service.cmServiceName) }}</span
                    >
                    <el-tooltip
                      placement="right"
                      :disabled="!service.totalRequests"
                    >
                      <template #content>
                        占比: {{ service.percentage.toFixed(2) }}%<br />
                        分子: {{ service.totalRequests.toLocaleString() }}<br />
                        分母: {{ service.grandTotal.toLocaleString() }}
                      </template>
                      <div class="progress-ring-container">
                        <div
                          class="progress-ring"
                          :style="{
                            background: `conic-gradient(#67c23a ${service.percentage}%, #f2f2f2 0)`,
                          }"
                        ></div>
                      </div>
                    </el-tooltip>
                  </div>
                </el-menu-item>
              </el-menu>
            </el-col>
            <el-col
              :span="isServiceSelectorVisible ? 18 : 24"
              class="service-details"
            >
              <div class="service-details-header">
                <el-button
                  v-show="!isServiceSelectorVisible"
                  text
                  circle
                  @click="isServiceSelectorVisible = true"
                >
                  <el-icon :size="20"><Expand /></el-icon>
                </el-button>
                <h4 v-if="selectedDefService" class="service-detail-title">
                  <el-tooltip
                    placement="top"
                    :content="`通信服务编码: ${selectedDefService.cmServiceId}`"
                  >
                    <span
                      class="clickable-service-name"
                      @click="showServiceInfo(selectedDefService.cmServiceId)"
                    >
                      {{ formatServiceName(selectedDefService.cmServiceName) }}
                    </span>
                  </el-tooltip>
                  - 指标详情
                </h4>
              </div>
              <div v-if="selectedDefService" class="table-container">
                <el-table
                  v-loading="loading"
                  :data="selectedDefServiceData.rows"
                  border
                  style="width: 100%"
                  row-key="id"
                  :span-method="objectSpanMethod"
                  :row-style="getRowStyle"
                  :cell-style="getPercentageCellStyle"
                  height="100%"
                >
                  <el-table-column
                    prop="groupName"
                    label="分类"
                    fixed
                    width="150"
                  />
                  <el-table-column
                    prop="metricName"
                    label="指标名称"
                    fixed
                    width="200"
                  >
                    <template #default="{ row }">
                      <a
                        href="#"
                        class="metric-link"
                        @click.prevent="
                          showTrendChart(row, 'defbearer_service')
                        "
                        >{{ row.metricName }}</a
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-for="date in selectedDefServiceData.headers"
                    :key="date"
                    :prop="date"
                    :label="formatDateForHeader(date)"
                    width="120"
                    align="center"
                  >
                    <template #default="{ row, column }">
                      <el-tooltip
                        :disabled="
                          row[column.property + '_numerator'] === undefined
                        "
                        placement="top"
                      >
                        <template #content>
                          分子:
                          {{
                            row[
                              column.property + '_numerator'
                            ]?.toLocaleString()
                          }}<br />
                          分母:
                          {{
                            row[
                              column.property + '_denominator'
                            ]?.toLocaleString()
                          }}
                        </template>
                        <span>{{ row[column.property] }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-empty v-else description="请从左侧选择一个服务以查看详情" />
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="流程分阶段分析" name="byStage">
          <div class="sub-nav-container">
            <el-radio-group v-model="byStageTab" size="large">
              <el-radio-button label="dedbearer">专载</el-radio-button>
              <el-radio-button label="defbearer">默载</el-radio-button>
            </el-radio-group>
          </div>

          <el-table
            v-show="byStageTab === 'dedbearer'"
            ref="dedTableRef"
            v-loading="loading"
            :data="dedPivotedStageData.data"
            style="width: 100%"
            row-key="id"
            border
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :cell-style="getStageTableCellStyle"
            :row-style="getStageTreeRowStyle"
            class="sub-tab-content"
          >
            <el-table-column
              prop="description"
              label="阶段 - 类型 / 响应码 - 描述"
              width="550"
              fixed
            >
              <template #header>
                <span>阶段 - 类型 / 响应码 - 描述</span>
                <el-icon
                  class="header-icon"
                  title="全部展开"
                  @click="toggleAllDedRows(true)"
                  ><CirclePlus
                /></el-icon>
                <el-icon
                  class="header-icon"
                  title="全部收起"
                  @click="toggleAllDedRows(false)"
                  ><Remove
                /></el-icon>
              </template>
              <template #default="{ row }">
                <a
                  v-if="row.id?.startsWith('c-')"
                  href="#"
                  class="metric-link"
                  @click.prevent="showStageTrend(row, 'ded')"
                >
                  <div class="description-cell-content">
                    <span class="desc-text" :title="row.description">{{
                      row.description
                    }}</span>
                    <el-tag
                      :color="getTagColorForServiceCode(row.respCode)"
                      size="small"
                      class="resp-code-tag"
                    >
                      {{ row.respCode }}
                    </el-tag>
                  </div>
                </a>
                <template v-else>
                  <span>{{ row.description }}</span>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              v-for="date in dedPivotedStageData.headers"
              :key="date"
              :prop="date"
              :label="formatDateForHeader(date)"
              width="140"
              align="center"
            >
              <template #default="{ row, column }">
                <div v-if="row.id?.startsWith('c-')">
                  <span
                    class="count-value"
                    :style="
                      getDateCellStyle(row, column, dedPivotedStageData.headers)
                    "
                  >
                    {{ row[column.property].count }}
                  </span>
                  <span
                    v-if="row[column.property].percentage > 0.01"
                    class="percentage-value"
                  >
                    ({{ row[column.property].percentage.toFixed(1) }}%)
                  </span>
                </div>
                <span
                  v-else
                  :style="
                    getDateCellStyle(row, column, dedPivotedStageData.headers)
                  "
                >
                  {{ row[column.property] }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            v-show="byStageTab === 'defbearer'"
            ref="defTableRef"
            v-loading="loading"
            :data="defPivotedStageData.data"
            style="width: 100%"
            row-key="id"
            border
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :cell-style="getStageTableCellStyle"
            :row-style="getStageTreeRowStyle"
            class="sub-tab-content"
          >
            <el-table-column
              prop="description"
              label="阶段 - 类型 / 响应码 - 描述"
              width="550"
              fixed
            >
              <template #header>
                <span>阶段 - 类型 / 响应码 - 描述</span>
                <el-icon
                  class="header-icon"
                  title="全部展开"
                  @click="toggleAllDefRows(true)"
                  ><CirclePlus
                /></el-icon>
                <el-icon
                  class="header-icon"
                  title="全部收起"
                  @click="toggleAllDefRows(false)"
                  ><Remove
                /></el-icon>
              </template>
              <template #default="{ row }">
                <a
                  v-if="row.id?.startsWith('c-')"
                  href="#"
                  class="metric-link"
                  @click.prevent="showStageTrend(row, 'def')"
                >
                  <div class="description-cell-content">
                    <span class="desc-text" :title="row.description">{{
                      row.description
                    }}</span>
                    <el-tag
                      :color="getTagColorForServiceCode(row.respCode)"
                      size="small"
                      class="resp-code-tag"
                    >
                      {{ row.respCode }}
                    </el-tag>
                  </div>
                </a>
                <template v-else>
                  <span>{{ row.description }}</span>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              v-for="date in defPivotedStageData.headers"
              :key="date"
              :prop="date"
              :label="formatDateForHeader(date)"
              width="140"
              align="center"
            >
              <template #default="{ row, column }">
                <div v-if="row.id?.startsWith('c-')">
                  <span
                    class="count-value"
                    :style="
                      getDateCellStyle(row, column, defPivotedStageData.headers)
                    "
                  >
                    {{ row[column.property].count }}
                  </span>
                  <span
                    v-if="row[column.property].percentage > 0.01"
                    class="percentage-value"
                  >
                    ({{ row[column.property].percentage.toFixed(1) }}%)
                  </span>
                </div>
                <span
                  v-else
                  :style="
                    getDateCellStyle(row, column, defPivotedStageData.headers)
                  "
                >
                  {{ row[column.property] }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- Trend Chart Dialog -->
    <el-dialog
      v-model="trendDialogVisible"
      :title="trendChartTitle"
      width="60%"
      append-to-body
      @closed="destroyTrendChart"
    >
      <div ref="trendChartDialogRef" style="width: 100%; height: 400px"></div>
    </el-dialog>
    <!-- Stage Trend Chart Dialog -->
    <el-dialog
      v-model="stageTrendDialogVisible"
      :title="stageTrendChartTitle"
      width="70%"
      append-to-body
      @closed="destroyStageTrendChart"
    >
      <el-row :gutter="20">
        <el-col :span="16">
          <h4>历史趋势</h4>
          <div
            ref="stageBarChartDialogRef"
            style="width: 100%; height: 350px"
          ></div>
        </el-col>
        <el-col :span="8">
          <div class="pie-chart-header">
            <h4>占比分析</h4>
            <el-date-picker
              v-model="pieChartDate"
              type="date"
              placeholder="选择日期"
              :disabled-date="pieChartDisabledDate"
              size="small"
              style="width: 130px"
              @change="handlePieDateChange"
            />
          </div>
          <div
            ref="stagePieChartDialogRef"
            style="width: 100%; height: 350px"
          ></div>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 通信服务信息弹窗 -->
    <CmServiceInfoDialog
      v-model:visible="serviceInfoDialogVisible"
      :cm-service-id="selectedServiceId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useBusinessMetrics } from '../../composables/useBusinessMetrics'
import * as echarts from 'echarts'
import type {
  DedbearerCmServiceMetricsPO,
  DefbearerCmServiceMetricsPO,
} from '../../types/metrics'
import { ElMessage } from 'element-plus'
import {
  Fold,
  Expand,
  CirclePlus,
  Remove,
  DataAnalysis,
  SuccessFilled,
  Finished,
  ArrowDown,
  SortUp,
  SortDown,
  Search,
} from '@element-plus/icons-vue'
import type { CmServiceListDataDTO } from '../../api/cmService'
import CmServiceInfoDialog from '../../components/CmServiceInfoDialog.vue'

// ECharts 专业主题配置
const echartTheme = {
  color: [
    '#5470c6',
    '#91cc75',
    '#fac858',
    '#ee6666',
    '#73c0de',
    '#3ba272',
    '#fc8452',
    '#9a60b4',
    '#ea7ccc',
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'var(--el-font-family-base)',
    color: 'var(--el-text-color-primary)',
  },
  title: {
    textStyle: {
      color: 'var(--el-text-color-primary)',
    },
    subtextStyle: {
      color: 'var(--el-text-color-secondary)',
    },
  },
  legend: {
    textStyle: {
      color: 'var(--el-text-color-regular)',
    },
  },
  line: {
    itemStyle: {
      borderWidth: 1,
    },
    lineStyle: {
      width: 2.5,
    },
    symbolSize: 8,
    symbol: 'emptyCircle',
    smooth: true,
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'var(--el-bg-color-overlay)',
    borderColor: 'var(--el-border-color-lighter)',
    borderWidth: 1,
    padding: [10, 15],
    textStyle: {
      color: 'var(--el-text-color-primary)',
      fontSize: 14,
    },
    axisPointer: {
      type: 'cross',
      lineStyle: {
        color: 'var(--el-color-primary)',
        width: 1,
        type: 'dashed',
      },
      crossStyle: {
        color: 'var(--el-color-primary)',
        width: 1,
        type: 'dashed',
      },
      label: {
        backgroundColor: 'var(--el-color-primary)',
      },
    },
    confine: true, // 将 tooltip 限制在图表区域内
    valueFormatter: (value: number | null) =>
      value != null ? value.toFixed(2) : 'N/A',
  },
  grid: {
    left: '2%',
    right: '3%',
    bottom: '3%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    axisLine: {
      lineStyle: {
        color: 'var(--el-border-color)',
      },
    },
    axisLabel: {
      color: 'var(--el-text-color-secondary)',
    },
  },
  yAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: 'var(--el-border-color)',
      },
    },
    axisLabel: {
      color: 'var(--el-text-color-secondary)',
    },
    splitLine: {
      lineStyle: {
        color: 'var(--el-border-color-lighter)',
        type: 'dashed',
      },
    },
  },
  toolbox: {
    iconStyle: {
      borderColor: 'var(--el-text-color-secondary)',
    },
  },
  dataZoom: {
    textStyle: {
      color: 'var(--el-text-color-secondary)',
    },
    dataBackground: {
      areaStyle: {
        color: 'rgba(0,0,0,0.1)',
      },
      lineStyle: {
        opacity: 0.8,
        color: '#8392A5',
      },
    },
    fillerColor: 'rgba(255,255,255,0.2)',
    handleStyle: {
      color: '#D3D3D3',
    },
  },
  pie: {
    itemStyle: {
      borderWidth: 2,
      borderColor: 'var(--el-bg-color-page)',
    },
    label: {
      color: 'var(--el-text-color-regular)',
    },
    labelLine: {
      lineStyle: {
        color: 'var(--el-text-color-secondary)',
      },
    },
  },
  bar: {
    itemStyle: {
      borderRadius: [4, 4, 0, 0],
    },
  },
}
echarts.registerTheme('customTheme', echartTheme)

const {
  loading,
  queryParams,
  metricsData,
  serviceData,
  stageData,
  fetchAllData,
  communicationServiceList,
  fetchCommunicationServices,
  servicesFetched,
} = useBusinessMetrics()

// 确保 communicationServices 始终是数组
if (!queryParams.communicationServices) {
  queryParams.communicationServices = []
}

// 添加缺失的selectedServiceNames计算属性
const selectedServiceNames = computed(() => {
  if (
    !queryParams.communicationServices ||
    queryParams.communicationServices.length === 0
  )
    return []

  return queryParams.communicationServices.map((serviceId: string) => {
    const service = communicationServiceList.value.find(
      (s: CmServiceListDataDTO) => s.cmServiceId === serviceId
    )
    return {
      value: serviceId,
      label: formatServiceName(service?.cmServiceName || ''),
    }
  })
})

// --- Visual Enhancement ---
const groupColors: Record<string, string> = {
  整体: '#ffffff',
  申请: 'rgba(236, 245, 255, 0.6)',
  事件上报: 'rgba(240, 249, 235, 0.6)',
  会话重建: 'rgba(253, 246, 236, 0.6)',
}

const getRowStyle = ({ row }: { row: any }) => {
  if (row.groupName && groupColors[row.groupName]) {
    return { backgroundColor: groupColors[row.groupName] }
  }
  return {}
}

const getColorForPercentage = (value: number) => {
  if (isNaN(value)) return '#606266' // Default text color
  if (value >= 90) return '#67c23a' // Success
  if (value >= 80) return '#a0d911' // Good
  if (value >= 70) return '#fadb14' // Acceptable
  if (value >= 60) return '#faad14' // Warning
  return '#f5222d' // Danger
}

const getPercentageCellStyle = ({
  row,
  column,
}: {
  row: Record<string, any>
  column: Record<string, any>
}) => {
  const style: Record<string, string> = {}
  // Style for groupName column
  if (column.property === 'groupName' && row.groupRowSpan > 0) {
    style.fontWeight = 'bold'
  }

  // Style for metricName column
  if (column.property === 'metricName') {
    style.color = '#495057'
    style.fontWeight = '600'
  }

  // Style for date columns
  if (column.property.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const metricName = row.metricName as string
    const cellValue = row[column.property]

    if (
      metricName &&
      metricName.includes('率') &&
      typeof cellValue === 'string' &&
      cellValue.includes('%')
    ) {
      const value = parseFloat(cellValue.replace('%', ''))
      if (!isNaN(value)) {
        style.color = getColorForPercentage(value)
        if (value < 80) {
          // Add bold for lower values
          style.fontWeight = '600'
        }
      }
    }
  }
  return style
}

// --- 侧边栏显隐控制 ---
const isServiceSelectorVisible = ref(true)

// --- 格式化函数 ---
const formatValue = (
  value: number | null | undefined,
  isPercentage: boolean = false
): string => {
  if (value === null || typeof value === 'undefined') {
    return '-'
  }
  if (isPercentage) {
    // Keep raw value for pivoting, format in cell
    return `${value.toFixed(2)}%`
  }
  return String(value)
}

const formatServiceName = (rawName: string | undefined): string => {
  if (!rawName) return ''
  if (rawName.includes('|')) {
    return rawName.split('|')[1] || rawName
  }
  return rawName
}

const formatDateForHeader = (dateString: string) => {
  // Assumes dateString is YYYY-MM-DD
  const parts = dateString.split('-')
  if (parts.length === 3) {
    return `${parseInt(parts[1], 10)}月${parseInt(parts[2], 10)}日`
  }
  return dateString
}

// --- 筛选逻辑 ---
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: '近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '本周',
    value: () => {
      const end = new Date()
      const start = new Date()
      const dayOfWeek = start.getDay() === 0 ? 7 : start.getDay() // make Sunday 7, Monday 1
      start.setDate(start.getDate() - dayOfWeek + 1)
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const end = new Date()
      const start = new Date(end.getFullYear(), end.getMonth(), 1)
      return [start, end]
    },
  },
  {
    text: '近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

const dateRange = ref<[string, string]>(['', ''])

const setDefaultDateRange = () => {
  const today = new Date()
  const currentDay = today.getDay() // 0 (Sunday) to 6 (Saturday)

  // 计算到上周五的天数差
  // 修正：如果今天是周五(5)，需要回退7天才是上周五
  const daysSinceLastFriday =
    currentDay === 5 ? 7 : currentDay < 5 ? currentDay + 2 : currentDay - 5
  const lastFriday = new Date(today)
  lastFriday.setDate(today.getDate() - daysSinceLastFriday)

  // 计算到本周四的天数差
  // 修正：如果今天是周五(5)，需要回退1天才是本周四
  const daysUntilThisThursday =
    currentDay === 5 ? -1 : currentDay > 4 ? 4 - currentDay + 7 : 4 - currentDay
  const thisThursday = new Date(today)
  thisThursday.setDate(today.getDate() + daysUntilThisThursday)

  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  const startDate = formatDate(lastFriday)
  const endDate = formatDate(thisThursday)

  dateRange.value = [startDate, endDate]
  queryParams.startTime = startDate
  queryParams.endTime = endDate
}

const disabledDate = (time: Date) => {
  // 禁止选择今天之后的日期
  if (time.getTime() > Date.now()) {
    return true
  }

  // 如果已选择一个日期，则另一日期不能超过一年范围
  // 注意：日期选择器API会回调单个日期，此时可能只有第一个值被设置
  const currentValue = dateRange.value
  if (currentValue && currentValue[0] && !currentValue[1]) {
    const pickedDate = new Date(currentValue[0])

    // 计算与已选日期的最大间隔（一年）
    const minDate = new Date(pickedDate)
    minDate.setFullYear(minDate.getFullYear() - 1)

    const maxDate = new Date(pickedDate)
    maxDate.setFullYear(maxDate.getFullYear() + 1)

    // 禁用超出一年范围的日期
    return time < minDate || time > maxDate
  }

  return false
}

const handleQuery = () => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    // 验证日期范围不超过一年
    const startDate = new Date(dateRange.value[0])
    const endDate = new Date(dateRange.value[1])

    // 计算更准确的一年时间
    const maxDate = new Date(startDate)
    maxDate.setFullYear(maxDate.getFullYear() + 1)

    if (endDate > maxDate) {
      ElMessage.warning('查询时间范围不能超过一年')
      return
    }

    queryParams.startTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.startTime = ''
    queryParams.endTime = ''
  }
  fetchAllData()
}

const handleReset = () => {
  // 1. Reset communication services
  queryParams.communicationServices = []

  // 2. Reset date range to default
  setDefaultDateRange() // This already sets queryParams.startTime and endTime

  // 3. Fetch data with reset filters
  fetchAllData()
}

// --- KPI数据 ---
const kpiDataAverages = computed(() => {
  const calculateAverage = (list: any[] | undefined, key: string): string => {
    if (!list || list.length === 0) {
      return '-'
    }
    const validValues = list
      .map((item) => item[key])
      .filter((val) => typeof val === 'number')
    if (validValues.length === 0) {
      return '-'
    }
    const sum = validValues.reduce((acc, val) => acc + val, 0)
    const average = sum / validValues.length
    return average.toFixed(2)
  }

  return {
    overallSuccessRate: calculateAverage(
      metricsData.value?.allMetricsList,
      'overallSuccessRate'
    ),
    dedbearerSuccessRate: calculateAverage(
      metricsData.value?.allMetricsList,
      'dedbearerRequestSuccessPercentage'
    ),
    defbearerSuccessRate: calculateAverage(
      metricsData.value?.allMetricsList,
      'defbearerRequestSuccessPercentage'
    ),
  }
})

const overviewSubTab = ref('all')

// Helper function for pivoting data
const pivotTableData = (
  originalData: Record<string, any>[] | undefined,
  metricMapping: Record<string, string>,
  formatOptions: Record<string, string> = {}
) => {
  if (!originalData || originalData.length === 0) {
    return { headers: [], rows: [], meta: { maxValues: {} } }
  }

  const headers = [...new Set(originalData.map((item) => item.dayId))].sort()
  const metricKeys = Object.keys(metricMapping)

  const groupedRows: Record<string, Record<string, any>[]> = {}
  const topLevelRows: Record<string, any>[] = []
  let idCounter = 1

  const maxValues: Record<string, number> = {}
  const numericKeys = new Set<string>()

  // First, group rows by category
  metricKeys.forEach((key) => {
    const fullName = metricMapping[key]
    const parts = fullName.split(' - ')
    const hasGroup = parts.length > 1

    const metricName = hasGroup ? parts.slice(1).join(' - ') : fullName
    const groupName = hasGroup ? parts[0] : null // Use null for top-level

    const rowData: Record<string, any> = {
      id: `r-${idCounter++}`,
      metricName,
      groupName,
      metricKey: key,
    }
    headers.forEach((header) => {
      const dayData = originalData.find((d) => d.dayId === header)
      const rawValue = dayData ? dayData[key] : null

      if (typeof rawValue === 'number') {
        numericKeys.add(key)
      }
      const isPercentage = formatOptions[key] === 'percentage'
      rowData[header] = dayData ? formatValue(rawValue, isPercentage) : '-'

      if (dayData && dayData[`${key}_numerator`] !== undefined) {
        rowData[`${header}_numerator`] = dayData[`${key}_numerator`]
      }
      if (dayData && dayData[`${key}_denominator`] !== undefined) {
        rowData[`${header}_denominator`] = dayData[`${key}_denominator`]
      }
    })

    if (groupName) {
      if (!groupedRows[groupName]) {
        groupedRows[groupName] = []
      }
      groupedRows[groupName].push(rowData)
    } else {
      topLevelRows.push(rowData)
    }
  })

  // Calculate max values for numeric columns
  numericKeys.forEach((key) => {
    const values = originalData
      .map((d) => d[key])
      .filter((v) => typeof v === 'number') as number[]
    if (values.length > 0) {
      maxValues[key] = Math.max(...values)
    }
  })

  // Now, flatten the structure and add span info
  const finalRows: any[] = []
  // Correctly define group order, including handling the '总览' case which has null groupName
  const groupOrder = ['整体', '申请', '事件上报', '会话重建']
  const allGroupNames = new Set(
    Object.values(metricMapping).map((name) => name.split(' - ')[0])
  )
  allGroupNames.forEach((name) => {
    if (!groupOrder.includes(name)) groupOrder.push(name)
  })

  groupOrder.forEach((groupName) => {
    const group = groupedRows[groupName]
    if (group) {
      group.forEach((row, index) => {
        if (index === 0) {
          row.groupRowSpan = group.length
        } else {
          row.groupRowSpan = 0
        }
        finalRows.push(row)
      })
    }
  })

  // Add top-level rows (like '总览')
  const sortedFinalRows = [...finalRows, ...topLevelRows]

  return { headers, rows: sortedFinalRows, meta: { maxValues } }
}

const objectSpanMethod = ({
  row,
  column: _,
  columnIndex,
}: {
  row: Record<string, any>
  column: Record<string, any>
  rowIndex: number
  columnIndex: number
}) => {
  if (columnIndex === 0 && row.groupName) {
    // The first column is '分类' and has a group
    return {
      rowspan: row.groupRowSpan,
      colspan: row.groupRowSpan === 0 ? 0 : 1,
    }
  }
  return undefined
}

// --- Pivoted Overview Data ---

// Mapping for the "总览" table
const allOverviewMetricMapping = {
  overallSuccessRate: '2C商用业务接入成功率',
  dedbearerRequestSuccessPercentage: '专载请求成功率',
  defbearerRequestSuccessPercentage: '默载请求成功率',
}
const allOverviewFormatOptions = {
  overallSuccessRate: 'percentage',
  dedbearerRequestSuccessPercentage: 'percentage',
  defbearerRequestSuccessPercentage: 'percentage',
}
const pivotedAllOverviewData = computed(() => {
  return pivotTableData(
    metricsData.value?.allMetricsList,
    allOverviewMetricMapping,
    allOverviewFormatOptions
  )
})

// Mapping for the "专载指标详情" table (Overview Tab)
const dedbearerMetricMapping = {
  requestSuccessPercentageFitting: '整体 - 专载业务成功率',
  businessSuccessPercentageFitting: '整体 - 专载业务成功率(E)',
  allCount: '申请 - 专载申请调用数',
  noRepeatCount: '申请 - 专载申请请求数',
  getNumCount: '申请 - 取号总调用数',
  getNumSucCount: '申请 - 取号成功调用数',
  getNumFailCount: '申请 - 取号失败调用数',
  getNumSuccessPercentage: '申请 - 取号成功率',
  southSuc5GCount: '申请 - 专载申请请求成功数',
  southSucCount: '申请 - 专载申请业务成功数',
  southSuccessPercentage: '申请 - 专载南向请求成功率',
  requestSuccessPercentage: '申请 - 专载申请请求成功率(全)',
  businessSuccessPercentage: '申请 - 专载申请业务成功率(全)',
  getNumFailPercentageOfAll: '申请 - 专载申请取号失败率(全)',
  southFailurePercentageOfAll: '申请 - 专载申请南向失败率(全)',
  interruptCount: '事件上报 - 中断会话数',
  interruptionPercentage: '事件上报 - 会话中断率',
  correlationRebuildCount: '会话重建 - 发起的重建会话数',
  correlationRebuildSucCount: '会话重建 - 重建成功会话数',
  rebuildSuccessPercentage: '会话重建 - 重建成功率',
}
const dedbearerFormatOptions = {
  requestSuccessPercentageFitting: 'percentage',
  businessSuccessPercentageFitting: 'percentage',
  getNumSuccessPercentage: 'percentage',
  southSuccessPercentage: 'percentage',
  requestSuccessPercentage: 'percentage',
  businessSuccessPercentage: 'percentage',
  getNumFailPercentageOfAll: 'percentage',
  southFailurePercentageOfAll: 'percentage',
  interruptionPercentage: 'percentage',
  rebuildSuccessPercentage: 'percentage',
}
const pivotedDedbearerOverviewData = computed(() => {
  // Manually add getNumCount to the data if it's not present
  const data = metricsData.value?.dedbearerMetricsList?.map(
    (item: Record<string, any>) => {
      return {
        ...item,
        getNumCount: (item.getNumSucCount || 0) + (item.getNumFailCount || 0),
      }
    }
  )
  return pivotTableData(data, dedbearerMetricMapping, dedbearerFormatOptions)
})

// Mapping for the "默载指标详情" table (Overview Tab)
const defbearerMetricMapping = {
  requestSuccessPercentage: '整体 - 默载业务成功率',
  allCount: '申请 - 默载申请调用数',
  noRepeatCount: '申请 - 默载申请请求数',
  southSucCount: '申请 - 默载申请请求成功数',
  southSuccessPercentage: '申请 - 默载南向请求成功率',
  southFailurePercentageOfAll: '申请 - 默载申请南向失败率(全)',
}
const defbearerFormatOptions = {
  southSuccessPercentage: 'percentage',
  southFailurePercentageOfAll: 'percentage',
  requestSuccessPercentage: 'percentage',
}
const pivotedDefbearerOverviewData = computed(() => {
  return pivotTableData(
    metricsData.value?.defbearerMetricsList,
    defbearerMetricMapping,
    defbearerFormatOptions
  )
})

// --- "By Service" Tab Mappings ---
const dedbearerServiceMetricMapping = {
  ...dedbearerMetricMapping,
  requestCountOverallPercentage: '整体 - 专载申请请求数总体占比',
}
const dedbearerServiceFormatOptions = {
  ...dedbearerFormatOptions,
  requestCountOverallPercentage: 'percentage',
}
const defbearerServiceMetricMapping = {
  ...defbearerMetricMapping,
  requestCountOverallPercentage: '整体 - 默载申请请求数总体占比',
}
const defbearerServiceFormatOptions = {
  ...defbearerFormatOptions,
  requestCountOverallPercentage: 'percentage',
}

const byServiceTab = ref('dedbearer')
const dedServiceSearch = ref('')
const defServiceSearch = ref('')
const activeDedServiceId = ref<string | null>(null)
const activeDefServiceId = ref<string | null>(null)

// --- Sorting for service lists ---
type SortField = 'cmServiceName' | 'percentage'
type SortOrder = 'asc' | 'desc'

const dedSortBy = ref<SortField>('cmServiceName')
const dedSortOrder = ref<SortOrder>('asc')
const defSortBy = ref<SortField>('cmServiceName')
const defSortOrder = ref<SortOrder>('asc')

const setSort = (type: 'ded' | 'def', field: SortField) => {
  if (type === 'ded') {
    if (dedSortBy.value === field) {
      dedSortOrder.value = dedSortOrder.value === 'asc' ? 'desc' : 'asc'
    } else {
      dedSortBy.value = field
      // 业务需求：占比默认降序，名称默认升序
      dedSortOrder.value = field === 'percentage' ? 'desc' : 'asc'
    }
  } else {
    // type === 'def'
    if (defSortBy.value === field) {
      defSortOrder.value = defSortOrder.value === 'asc' ? 'desc' : 'asc'
    } else {
      defSortBy.value = field
      // 业务需求：占比默认降序，名称默认升序
      defSortOrder.value = field === 'percentage' ? 'desc' : 'asc'
    }
  }
}

const calculateServiceStats = (
  services: Record<string, any>[] | undefined,
  metricKey: string,
  metricsListName: 'dedbearerMetricsList' | 'defbearerMetricsList'
) => {
  if (!services || services.length === 0) return []

  const servicesWithTotals = services.map((service: Record<string, any>) => {
    const metricsList = service[metricsListName] as
      | Record<string, any>[]
      | undefined
    const totalRequests =
      metricsList?.reduce(
        (subTotal, dailyMetric) => subTotal + (dailyMetric[metricKey] || 0),
        0
      ) || 0
    return { ...service, totalRequests }
  })

  const grandTotal = servicesWithTotals.reduce(
    (total, service) => total + service.totalRequests,
    0
  )

  if (grandTotal === 0) {
    return servicesWithTotals.map((s) => ({
      ...s,
      percentage: 0,
      grandTotal: 0,
    }))
  }

  return servicesWithTotals.map((service) => ({
    ...service,
    percentage: (service.totalRequests / grandTotal) * 100,
    grandTotal: grandTotal,
  }))
}

// 确保服务数据包含必要的属性
interface ServiceWithStats {
  cmServiceId: string
  cmServiceName: string
  percentage: number
  grandTotal: number
  totalRequests: number
  [key: string]: any
}

const dedServicesWithStats = computed(() => {
  return calculateServiceStats(
    serviceData.value?.dedbearerCmServiceList as
      | Record<string, any>[]
      | undefined,
    'noRepeatCount',
    'dedbearerMetricsList'
  ) as ServiceWithStats[]
})

const defServicesWithStats = computed(() => {
  return calculateServiceStats(
    serviceData.value?.defbearerCmServiceList as
      | Record<string, any>[]
      | undefined,
    'noRepeatCount',
    'defbearerMetricsList'
  ) as ServiceWithStats[]
})

const filteredDedServicesWithStats = computed(() => {
  if (!dedServicesWithStats.value) return []
  const search = dedServiceSearch.value.toLowerCase()

  const filtered = dedServicesWithStats.value.filter((s) =>
    (s.cmServiceName || '').toLowerCase().includes(search) ||
    s.cmServiceId === dedServiceSearch.value // 编码精确匹配
  )

  return filtered.sort((a, b) => {
    const field = dedSortBy.value
    const order = dedSortOrder.value === 'asc' ? 1 : -1

    if (field === 'percentage') {
      return (a.percentage - b.percentage) * order
    } else {
      // cmServiceName
      return (
        ((a.cmServiceName || '') as string).localeCompare(
          (b.cmServiceName || '') as string
        ) * order
      )
    }
  })
})

const filteredDefServicesWithStats = computed(() => {
  if (!defServicesWithStats.value) return []
  const search = defServiceSearch.value.toLowerCase()

  const filtered = defServicesWithStats.value.filter((s) =>
    ((s.cmServiceName || '') as string).toLowerCase().includes(search) ||
    s.cmServiceId === defServiceSearch.value // 编码精确匹配
  )

  return filtered.sort((a, b) => {
    const field = defSortBy.value
    const order = defSortOrder.value === 'asc' ? 1 : -1

    if (field === 'percentage') {
      return (a.percentage - b.percentage) * order
    } else {
      // cmServiceName
      return (
        ((a.cmServiceName || '') as string).localeCompare(
          (b.cmServiceName || '') as string
        ) * order
      )
    }
  })
})

const selectedDedService = computed(
  (): DedbearerCmServiceMetricsPO | undefined => {
    if (!activeDedServiceId.value || !serviceData.value?.dedbearerCmServiceList)
      return undefined
    return serviceData.value.dedbearerCmServiceList.find(
      (s) => s.cmServiceId === activeDedServiceId.value
    )
  }
)

const selectedDefService = computed(
  (): DefbearerCmServiceMetricsPO | undefined => {
    if (!activeDefServiceId.value || !serviceData.value?.defbearerCmServiceList)
      return undefined
    return serviceData.value.defbearerCmServiceList.find(
      (s) => s.cmServiceId === activeDefServiceId.value
    )
  }
)

const selectedDedServiceData = computed(() => {
  if (!selectedDedService.value)
    return { headers: [], rows: [], meta: { maxValues: {} } }
  const data = selectedDedService.value.dedbearerMetricsList?.map((item) => {
    const totalForDay = dailyDedbearerTotalRequests.value[item.dayId] || 0
    const numerator = item.noRepeatCount || 0
    const percentage = totalForDay > 0 ? (numerator / totalForDay) * 100 : 0
    return {
      ...item,
      getNumCount: (item.getNumSucCount || 0) + (item.getNumFailCount || 0),
      requestCountOverallPercentage: percentage,
      requestCountOverallPercentage_numerator: numerator,
      requestCountOverallPercentage_denominator: totalForDay,
    }
  })
  return pivotTableData(
    data,
    dedbearerServiceMetricMapping,
    dedbearerServiceFormatOptions
  )
})

const selectedDefServiceData = computed(() => {
  if (!selectedDefService.value)
    return { headers: [], rows: [], meta: { maxValues: {} } }
  const data = selectedDefService.value.defbearerMetricsList?.map((item) => {
    const totalForDay = dailyDefbearerTotalRequests.value[item.dayId] || 0
    const numerator = item.noRepeatCount || 0
    const percentage = totalForDay > 0 ? (numerator / totalForDay) * 100 : 0
    return {
      ...item,
      requestCountOverallPercentage: percentage,
      requestCountOverallPercentage_numerator: numerator,
      requestCountOverallPercentage_denominator: totalForDay,
    }
  })
  return pivotTableData(
    data,
    defbearerServiceMetricMapping,
    defbearerServiceFormatOptions
  )
})

const handleDedServiceSelect = (serviceId: string) => {
  activeDedServiceId.value = serviceId
}

const handleDefServiceSelect = (serviceId: string) => {
  activeDefServiceId.value = serviceId
}

watch(serviceData, (newVal) => {
  if (newVal?.dedbearerCmServiceList?.length && !activeDedServiceId.value) {
    const sortedServices = [...newVal.dedbearerCmServiceList].sort((a, b) =>
      (a.cmServiceName || '').localeCompare(b.cmServiceName || '')
    )
    activeDedServiceId.value = sortedServices[0].cmServiceId
  }
  if (newVal?.defbearerCmServiceList?.length && !activeDefServiceId.value) {
    const sortedServices = [...newVal.defbearerCmServiceList].sort((a, b) =>
      (a.cmServiceName || '').localeCompare(b.cmServiceName || '')
    )
    activeDefServiceId.value = sortedServices[0].cmServiceId
  }
})

// --- 分阶段分析逻辑 ---
const byStageTab = ref('dedbearer')

// --- Expand/Collapse All Logic ---
const dedTableRef = ref<any>(null)
const toggleAllDedRows = (expand: boolean) => {
  if (dedTableRef.value && dedPivotedStageData.value.data) {
    for (const row of dedPivotedStageData.value.data) {
      dedTableRef.value.toggleRowExpansion(row, expand)
    }
  }
}
const defTableRef = ref<any>(null)
const toggleAllDefRows = (expand: boolean) => {
  if (defTableRef.value && defPivotedStageData.value.data) {
    for (const row of defPivotedStageData.value.data) {
      defTableRef.value.toggleRowExpansion(row, expand)
    }
  }
}

const createFlattenedStageData = (dataList: any[] | undefined) => {
  if (!dataList) return []
  const flatData: any[] = []
  dataList.forEach((day) => {
    if (day.stageDataList) {
      day.stageDataList.forEach((stage: any) => {
        flatData.push({
          dayId: day.dayId,
          ...stage,
        })
      })
    }
  })
  return flatData
}

const createPivotedData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) {
    return { headers: [], data: [] }
  }

  // 1. Get unique sorted date headers
  const dateHeaders = [...new Set(flatData.map((item) => item.dayId))].sort()

  // 2. Group data by Stage -> Type -> Resp
  const grouped = flatData.reduce((acc: Record<string, any>, item) => {
    const { stageType, type, respCode, respDesc, dayId, count } = item

    if (!acc[stageType]) acc[stageType] = {}
    if (!acc[stageType][type]) acc[stageType][type] = {}
    if (!acc[stageType][type][`${respCode}|${respDesc}`]) {
      acc[stageType][type][`${respCode}|${respDesc}`] = { respCode, respDesc }
    }

    acc[stageType][type][`${respCode}|${respDesc}`][dayId] = count

    return acc
  }, {})

  // 3. Build tree structure
  const tableData: any[] = []
  let idCounter = 0

  // 4. Custom sort for stageType
  const sortOrder = ['取号', '南向请求', '其他', '事件上报', '会话重建']
  const sortedStageTypes = Object.keys(grouped).sort((a, b) => {
    const indexA = sortOrder.indexOf(a)
    const indexB = sortOrder.indexOf(b)
    const finalIndexA = indexA === -1 ? Infinity : indexA
    const finalIndexB = indexB === -1 ? Infinity : indexB
    if (finalIndexA !== finalIndexB) {
      return finalIndexA - finalIndexB
    }
    return a.localeCompare(b) // Fallback sort
  })

  for (const stageType of sortedStageTypes) {
    const types = grouped[stageType]
    for (const type in types) {
      const parentRow: any = {
        id: `p-${idCounter++}`,
        description: `${stageType} - ${type}`,
        children: [] as any[],
      }
      // Initialize date columns
      dateHeaders.forEach((date) => (parentRow[date] = 0))

      const responses = types[type]
      for (const respKey in responses) {
        const respData = responses[respKey]
        const childRow: any = {
          id: `c-${idCounter++}`,
          description: respData.respDesc,
          respCode: respData.respCode,
          parentDescription: parentRow.description,
        }

        // Fill counts for child and aggregate for parent
        dateHeaders.forEach((date) => {
          const count = respData[date] || 0
          childRow[date] = count
          parentRow[date] += count
        })

        parentRow.children.push(childRow)
      }

      // 新增：后处理子行以计算并附加百分比信息
      parentRow.children.forEach((childRow: Record<string, any>) => {
        dateHeaders.forEach((date: string) => {
          const childCount = childRow[date] || 0 // 这是原始计数
          const parentCount = parentRow[date] || 0 // 这是聚合后的父行总数

          // 将原始计数替换为一个包含计数和百分比的对象
          childRow[date] = {
            count: childCount,
            percentage: parentCount > 0 ? (childCount / parentCount) * 100 : 0,
          }
        })
      })

      if (parentRow.children.length > 0) {
        tableData.push(parentRow)
      }
    }
  }

  // 5. Add tree group index for alternating row colors
  tableData.forEach((parentRow, parentIndex) => {
    parentRow.treeGroupIndex = parentIndex
    if (parentRow.children) {
      parentRow.children.forEach((childRow: Record<string, any>) => {
        childRow.treeGroupIndex = parentIndex
      })
    }
  })

  return { headers: dateHeaders, data: tableData }
}

const dedPivotedStageData = computed(() => {
  const flatData = createFlattenedStageData(stageData.value?.ded)
  return createPivotedData(flatData)
})

const defPivotedStageData = computed(() => {
  const flatData = createFlattenedStageData(stageData.value?.def)
  return createPivotedData(flatData)
})

const getStageTreeRowStyle = ({ row }: { row: Record<string, any> }) => {
  if (
    typeof row.treeGroupIndex !== 'undefined' &&
    row.treeGroupIndex % 2 === 1
  ) {
    return { backgroundColor: '#f9fafc' }
  }
  return {}
}

const getDateCellStyle = (
  row: Record<string, any>,
  column: Record<string, any>,
  headers: string[]
) => {
  // Only apply if there are 3 or more date columns and the current column is a date
  if (headers.length < 3 || !headers.includes(column.property)) {
    return {}
  }

  // Find the top 2 values in the row
  const rowValues = headers
    .map((date) => {
      const cell = row[date]
      // 如果是子节点，值为一个对象，需要从中提取count
      if (
        row.id?.startsWith('c-') &&
        typeof cell === 'object' &&
        cell !== null
      ) {
        return Number((cell as { count: number }).count)
      }
      // 父节点直接使用值
      return Number(cell as string)
    })
    .filter((v) => !isNaN(v) && v > 0) // Exclude 0s as they are not meaningful counts

  if (rowValues.length < 1) {
    // Need at least one value to highlight
    return {}
  }

  const sortedUniqueValues = [...new Set(rowValues)].sort((a, b) => b - a)
  const threshold =
    sortedUniqueValues.length > 1
      ? sortedUniqueValues[1]
      : sortedUniqueValues[0]

  const currentCell = row[column.property]
  let currentValue
  // 根据行类型提取当前值
  if (
    row.id?.startsWith('c-') &&
    typeof currentCell === 'object' &&
    currentCell !== null
  ) {
    currentValue = Number((currentCell as { count: number }).count)
  } else {
    currentValue = Number(currentCell as string)
  }

  if (!isNaN(currentValue) && currentValue >= threshold) {
    return { color: '#D9001B', fontWeight: 'bold' }
  }

  return {}
}

const getStageTableCellStyle = ({
  row,
  column,
}: {
  row: Record<string, any>
  column: Record<string, any>
}) => {
  // 1. Style for the description column (hierarchy)
  if (column.property === 'description') {
    if (row.id?.startsWith('p-')) {
      return { fontWeight: 'bold' }
    }
    if (row.id?.startsWith('c-')) {
      return { paddingLeft: '20px' }
    }
  }

  return {}
}

const serviceCodeColorMap: Record<string, string> = {
  '10': '#A8D8EA', // 网关
  '11': '#A3D2CA', // 用户
  '12': '#F4DDB4', // 快照
  '13': '#F8C8DC', // ID生成
  '14': '#C8B6E2', // 5GA
  '20': '#82C0CC', // 人网默载
  '21': '#96D6C9', // 人网默载适配
  '22': '#B5EAD7', // 默载批量
  '30': '#FFB7B2', // 专载
  '31': '#FFDAC1', // 专载适配
  '40': '#E2F0CB', // 物网默载
  '51': '#B8E0D2', // 物网默载适配
  '80': '#D7BDE2', // 短信
  '88': '#D5DBDB', // 任务
}

const getTagColorForServiceCode = (code: number): string => {
  if (code.toString().length < 6) {
    return '#E9EEF3'
  }
  const prefix = code.toString().substring(0, 2)
  return serviceCodeColorMap[prefix] || '#E9EEF3'
}

// --- Tabs ---
const activeTab = ref('overview')
const handleTabChange = (tabName: string) => {
  if (tabName === 'overview') {
    nextTick(() => chartInstance?.resize())
  }
}

// --- ECharts ---
let chartInstance: echarts.ECharts | null = null
onMounted(() => {
  setDefaultDateRange()
  fetchAllData() // 自动加载初始数据

  // 初始化ECharts
  const chartDom = document.getElementById('trend-chart')
  if (chartDom) {
    chartInstance = echarts.init(chartDom, 'customTheme')
    // 初始配置
    chartInstance.setOption({
      tooltip: {
        trigger: 'axis',
        valueFormatter: (value: number | null) =>
          value != null ? value.toFixed(2) + ' %' : 'N/A',
      },
      legend: {
        data: ['2C商用业务接入成功率', '专载成功率', '默载成功率'],
        bottom: 0,
        itemWidth: 15,
        itemHeight: 15,
        textStyle: {
          color: '#606266',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value', axisLabel: { formatter: '{value} %' } },
      series: [
        { name: '2C商用业务接入成功率', type: 'line', data: [], smooth: false },
        { name: '专载成功率', type: 'line', data: [], smooth: false },
        { name: '默载成功率', type: 'line', data: [], smooth: false },
      ],
    })
  }
})

watch(metricsData, (newData) => {
  if (!newData || !chartInstance) return
  const categories = newData.allMetricsList.map(
    (d: Record<string, any>) => d.dayId
  )
  const overallData = newData.allMetricsList.map(
    (d: Record<string, any>) => d.overallSuccessRate
  )
  const dedData = newData.allMetricsList.map(
    (d: Record<string, any>) => d.dedbearerRequestSuccessPercentage
  )
  const defData = newData.allMetricsList.map(
    (d: Record<string, any>) => d.defbearerRequestSuccessPercentage
  )

  // Dynamically adjust Y-axis
  const allValues = [...overallData, ...dedData, ...defData].filter(
    (v) => typeof v === 'number'
  )
  let yAxisMin = 80 // Default min to zoom in
  if (allValues.length > 0) {
    const dataMin = Math.min(...allValues)
    yAxisMin = Math.max(0, Math.floor(dataMin - 5)) // Add padding, but not below 0
  }

  chartInstance.setOption({
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} %',
      },
      min: yAxisMin,
      max: 100,
    },
    series: [
      {
        name: '2C商用业务接入成功率',
        type: 'line',
        data: overallData,
        smooth: false,
      },
      {
        name: '专载成功率',
        type: 'line',
        data: dedData,
        smooth: false,
      },
      {
        name: '默载成功率',
        type: 'line',
        data: defData,
        smooth: false,
      },
    ],
  })
})

// --- 自定义通信服务选择器逻辑 ---
const servicePopoverRef = ref<any>(null)
const isPopoverVisible = computed(
  () => servicePopoverRef.value?.popperRef?.open
)
const popoverSearchTerm = ref('')
const popoverActiveTab = ref('dedbearer')
const tempSelectedServices = ref<string[]>([])

const bearerModeMap: { [key: number]: string } = {
  0: '专载',
  1: '默载',
  2: 'AM-PCF',
}

const popoverServiceGroups = computed(() => {
  const groups: {
    [key: string]: {
      key: string
      label: string
      services: CmServiceListDataDTO[]
      filteredCount?: number
      selectedCount?: number
      isAllSelected?: boolean
      isIndeterminate?: boolean
    }
  } = {}

  // Initialize groups based on bearerModeMap to maintain order and include all
  Object.entries(bearerModeMap).forEach(([mode, label]) => {
    const key = label.toLowerCase().replace('-', '') // e.g., 'ampcf'
    groups[mode] = { key, label, services: [] }
  })

  // Populate services into groups
  communicationServiceList.value.forEach((service) => {
    const modeKey = String(service.bearerMode)
    if (groups[modeKey]) {
      groups[modeKey].services.push(service)
    }
  })

  // Sort services within each group and then filter
  Object.values(groups).forEach((group) => {
    group.services.sort((a, b) =>
      formatServiceName(a.cmServiceName).localeCompare(
        formatServiceName(b.cmServiceName)
      )
    )
  })

  // Filter and add selection-related properties
  return Object.values(groups).map((group) => {
    const filtered = filterServices(group.services, popoverSearchTerm.value)
    const filteredIds = new Set(filtered.map((s) => s.cmServiceId))
    const selectedIdsInGroup = tempSelectedServices.value.filter((id) =>
      filteredIds.has(id)
    )

    return {
      ...group,
      services: filtered,
      filteredCount: filtered.length,
      selectedCount: selectedIdsInGroup.length,
      isAllSelected:
        filtered.length > 0 && selectedIdsInGroup.length === filtered.length,
      isIndeterminate:
        selectedIdsInGroup.length > 0 &&
        selectedIdsInGroup.length < filtered.length,
    }
  })
})

const onPopoverShow = () => {
  // Fetch services if they haven't been fetched yet
  if (!servicesFetched.value) {
    fetchCommunicationServices()
  }
  // Initialize temporary selection and clear search
  tempSelectedServices.value = queryParams.communicationServices
    ? [...queryParams.communicationServices]
    : []
  popoverSearchTerm.value = ''
}

const confirmSelection = (apply: boolean) => {
  if (apply) {
    queryParams.communicationServices = [...tempSelectedServices.value]
  } else {
    // This is "Clear"
    queryParams.communicationServices = []
  }
  servicePopoverRef.value?.hide()
}

const clearSingleService = (serviceIdToRemove: string | undefined) => {
  if (!serviceIdToRemove || !queryParams.communicationServices) return
  queryParams.communicationServices = queryParams.communicationServices.filter(
    (id) => id !== serviceIdToRemove
  )
}

const handleSelectAll = (groupKey: string, isSelected: boolean) => {
  const group = popoverServiceGroups.value.find((g) => g.key === groupKey)
  if (!group) return

  const groupServiceIds = new Set(group.services.map((s) => s.cmServiceId))
  const otherSelectedIds = tempSelectedServices.value.filter(
    (id) => !groupServiceIds.has(id)
  )

  if (isSelected) {
    // Add all services from the current group
    tempSelectedServices.value = [
      ...otherSelectedIds,
      ...Array.from(groupServiceIds),
    ]
  } else {
    // Remove all services from the current group
    tempSelectedServices.value = otherSelectedIds
  }
}

const filterServices = (services: CmServiceListDataDTO[], term: string) => {
  if (!term) return services
  const lowerCaseTerm = term.toLowerCase()
  return services.filter(
    (s) =>
      formatServiceName(s.cmServiceName)
        .toLowerCase()
        .includes(lowerCaseTerm) ||
      s.cmServiceId === term // 编码精确匹配
  )
}

// --- Trend Chart Dialog Logic ---
const trendDialogVisible = ref(false)
const trendChartTitle = ref('')
const trendChartDialogRef = ref<HTMLElement | null>(null)
let trendChartInstance: echarts.ECharts | null = null

const showTrendChart = (row: Record<string, any>, tableType: string) => {
  const metricKey = row.metricKey
  const metricName = row.metricName
  if (!metricKey) return

  let sourceData: any[] | undefined

  switch (tableType) {
    case 'all_overview':
      sourceData = metricsData.value?.allMetricsList
      break
    case 'dedbearer_overview':
      sourceData = metricsData.value?.dedbearerMetricsList?.map((item) => ({
        ...item,
        getNumCount: (item.getNumSucCount || 0) + (item.getNumFailCount || 0),
      }))
      break
    case 'defbearer_overview':
      sourceData = metricsData.value?.defbearerMetricsList
      break
    case 'dedbearer_service':
      const dedService = serviceData.value?.dedbearerCmServiceList?.find(
        (s) => s.cmServiceId === activeDedServiceId.value
      )
      sourceData = dedService?.dedbearerMetricsList?.map((item) => {
        const totalForDay = dailyDedbearerTotalRequests.value[item.dayId] || 0
        const percentage =
          totalForDay > 0 ? ((item.noRepeatCount || 0) / totalForDay) * 100 : 0
        return {
          ...item,
          getNumCount: (item.getNumSucCount || 0) + (item.getNumFailCount || 0),
          requestCountOverallPercentage: percentage,
        }
      })
      break
    case 'defbearer_service':
      const defService = serviceData.value?.defbearerCmServiceList?.find(
        (s) => s.cmServiceId === activeDefServiceId.value
      )
      sourceData = defService?.defbearerMetricsList?.map((item) => {
        const totalForDay = dailyDefbearerTotalRequests.value[item.dayId] || 0
        const percentage =
          totalForDay > 0 ? ((item.noRepeatCount || 0) / totalForDay) * 100 : 0
        return {
          ...item,
          requestCountOverallPercentage: percentage,
        }
      })
      break
  }

  if (!sourceData || sourceData.length === 0) return

  sourceData.sort((a, b) => {
    const dayIdA = a.dayId || ''
    const dayIdB = b.dayId || ''
    return dayIdA.localeCompare(dayIdB)
  })

  const dates = sourceData.map((d) => formatDateForHeader(d.dayId || ''))
  const values = sourceData.map((d) => d[metricKey])
  const isPercentage =
    metricName && (metricName.includes('率') || metricName.includes('占比'))

  // Dynamically adjust Y-axis for dialog chart
  const numericValues = values.filter((v) => typeof v === 'number')
  let yAxisMin = 80 // Default min to zoom in
  if (numericValues.length > 0) {
    const dataMin = Math.min(...numericValues)
    yAxisMin = Math.max(0, Math.floor(dataMin - 2)) // Add a smaller padding for dialog
  }

  trendChartTitle.value = `${metricName || ''} - 趋势图`
  trendDialogVisible.value = true

  nextTick(() => {
    if (trendChartDialogRef.value) {
      trendChartInstance = echarts.init(trendChartDialogRef.value)
      trendChartInstance.setOption({
        tooltip: {
          trigger: 'axis',
          valueFormatter: (value: number | null) =>
            value != null
              ? value.toFixed(2) + (isPercentage ? ' %' : '')
              : 'N/A',
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'category', boundaryGap: false, data: dates },
        yAxis: {
          type: 'value',
          axisLabel: { formatter: `{value}${isPercentage ? ' %' : ''}` },
          min: yAxisMin,
          max: isPercentage ? 100 : undefined,
        },
        series: [
          {
            name: metricName || '',
            data: values,
            type: 'line',
            smooth: true,
            areaStyle: {},
          },
        ],
        dataZoom: [{ type: 'inside' }, { type: 'slider' }],
      })
    }
  })
}

const destroyTrendChart = () => {
  if (trendChartInstance) {
    trendChartInstance.dispose()
    trendChartInstance = null
  }
}

// --- Stage Trend Analysis ---
const stageTrendDialogVisible = ref(false)
const stageTrendChartTitle = ref('')
const stageBarChartDialogRef = ref<HTMLElement | null>(null)
const stagePieChartDialogRef = ref<HTMLElement | null>(null)
let stageBarChartInstance: echarts.ECharts | null = null
let stagePieChartInstance: echarts.ECharts | null = null
let currentStageAnalysisData: {
  trendData: any[]
  flatData: any[]
  stageType: string
  type: string
  description: string
  respCode: number
} | null = null
const pieChartDate = ref<Date | null>(null)
const pieChartDisabledDate = (time: Date) => {
  if (!currentStageAnalysisData?.trendData) return true
  const dates = new Set(currentStageAnalysisData.trendData.map((d) => d.dayId))
  const currentYMD = `${time.getFullYear()}-${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`
  return !dates.has(currentYMD)
}

const showStageTrend = (row: Record<string, any>, bearerKey: 'ded' | 'def') => {
  const { respCode, description, parentDescription } = row
  if (!respCode || !parentDescription) return

  // 1. Parse parent description
  const [stageType, type] = parentDescription.split(' - ')
  if (!stageType || !type) return

  // 2. Get the raw, unpivoted data
  const rawStageData = stageData.value?.[bearerKey]
  const flatData = createFlattenedStageData(rawStageData)

  // 3. Filter for the specific error code's trend, matching both code and description
  const trendData = flatData
    .filter(
      (item) =>
        item.stageType === stageType &&
        item.type === type &&
        item.respCode === respCode &&
        item.respDesc === description
    )
    .sort((a, b) => a.dayId.localeCompare(b.dayId))

  if (trendData.length === 0) return

  // Store data for pie chart interactions
  currentStageAnalysisData = {
    trendData,
    flatData,
    stageType,
    type,
    description,
    respCode,
  }

  // 4. Prepare Bar Chart data
  const barDates = trendData.map((d) => formatDateForHeader(d.dayId))
  const barValues = trendData.map((d) => d.count)

  // 5. Prepare Pie Chart data for the latest day within the selected range
  const relevantHeaders =
    bearerKey === 'ded'
      ? dedPivotedStageData.value.headers
      : defPivotedStageData.value.headers
  const latestDayId = relevantHeaders[relevantHeaders.length - 1]
  const latestDayData = trendData.find((d) => d.dayId === latestDayId)
  const latestDayValue = latestDayData ? latestDayData.count : 0

  const parentGroupOnLatestDay = flatData.filter(
    (item) =>
      item.stageType === stageType &&
      item.type === type &&
      item.dayId === latestDayId
  )
  const totalOnLatestDay = parentGroupOnLatestDay.reduce(
    (sum, item) => sum + item.count,
    0
  )

  Math.max(0, totalOnLatestDay - latestDayValue)

  // 6. Show dialog and render charts
  stageTrendChartTitle.value = `错误分析: ${parentDescription} / ${description} (${respCode})`
  stageTrendDialogVisible.value = true

  nextTick(() => {
    // Bar Chart
    if (stageBarChartDialogRef.value) {
      stageBarChartInstance = echarts.init(stageBarChartDialogRef.value)
      stageBarChartInstance.setOption({
        tooltip: { trigger: 'axis' },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'category', data: barDates },
        yAxis: { type: 'value' },
        series: [{ name: '出现次数', data: barValues, type: 'bar' }],
        dataZoom: [{ type: 'inside' }, { type: 'slider', bottom: 10 }],
      })
    }
    // Initial Pie Chart
    const latestDayId = trendData[trendData.length - 1]?.dayId
    if (latestDayId) {
      pieChartDate.value = new Date(latestDayId)
      updatePieChart(latestDayId)
    } else {
      updatePieChart(null)
    }
  })
}
const handlePieDateChange = (newDate: Date | null) => {
  if (!newDate) {
    updatePieChart(null)
    return
  }
  const ymd = `${newDate.getFullYear()}-${(newDate.getMonth() + 1).toString().padStart(2, '0')}-${newDate.getDate().toString().padStart(2, '0')}`
  updatePieChart(ymd)
}

const updatePieChart = (dayId: string | null) => {
  if (!stagePieChartDialogRef.value) return
  if (!currentStageAnalysisData || !dayId) {
    if (!stagePieChartInstance)
      stagePieChartInstance = echarts.init(stagePieChartDialogRef.value)
    stagePieChartInstance.setOption({
      title: {
        text: '请选择日期',
        left: 'center',
        top: 'center',
        textStyle: { color: '#909399' },
      },
    })
    return
  }

  const { flatData, stageType, type, description, respCode } =
    currentStageAnalysisData

  const targetDayData = currentStageAnalysisData.trendData.find(
    (d) => d.dayId === dayId
  )
  const targetDayValue = targetDayData ? targetDayData.count : 0

  const parentGroupOnDay = flatData.filter(
    (item) =>
      item.stageType === stageType && item.type === type && item.dayId === dayId
  )
  const totalOnDay = parentGroupOnDay.reduce((sum, item) => sum + item.count, 0)

  const pieData = [
    { value: targetDayValue, name: `${description} (${respCode})` },
    { value: Math.max(0, totalOnDay - targetDayValue), name: '其他同类错误' },
  ]

  if (!stagePieChartInstance) {
    stagePieChartInstance = echarts.init(stagePieChartDialogRef.value)
  }

  if (totalOnDay > 0) {
    stagePieChartInstance.setOption(
      {
        title: {
          text: `日期: ${formatDateForHeader(dayId)}`,
          left: 'center',
          top: 'bottom',
        },
        tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: pieData.map((p) => p.name),
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      },
      { notMerge: true }
    ) // Use notMerge to ensure legend updates correctly
  } else {
    stagePieChartInstance.setOption({
      title: {
        text: '当日无数据',
        left: 'center',
        top: 'center',
        textStyle: { color: '#909399' },
      },
    })
  }
}

const destroyStageTrendChart = () => {
  if (stageBarChartInstance) {
    stageBarChartInstance.dispose()
    stageBarChartInstance = null
  }
  if (stagePieChartInstance) {
    stagePieChartInstance.dispose()
    stagePieChartInstance = null
  }
  currentStageAnalysisData = null
  pieChartDate.value = null
}

// --- 通信服务信息弹窗相关 ---
const serviceInfoDialogVisible = ref(false)
const selectedServiceId = ref<string | null>(null)

// 显示通信服务信息弹窗
const showServiceInfo = (serviceId: string) => {
  selectedServiceId.value = serviceId
  serviceInfoDialogVisible.value = true
}

// --- Daily Totals for Percentage Calculation ---
const dailyDedbearerTotalRequests = computed(() => {
  if (!serviceData.value?.dedbearerCmServiceList) return {}
  const dailyTotals: Record<string, number> = {}
  serviceData.value.dedbearerCmServiceList.forEach((service) => {
    service.dedbearerMetricsList?.forEach((metric) => {
      if (!dailyTotals[metric.dayId]) {
        dailyTotals[metric.dayId] = 0
      }
      dailyTotals[metric.dayId] += metric.noRepeatCount || 0
    })
  })
  return dailyTotals
})

const dailyDefbearerTotalRequests = computed(() => {
  if (!serviceData.value?.defbearerCmServiceList) return {}
  const dailyTotals: Record<string, number> = {}
  serviceData.value.defbearerCmServiceList.forEach((service) => {
    service.defbearerMetricsList?.forEach((metric) => {
      if (!dailyTotals[metric.dayId]) {
        dailyTotals[metric.dayId] = 0
      }
      dailyTotals[metric.dayId] += metric.noRepeatCount || 0
    })
  })
  return dailyTotals
})
</script>

<style scoped>
.business-metrics-container {
  padding: 20px;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  transform: translateZ(0);
}

.composite-search-container {
  display: flex;
  flex-grow: 1;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  transition: border-color var(--el-transition-duration);
}
.composite-search-container:hover {
  border-color: var(--el-color-primary-light-3);
}
.composite-search-container:focus-within {
  border-color: var(--el-color-primary);
}
.composite-search-container .sort-select-in-input {
  width: 80px;
}
.composite-search-container .sort-select-in-input .el-input__wrapper {
  box-shadow: none !important;
  border: none !important;
  border-right: 1px solid var(--el-border-color) !important;
  border-radius: 0;
  background-color: transparent;
  padding: 1px 11px;
}
.composite-search-container .search-input-in-composite {
  flex: 1;
}
.composite-search-container .search-input-in-composite .el-input__wrapper {
  box-shadow: none !important;
  border: none !important;
  background-color: transparent;
}
.composite-search-container .search-input-in-composite .el-input-group__append {
  background-color: transparent;
  padding: 0;
  border: none;
  box-shadow: none;
}

.filter-card {
  margin-bottom: 20px;
  border: none;
}

.kpi-cards {
  margin-bottom: 20px;
}

.kpi-card {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: -1;
  transition: opacity 0.4s ease;
}

.kpi-card-blue::before {
  background: linear-gradient(135deg, #409eff, #53a8ff);
}

.kpi-card-green::before {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.kpi-card-orange::before {
  background: linear-gradient(135deg, #e6a23c, #f3b760);
}

.kpi-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.kpi-card:hover::before {
  opacity: 0.2;
}

.kpi-icon-wrapper {
  font-size: 28px;
  color: #fff;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
  box-shadow: 0 6px 16px -3px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.kpi-card:hover .kpi-icon-wrapper {
  transform: scale(1.08);
}

.kpi-card-blue .kpi-icon-wrapper {
  background: linear-gradient(135deg, #409eff, #53a8ff);
}
.kpi-card-green .kpi-icon-wrapper {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}
.kpi-card-orange .kpi-icon-wrapper {
  background: linear-gradient(135deg, #e6a23c, #f3b760);
}

.kpi-content {
  display: flex;
  flex-direction: column;
}

.kpi-title {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 10px;
  white-space: nowrap;
  letter-spacing: 0.2px;
}

.kpi-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2;
  transition: transform 0.3s ease;
}

.kpi-card:hover .kpi-value {
  transform: scale(1.03);
}

.kpi-unit {
  font-size: 16px;
  font-weight: normal;
  margin-left: 4px;
  color: #606266;
  opacity: 0.8;
}

.content-card {
  border: none;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs {
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 修复生产环境tabs顺序问题 */
.content-tabs :deep(.el-tabs__header) {
  order: -1; /* 强制header显示在前面 */
}

.content-tabs :deep(.el-tabs__content) {
  order: 1; /* 强制content显示在后面 */
}

:deep(.el-tabs__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content .el-tab-pane) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  background-color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.sub-nav-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 25px;
}

.sub-nav-container .el-radio-group {
  background-color: var(--el-fill-color-light);
  border-radius: var(--el-border-radius-base);
  padding: 4px;
}

.sub-nav-container .el-radio-button :deep(.el-radio-button__inner) {
  padding: 8px 20px;
  font-size: 14px;
  border: none !important;
  border-radius: var(--el-border-radius-base) !important;
  box-shadow: none !important;
  background-color: transparent;
  color: var(--el-text-color-regular);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sub-nav-container .el-radio-button.is-active :deep(.el-radio-button__inner) {
  background-color: #fff;
  color: var(--el-color-primary);
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.sub-tab-content {
  margin-top: 20px;
}

.service-layout {
  position: relative;
  flex: 1;
  display: flex;
  min-height: 0;
}
.service-selector {
  border-right: 1px solid #e4e7ed;
  height: 100%;
  display: flex;
  flex-direction: column;
  transform: translateZ(0);
  padding-left: 20px;
}
.search-input {
  flex-grow: 1;
}
.sort-controls {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.sort-select {
  width: 90px;
}
.sort-order-btn {
  margin-left: 4px;
}
.service-menu {
  flex-grow: 1;
  overflow-y: auto;
  border-right: none; /* Override default menu border */
}
.service-menu .el-menu-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4px;
  margin: 2px 5px;
}
.service-menu .el-menu-item.is-active {
  background-color: #ecf5ff;
  color: #409eff;
}

.service-details {
  height: 100%;
  position: relative;
  transform: translateZ(0);
  padding-left: 20px;
  display: flex;
  flex-direction: column;
}
.service-details-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-top: 10px;
}
.service-detail-title {
  margin: 0 0 0 10px;
  font-size: 18px;
  font-weight: 600;
  flex-grow: 1;
  color: #303133;
}
.table-container {
  flex: 1;
  min-height: 0;
}
.table-container h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
}
.header-icon {
  margin-left: 8px;
  cursor: pointer;
  vertical-align: middle;
  color: #909399; /* A bit more subtle default color */
  transition: color 0.3s;
}
.header-icon:hover {
  color: #409eff; /* Brighter on hover */
}

/* --- General Table Style Override --- */
.business-metrics-container :deep(.el-table) {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
}

:deep(.el-table th.el-table__cell) {
  background-color: var(--el-fill-color-lighter) !important;
  color: var(--el-text-color-primary);
  font-weight: 600;
  padding: 16px 10px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-table td.el-table__cell) {
  padding: 16px 10px;
  border-bottom: 1px solid var(--el-border-color-extra-light);
  color: var(--el-text-color-regular);
}

:deep(.el-table .el-table__row) {
  transition: background-color 0.2s ease-in-out;
}

:deep(.el-table__body tr:hover > td.el-table__cell) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.el-table .el-table__row--striped > .el-table__cell) {
  background: var(--el-fill-color-lighter);
}
:deep(.el-table .el-table__row--striped:hover > .el-table__cell) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--el-border-color-extra-light);
}

:deep(.el-table::before) {
  display: none;
}

/* --- Tree Table Specific Style --- */
:deep(.el-table .el-table__row[class*='level-']) td:first-child {
  padding-left: 20px;
}
:deep(.el-table .el-table__row[class*='level-1']) td:first-child {
  padding-left: 40px;
}

:deep(span.el-table__placeholder) {
  display: none;
}

.business-metrics-container :deep(.el-table__row .cell) {
  white-space: normal;
  word-break: break-all;
}
.data-bar-container {
  position: relative;
  height: 24px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.data-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: var(--el-color-primary-light-5);
  transition: width 0.3s ease;
  opacity: 0.6;
}
.data-bar-value {
  position: relative;
  z-index: 1;
  padding: 0 8px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.description-cell-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.desc-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  padding-right: 10px;
}
.resp-code-tag {
  flex-shrink: 0;
  color: #34495e; /* Dark text for readability on light custom backgrounds */
  border-color: transparent !important; /* Remove border for a cleaner look */
  font-weight: 500;
}

.custom-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 240px;
  height: 32px;
  padding: 0 11px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.custom-select-trigger:hover {
  border-color: #c0c4cc;
}
.custom-select-trigger.is-active {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 1px var(--el-color-primary-light-3);
}
.trigger-content {
  flex-grow: 1;
  overflow: hidden;
  white-space: nowrap;
}
.trigger-content .placeholder {
  color: #a8abb2;
  font-size: 14px;
}
.trigger-content .el-tag {
  background-color: var(--el-color-info-light-9);
  color: var(--el-text-color-regular);
  border: 1px solid var(--el-border-color);
}
.arrow-icon {
  color: #a8abb2;
  transition: transform 0.3s;
}
.arrow-icon.is-reverse {
  transform: rotate(180deg);
}

.service-select-popover-content {
  display: flex;
  flex-direction: column;
}
.popover-search-input {
  margin-bottom: 12px;
}
.popover-tabs .el-tabs__header {
  margin-bottom: 10px;
}
.service-list-scrollbar {
  padding: 0 10px;
}
.service-checkbox {
  display: flex;
  width: 100%;
  height: 34px;
  line-height: 34px;
  border-radius: 4px;
  margin-right: 0;
  padding: 0 8px;
}
.service-checkbox:hover {
  background-color: #f5f7fa;
}
.popover-footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--el-border-color-extra-light);
  padding-top: 12px;
  margin-top: 10px;
}

.metric-link {
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
}
.metric-link:hover {
  text-decoration: underline;
}
.pie-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.service-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.service-name {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 15px;
}

.clickable-service-name {
  cursor: pointer;
  color: #409eff;
  transition: color 0.2s;
}

.clickable-service-name:hover {
  color: #66b1ff;
  text-decoration: underline;
}
.progress-ring-container {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.progress-ring {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: grid;
  place-items: center;
  transition: transform 0.3s;
}
.progress-ring::before {
  content: '';
  position: absolute;
  height: 70%;
  width: 70%;
  background: var(--el-bg-color-page);
  border-radius: 50%;
}

.progress-ring:hover {
  transform: scale(1.15);
}

.service-selector-header {
  display: flex;
  align-items: center;
  padding: 10px 10px 12px;
  flex-shrink: 0;
  gap: 8px;
}
.search-and-sort-area {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}
.search-and-sort-area .el-input {
  flex-grow: 1;
}
.sort-controls-v2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 16px;
  padding-top: 0;
}
.sort-key {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  transition: color 0.2s;
  white-space: nowrap;
}
.sort-key:hover {
  color: #409eff;
}
.sort-key.active {
  color: #409eff;
  font-weight: 600;
}
.sort-key .el-icon {
  font-size: 14px;
}

.count-value {
  font-weight: 500;
}
.percentage-value {
  display: inline-block;
  font-size: 0.8em;
  color: #909399;
  margin-left: 4px;
}
</style>
