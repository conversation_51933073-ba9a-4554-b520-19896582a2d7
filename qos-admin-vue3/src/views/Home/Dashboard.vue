<template>
  <div v-loading="loading" class="dashboard-container">
    <h1>分析页</h1>

    <div class="filter-section">
      <el-form :inline="true" class="search-form">
        <el-form-item label="起止时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="dateShortcuts"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
          <el-button class="show-all-btn" @click="showAllData"
            >一键展示全部</el-button
          >
          <el-dropdown class="export-dropdown" @command="handleExportCommand">
            <el-button class="export-btn">
              📥 导出报告 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="png">导出为PNG图片</el-dropdown-item>
                <el-dropdown-item command="excel"
                  >导出为Excel文件</el-dropdown-item
                >
                <el-dropdown-item command="pdf">导出为PDF报告</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            type="warning"
            class="anomaly-btn"
            @click="showAnomalyDetection"
          >
            🔍 异常检测
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>网关异常统计</span>
            </div>
          </template>
          <el-table
            ref="httpErrorTableRef"
            :data="paginatedHttpErrorData"
            border
            style="width: 100%"
            @sort-change="handleHttpErrorSortChange"
          >
            <el-table-column prop="requestUri" label="接口URI" sortable />
            <el-table-column prop="respCode" label="响应码" sortable />
            <el-table-column prop="respDesc" label="响应描述" />
            <el-table-column prop="count" label="数量" sortable />
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="httpErrorCurrentPage"
              v-model:page-size="httpErrorPageSize"
              :page-sizes="httpErrorPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="httpErrorTotalItems"
              @size-change="handleHttpErrorSizeChange"
              @current-change="handleHttpErrorCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>用户订购与退订统计</span>
            </div>
          </template>
          <el-table
            ref="subscriptionTableRef"
            :data="paginatedSubscriptionData"
            border
            style="width: 100%"
            @sort-change="handleSubscriptionSortChange"
          >
            <el-table-column prop="operTypeName" label="操作类型" sortable />
            <el-table-column prop="respCode" label="响应代码" sortable />
            <el-table-column prop="respDesc" label="响应描述" />
            <el-table-column prop="count" label="数量" sortable>
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  class="count-button"
                  @click="handleSubscriptionCountClick(row, dateRange)"
                >
                  {{ row.count }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="subscriptionCurrentPage"
              v-model:page-size="subscriptionPageSize"
              :page-sizes="subscriptionPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="subscriptionTotalItems"
              @size-change="handleSubscriptionSizeChange"
              @current-change="handleSubscriptionCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>默载调用统计</span>
            </div>
          </template>
          <el-table
            ref="defbearerTableRef"
            :data="paginateddefbearerData"
            border
            style="width: 100%"
            @sort-change="handledefbearerSortChange"
          >
            <el-table-column prop="orderSourceName" label="订单来源" sortable />
            <el-table-column prop="operTypeName" label="操作类型" sortable />
            <el-table-column prop="respCode" label="响应代码" sortable />
            <el-table-column prop="respDesc" label="响应描述" />
            <el-table-column prop="count" label="数量" sortable>
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  class="count-button"
                  @click="handleDefbearerCountClick(row, dateRange)"
                >
                  {{ row.count }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="defbearerCurrentPage"
              v-model:page-size="defbearerPageSize"
              :page-sizes="defbearerPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="defbearerTotalItems"
              @size-change="handledefbearerSizeChange"
              @current-change="handledefbearerCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>专载调用统计</span>
            </div>
          </template>
          <el-table
            ref="dedbearerTableRef"
            :data="paginateddedbearerData"
            border
            style="width: 100%"
            @sort-change="handlededbearerSortChange"
          >
            <el-table-column prop="orderSourceName" label="订单来源" sortable />
            <el-table-column prop="operTypeName" label="操作类型" sortable />
            <el-table-column prop="respCode" label="响应代码" sortable />
            <el-table-column prop="respDesc" label="响应描述" />
            <el-table-column prop="count" label="数量" sortable>
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  class="count-button"
                  @click="handleDedbearerCountClick(row, dateRange)"
                >
                  {{ row.count }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="dedbearerCurrentPage"
              v-model:page-size="dedbearerPageSize"
              :page-sizes="dedbearerPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="dedbearerTotalItems"
              @size-change="handlededbearerSizeChange"
              @current-change="handlededbearerCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>默载批量调用统计</span>
            </div>
          </template>
          <el-table
            ref="defbearerBatchTableRef"
            :data="paginateddefbearerBatchData"
            border
            style="width: 100%"
            @sort-change="handledefbearerBatchSortChange"
          >
            <el-table-column prop="homeProvinceName" label="归属省" sortable />
            <el-table-column prop="respCode" label="响应代码" sortable />
            <el-table-column prop="respDesc" label="响应描述" />
            <el-table-column prop="count" label="数量" sortable />
          </el-table>
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="defbearerBatchCurrentPage"
              v-model:page-size="defbearerBatchPageSize"
              :page-sizes="defbearerBatchPageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="defbearerBatchTotalItems"
              @size-change="handledefbearerBatchSizeChange"
              @current-change="handledefbearerBatchCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <DetailModal
      v-model="detailModalVisible"
      :title="detailModalTitle"
      :stats-type="detailStatsType"
      :stats-info="detailStatsInfo"
      :query-params="detailQueryParams"
      @refresh="handleDetailRefresh"
    />

    <!-- 异常检测弹窗 -->
    <AnomalyDetectionModal v-model="anomalyDetectionVisible" />
  </div>
</template>

<script setup lang="ts" name="Dashboard">
import { ArrowDown } from '@element-plus/icons-vue'
import { ElLoading, ElMessage, TableInstance } from 'element-plus'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { computed, createApp, nextTick, onMounted, ref } from 'vue'
import * as XLSX from 'xlsx'
import ReportView from '../../components/ReportView.vue'
import DetailModal from '../../components/DetailModal.vue'
import AnomalyDetectionModal from '../../components/AnomalyDetectionModal.vue'
import request from '../../utils/request'

// 详情弹窗相关状态
const detailModalVisible = ref(false)
const detailModalTitle = ref('')
const detailStatsType = ref<
  'defbearer' | 'dedbearer' | 'subscription' | 'defbearerBatch'
>('defbearer')
const detailStatsInfo = ref('')
const detailQueryParams = ref({})

// 异常检测弹窗状态
const anomalyDetectionVisible = ref(false)

// 显示详情弹窗
const showDetailModal = (config: {
  title: string
  statsType: 'defbearer' | 'dedbearer' | 'subscription' | 'defbearerBatch'
  statsInfo: string
  queryParams: Record<string, any>
}) => {
  detailModalTitle.value = config.title
  detailStatsType.value = config.statsType
  detailStatsInfo.value = config.statsInfo
  detailQueryParams.value = config.queryParams
  detailModalVisible.value = true
}

// 处理详情弹窗刷新
const handleDetailRefresh = () => {
  console.log('刷新详情数据', detailQueryParams.value)
}

// 显示异常检测弹窗
const showAnomalyDetection = () => {
  anomalyDetectionVisible.value = true
}

// 处理默载调用统计数量点击
const handleDefbearerCountClick = (row: any, dateRange: [string, string]) => {
  showDetailModal({
    title: '默载调用统计',
    statsType: 'defbearer',
    statsInfo: `${row.orderSourceName} - ${row.operTypeName} - 响应码${row.respCode}`,
    queryParams: {
      orderSource: row.orderSource,
      orderSourceName: row.orderSourceName,
      operType: row.operType,
      operTypeName: row.operTypeName,
      respCode: row.respCode,
      respDesc: row.respDesc,
      startTime: dateRange[0],
      endTime: dateRange[1],
      count: row.count,
    },
  })
}

// 处理专载调用统计数量点击
const handleDedbearerCountClick = (row: any, dateRange: [string, string]) => {
  showDetailModal({
    title: '专载调用统计',
    statsType: 'dedbearer',
    statsInfo: `${row.orderSourceName} - ${row.operTypeName} - 响应码${row.respCode}`,
    queryParams: {
      orderSource: row.orderSource,
      orderSourceName: row.orderSourceName,
      operType: row.operType,
      operTypeName: row.operTypeName,
      respCode: row.respCode,
      respDesc: row.respDesc,
      startTime: dateRange[0],
      endTime: dateRange[1],
      count: row.count,
    },
  })
}

// 处理用户订购与退订统计数量点击
const handleSubscriptionCountClick = (
  row: any,
  dateRange: [string, string]
) => {
  showDetailModal({
    title: '用户订购与退订统计',
    statsType: 'subscription',
    statsInfo: `${row.operTypeName} - 响应码${row.respCode}`,
    queryParams: {
      operType: row.operType,
      respCode: row.respCode,
      respDesc: row.respDesc,
      count: row.count,
      startTime: dateRange[0],
      endTime: dateRange[1],
    },
  })
}

// --- Type Definitions ---
interface HttpErrorStat {
  requestUri: string
  respCode: number
  respDesc: string
  count: number
}
interface SubscriptionStat {
  operType: number
  operTypeName: string
  respCode: number
  respDesc: string
  count: number
}
interface DefbearerStat {
  orderSource: string
  orderSourceName: string
  operType: number
  operTypeName: string
  respCode: number
  respDesc: string
  count: number
}
interface DefbearerBatchStat {
  homeProvinceName: string
  respCode: number
  respDesc: string
  count: number
}
interface DedbearerStat {
  orderSource: string
  orderSourceName: string
  operType: number
  operTypeName: string
  respCode: number
  respDesc: string
  count: number
}
type SortOrder = 'ascending' | 'descending' | null
interface SortParams<T> {
  prop: keyof T
  order: SortOrder
}

const loading = ref(false)

// 日期选择器相关
const getYesterdayDate = () => {
  const date = new Date()
  date.setTime(date.getTime() - 3600 * 1000 * 24)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}
const yesterday = getYesterdayDate()
const dateRange = ref<[string, string]>([yesterday, yesterday])

const httpErrorTableRef = ref<TableInstance>()
const subscriptionTableRef = ref<TableInstance>()
const defbearerTableRef = ref<TableInstance>()
const defbearerBatchTableRef = ref<TableInstance>()
const dedbearerTableRef = ref<TableInstance>()

const dateShortcuts = [
  {
    text: '昨天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24)
      end.setTime(end.getTime() - 3600 * 1000 * 24)
      return [start, end]
    },
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const sortData = <T extends Record<string, any>>(
  data: T[],
  { prop, order }: SortParams<T>
): T[] => {
  if (!order || !Array.isArray(data)) return Array.isArray(data) ? data : []
  return [...data].sort((a, b) => {
    if (order === 'ascending') {
      return a[prop] > b[prop] ? 1 : -1
    } else {
      return b[prop] > a[prop] ? 1 : -1
    }
  })
}

// HTTP错误数据
const httpErrorDataAll = ref<HttpErrorStat[]>([])
const httpErrorSort = ref<SortParams<HttpErrorStat>>({
  prop: 'count',
  order: 'descending',
})
const sortedHttpErrorData = computed(() =>
  sortData(httpErrorDataAll.value, httpErrorSort.value)
)
const httpErrorCurrentPage = ref(1)
const httpErrorPageSize = ref(20)
const httpErrorTotalItems = ref(0)
const httpErrorPageSizes = computed(() => {
  const sizes = [10, 20, 50, 100]
  if (httpErrorTotalItems.value > 100) {
    sizes.push(httpErrorTotalItems.value)
  }
  return sizes
})
const paginatedHttpErrorData = computed(() => {
  const startIndex = (httpErrorCurrentPage.value - 1) * httpErrorPageSize.value
  const endIndex = startIndex + httpErrorPageSize.value
  return sortedHttpErrorData.value.slice(startIndex, endIndex)
})
const handleHttpErrorSortChange = ({
  prop,
  order,
}: SortParams<HttpErrorStat>) => {
  httpErrorSort.value = { prop, order: order || 'descending' }
}
const handleHttpErrorSizeChange = (val: number) => {
  httpErrorPageSize.value = val
  httpErrorCurrentPage.value = 1
}
const handleHttpErrorCurrentChange = (val: number) => {
  httpErrorCurrentPage.value = val
}

// 用户订购与退订情况统计相关
const subscriptionDataAll = ref<SubscriptionStat[]>([])
const subscriptionSort = ref<SortParams<SubscriptionStat>>({
  prop: 'count',
  order: 'descending',
})
const subscriptionCurrentPage = ref(1)
const subscriptionPageSize = ref(20)
const subscriptionTotalItems = ref(0)

const subscriptionPageSizes = computed(() => {
  const standardSizes = [10, 20, 50, 100]
  if (subscriptionTotalItems.value > 100) {
    return [...new Set([...standardSizes, subscriptionTotalItems.value])]
  }
  return standardSizes
})
const sortedSubscriptionData = computed(() =>
  sortData(subscriptionDataAll.value, subscriptionSort.value)
)

const paginatedSubscriptionData = computed(() => {
  const startIndex =
    (subscriptionCurrentPage.value - 1) * subscriptionPageSize.value
  const endIndex = startIndex + subscriptionPageSize.value
  return sortedSubscriptionData.value.slice(startIndex, endIndex)
})

const handleSubscriptionSortChange = ({
  prop,
  order,
}: SortParams<SubscriptionStat>) => {
  subscriptionSort.value = { prop, order: order || 'descending' }
}

// 默载调用情况统计相关
const defbearerDataAll = ref<DefbearerStat[]>([])
const defbearerSort = ref<SortParams<DefbearerStat>>({
  prop: 'count',
  order: 'descending',
})
const defbearerCurrentPage = ref(1)
const defbearerPageSize = ref(20)
const defbearerTotalItems = ref(0)

const defbearerPageSizes = computed(() => {
  const standardSizes = [10, 20, 50, 100]
  if (defbearerTotalItems.value > 100) {
    return [...new Set([...standardSizes, defbearerTotalItems.value])]
  }
  return standardSizes
})
const sorteddefbearerData = computed(() =>
  sortData(defbearerDataAll.value, defbearerSort.value)
)

const paginateddefbearerData = computed(() => {
  const startIndex = (defbearerCurrentPage.value - 1) * defbearerPageSize.value
  const endIndex = startIndex + defbearerPageSize.value
  return sorteddefbearerData.value.slice(startIndex, endIndex)
})

const handledefbearerSortChange = ({
  prop,
  order,
}: SortParams<DefbearerStat>) => {
  defbearerSort.value = { prop, order: order || 'descending' }
}

// 默载批量调用情况统计相关
const defbearerBatchDataAll = ref<DefbearerBatchStat[]>([])
const defbearerBatchSort = ref<SortParams<DefbearerBatchStat>>({
  prop: 'count',
  order: 'descending',
})
const defbearerBatchCurrentPage = ref(1)
const defbearerBatchPageSize = ref(20)
const defbearerBatchTotalItems = ref(0)

const defbearerBatchPageSizes = computed(() => {
  const standardSizes = [10, 20, 50, 100]
  if (defbearerBatchTotalItems.value > 100) {
    return [...new Set([...standardSizes, defbearerBatchTotalItems.value])]
  }
  return standardSizes
})
const sorteddefbearerBatchData = computed(() =>
  sortData(defbearerBatchDataAll.value, defbearerBatchSort.value)
)

const paginateddefbearerBatchData = computed(() => {
  const startIndex =
    (defbearerBatchCurrentPage.value - 1) * defbearerBatchPageSize.value
  const endIndex = startIndex + defbearerBatchPageSize.value
  return sorteddefbearerBatchData.value.slice(startIndex, endIndex)
})

const handledefbearerBatchSortChange = ({
  prop,
  order,
}: SortParams<DefbearerBatchStat>) => {
  defbearerBatchSort.value = { prop, order: order || 'descending' }
}

// 专载调用情况统计相关
const dedbearerDataAll = ref<DedbearerStat[]>([])
const dedbearerSort = ref<SortParams<DedbearerStat>>({
  prop: 'count',
  order: 'descending',
})
const dedbearerCurrentPage = ref(1)
const dedbearerPageSize = ref(20)
const dedbearerTotalItems = ref(0)

const dedbearerPageSizes = computed(() => {
  const standardSizes = [10, 20, 50, 100]
  if (dedbearerTotalItems.value > 100) {
    return [...new Set([...standardSizes, dedbearerTotalItems.value])]
  }
  return standardSizes
})
const sorteddedbearerData = computed(() =>
  sortData(dedbearerDataAll.value, dedbearerSort.value)
)

const paginateddedbearerData = computed(() => {
  const startIndex = (dedbearerCurrentPage.value - 1) * dedbearerPageSize.value
  const endIndex = startIndex + dedbearerPageSize.value
  return sorteddedbearerData.value.slice(startIndex, endIndex)
})

const handlededbearerSortChange = ({
  prop,
  order,
}: SortParams<DedbearerStat>) => {
  dedbearerSort.value = { prop, order: order || 'descending' }
}

// 方法：获取数据
const fetchData = async () => {
  // Reset sort states to default on every query
  httpErrorSort.value = { prop: 'count', order: 'descending' }
  subscriptionSort.value = { prop: 'count', order: 'descending' }
  defbearerSort.value = { prop: 'count', order: 'descending' }
  defbearerBatchSort.value = { prop: 'count', order: 'descending' }
  dedbearerSort.value = { prop: 'count', order: 'descending' }

  // Clear table sort UI
  httpErrorTableRef.value?.clearSort()
  subscriptionTableRef.value?.clearSort()
  defbearerTableRef.value?.clearSort()
  defbearerBatchTableRef.value?.clearSort()
  dedbearerTableRef.value?.clearSort()

  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    ElMessage.error('起止时间不能为空')
    return
  }

  const [startTimeStr, endTimeStr] = dateRange.value
  const startTime = new Date(startTimeStr)
  const endTime = new Date(endTimeStr)

  const maxendTime = new Date(startTime)
  maxendTime.setMonth(maxendTime.getMonth() + 3)

  if (endTime > maxendTime) {
    ElMessage.error('时间间隔不能超过3个月')
    return
  }

  loading.value = true
  try {
    const params = {
      startTime: startTimeStr,
      endTime: endTimeStr,
    }

    const [
      httpErrorResponse,
      subscriptionResponse,
      defbearerResponse,
      defbearerBatchResponse,
      dedbearerResponse,
    ] = await Promise.all([
      request.post('/admin/inspection/httpFailStats/1.0', params),
      request.post('/admin/inspection/subscriptionStats/1.0', params),
      request.post('/admin/inspection/defbearerStats/1.0', params),
      request.post('/admin/inspection/defbearerBatchStats/1.0', params),
      request.post('/admin/inspection/dedbearerStats/1.0', params),
    ])

    // 获取HTTP失败统计数据
    httpErrorDataAll.value = httpErrorResponse.data || []
    processHttpErrorTableData()

    // 获取业务订购与退订统计数据
    subscriptionDataAll.value = subscriptionResponse.data || []
    processSubscriptionTableData()

    // 获取默载调用情况统计数据
    defbearerDataAll.value = defbearerResponse.data || []
    processdefbearerTableData()

    // 获取默载批量调用情况统计数据
    defbearerBatchDataAll.value = defbearerBatchResponse.data || []
    processdefbearerBatchTableData()

    // 获取专载调用情况统计数据
    dedbearerDataAll.value = dedbearerResponse.data || []
    processdedbearerTableData()
  } catch (error: any) {
    if (!error.isHandled) {
      ElMessage.error('获取数据失败: ' + (error.message || '未知错误'))
    }
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理HTTP错误表格数据
const processHttpErrorTableData = () => {
  httpErrorTotalItems.value = httpErrorDataAll.value.length
  httpErrorCurrentPage.value = 1
  httpErrorPageSize.value = 20
}

// 处理用户订购与退订表格数据
const processSubscriptionTableData = () => {
  subscriptionTotalItems.value = subscriptionDataAll.value.length
  subscriptionCurrentPage.value = 1
  subscriptionPageSize.value = 20
}

// 处理默载调用表格数据
const processdefbearerTableData = () => {
  defbearerTotalItems.value = defbearerDataAll.value.length
  defbearerCurrentPage.value = 1
  defbearerPageSize.value = 20
}

// 处理默载批量调用表格数据
const processdefbearerBatchTableData = () => {
  defbearerBatchTotalItems.value = defbearerBatchDataAll.value.length
  defbearerBatchCurrentPage.value = 1
  defbearerBatchPageSize.value = 20
}

// 处理专载调用表格数据
const processdedbearerTableData = () => {
  dedbearerTotalItems.value = dedbearerDataAll.value.length
  dedbearerCurrentPage.value = 1
  dedbearerPageSize.value = 20
}

// 重置筛选条件
const resetFilters = () => {
  const yesterday = getYesterdayDate()
  dateRange.value = [yesterday, yesterday]
}

// 用户订购与退订表格分页处理
const handleSubscriptionSizeChange = (val: number) => {
  subscriptionPageSize.value = val
  subscriptionCurrentPage.value = 1
}

const handleSubscriptionCurrentChange = (val: number) => {
  subscriptionCurrentPage.value = val
}

// 默载调用表格分页处理
const handledefbearerSizeChange = (val: number) => {
  defbearerPageSize.value = val
  defbearerCurrentPage.value = 1
}

const handledefbearerCurrentChange = (val: number) => {
  defbearerCurrentPage.value = val
}

// 默载批量调用表格分页处理
const handledefbearerBatchSizeChange = (val: number) => {
  defbearerBatchPageSize.value = val
  defbearerBatchCurrentPage.value = 1
}

const handledefbearerBatchCurrentChange = (val: number) => {
  defbearerBatchCurrentPage.value = val
}

// 专载调用表格分页处理
const handlededbearerSizeChange = (val: number) => {
  dedbearerPageSize.value = val
  dedbearerCurrentPage.value = 1
}

const handlededbearerCurrentChange = (val: number) => {
  dedbearerCurrentPage.value = val
}

const showAllData = () => {
  httpErrorPageSize.value = httpErrorTotalItems.value
  httpErrorCurrentPage.value = 1
  subscriptionPageSize.value = subscriptionTotalItems.value
  subscriptionCurrentPage.value = 1
  defbearerPageSize.value = defbearerTotalItems.value
  defbearerCurrentPage.value = 1
  defbearerBatchPageSize.value = defbearerBatchTotalItems.value
  defbearerBatchCurrentPage.value = 1
  dedbearerPageSize.value = dedbearerTotalItems.value
  dedbearerCurrentPage.value = 1
}

// 导出功能处理
const handleExportCommand = (command: string) => {
  if (command === 'png') {
    exportToPNG()
  } else if (command === 'excel') {
    exportToExcel()
  } else if (command === 'pdf') {
    exportToPDF()
  }
}

// 导出为PNG
const exportToPNG = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成PNG图片，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    // 创建临时容器
    const tempContainer = document.createElement('div')
    tempContainer.style.position = 'fixed'
    tempContainer.style.top = '-10000px'
    tempContainer.style.left = '-10000px'
    tempContainer.style.width = '1920px'
    tempContainer.style.zIndex = '-1'
    document.body.appendChild(tempContainer)

    // 创建Vue应用实例
    const app = createApp(ReportView, {
      dateRange: dateRange.value,
      httpErrorData: httpErrorDataAll.value,
      subscriptionData: subscriptionDataAll.value,
      defbearerData: defbearerDataAll.value,
      dedbearerData: dedbearerDataAll.value,
      defbearerBatchData: defbearerBatchDataAll.value,
    })

    app.mount(tempContainer)

    await nextTick()
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 生成截图
    const canvas = await html2canvas(tempContainer, {
      width: 1920,
      height: tempContainer.scrollHeight,
      scale: 2,
      useCORS: true,
      allowTaint: false,
      backgroundColor: null,
    })

    // 下载图片
    const link = document.createElement('a')
    link.download = `QoS质量分析报告_${dateRange.value?.[0] || ''}_${dateRange.value?.[1] || ''}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()

    // 清理
    app.unmount()
    document.body.removeChild(tempContainer)

    ElMessage.success('PNG图片导出成功！')
  } catch (error) {
    console.error('PNG导出失败:', error)
    ElMessage.error('PNG导出失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 导出为Excel
const exportToExcel = () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成Excel文件，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    const wb = XLSX.utils.book_new()

    // 报告概要
    const summaryData = [
      ['QoS质量分析报告'],
      [''],
      [
        '查询时间范围',
        `${dateRange.value?.[0] || ''} 至 ${dateRange.value?.[1] || ''}`,
      ],
      ['生成时间', new Date().toLocaleString('zh-CN')],
      [''],
      ['数据统计概要'],
      ['网关异常记录数', httpErrorDataAll.value.length],
      ['订购退订记录数', subscriptionDataAll.value.length],
      ['默载调用记录数', defbearerDataAll.value.length],
      ['专载调用记录数', dedbearerDataAll.value.length],
      ['批量调用记录数', defbearerBatchDataAll.value.length],
    ]
    const summaryWS = XLSX.utils.aoa_to_sheet(summaryData)
    XLSX.utils.book_append_sheet(wb, summaryWS, '报告概要')

    // 网关异常统计
    if (httpErrorDataAll.value.length > 0) {
      const httpErrorWS = XLSX.utils.json_to_sheet(
        httpErrorDataAll.value.map((item) => ({
          接口URI: item.requestUri,
          响应码: item.respCode,
          响应描述: item.respDesc,
          异常次数: item.count,
        }))
      )
      XLSX.utils.book_append_sheet(wb, httpErrorWS, '网关异常统计')
    }

    // 订购退订统计
    if (subscriptionDataAll.value.length > 0) {
      const subscriptionWS = XLSX.utils.json_to_sheet(
        subscriptionDataAll.value.map((item) => ({
          操作类型: item.operTypeName,
          响应代码: item.respCode,
          响应描述: item.respDesc,
          操作次数: item.count,
        }))
      )
      XLSX.utils.book_append_sheet(wb, subscriptionWS, '订购退订统计')
    }

    // 默载调用统计
    if (defbearerDataAll.value.length > 0) {
      const defbearerWS = XLSX.utils.json_to_sheet(
        defbearerDataAll.value.map((item) => ({
          订单来源: item.orderSourceName,
          操作类型: item.operTypeName,
          响应代码: item.respCode,
          响应描述: item.respDesc,
          调用次数: item.count,
        }))
      )
      XLSX.utils.book_append_sheet(wb, defbearerWS, '默载调用统计')
    }

    // 专载调用统计
    if (dedbearerDataAll.value.length > 0) {
      const dedbearerWS = XLSX.utils.json_to_sheet(
        dedbearerDataAll.value.map((item) => ({
          订单来源: item.orderSourceName,
          操作类型: item.operTypeName,
          响应代码: item.respCode,
          响应描述: item.respDesc,
          调用次数: item.count,
        }))
      )
      XLSX.utils.book_append_sheet(wb, dedbearerWS, '专载调用统计')
    }

    // 批量调用统计
    if (defbearerBatchDataAll.value.length > 0) {
      const batchWS = XLSX.utils.json_to_sheet(
        defbearerBatchDataAll.value.map((item) => ({
          归属省: item.homeProvinceName,
          响应代码: item.respCode,
          响应描述: item.respDesc,
          调用次数: item.count,
        }))
      )
      XLSX.utils.book_append_sheet(wb, batchWS, '批量调用统计')
    }

    XLSX.writeFile(
      wb,
      `QoS质量分析报告_${dateRange.value?.[0] || ''}_${dateRange.value?.[1] || ''}.xlsx`
    )
    ElMessage.success('Excel文件导出成功！')
  } catch (error) {
    console.error('Excel导出失败:', error)
    ElMessage.error('Excel导出失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 导出为PDF
const exportToPDF = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成PDF报告，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    // 创建临时容器
    const tempContainer = document.createElement('div')
    tempContainer.style.position = 'fixed'
    tempContainer.style.top = '-10000px'
    tempContainer.style.left = '-10000px'
    tempContainer.style.width = '1920px'
    tempContainer.style.zIndex = '-1'
    document.body.appendChild(tempContainer)

    // 创建Vue应用实例
    const app = createApp(ReportView, {
      dateRange: dateRange.value,
      httpErrorData: httpErrorDataAll.value,
      subscriptionData: subscriptionDataAll.value,
      defbearerData: defbearerDataAll.value,
      dedbearerData: dedbearerDataAll.value,
      defbearerBatchData: defbearerBatchDataAll.value,
    })

    app.mount(tempContainer)

    await nextTick()
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 生成截图 - 优化设置以减小文件大小
    const canvas = await html2canvas(tempContainer, {
      width: 1920,
      height: tempContainer.scrollHeight,
      scale: 1.2, // 降低分辨率
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
    })

    // 转换为JPEG格式以减小文件大小
    const imgData = canvas.toDataURL('image/jpeg', 0.85) // 85%质量

    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = pdf.internal.pageSize.getHeight()

    // 计算图片尺寸
    const imgWidth = canvas.width
    const imgHeight = canvas.height
    const ratio = Math.min(
      (pdfWidth - 40) / imgWidth,
      (pdfHeight - 40) / imgHeight
    ) // 20mm边距

    const scaledWidth = imgWidth * ratio
    const scaledHeight = imgHeight * ratio

    // 如果内容太长，需要分页
    const maxHeightPerPage = pdfHeight - 40 // 减去上下边距
    let yPosition = 0
    let pageCount = 1

    while (yPosition < scaledHeight) {
      if (pageCount > 1) {
        pdf.addPage()
      }

      const remainingHeight = scaledHeight - yPosition
      const currentPageHeight = Math.min(maxHeightPerPage, remainingHeight)

      // 添加图片到PDF
      pdf.addImage(
        imgData,
        'JPEG',
        20, // x位置，20mm边距
        20, // y位置，20mm边距
        scaledWidth,
        currentPageHeight,
        undefined,
        'FAST' // 压缩模式
      )

      yPosition += currentPageHeight
      pageCount++
    }

    // 保存PDF
    pdf.save(
      `QoS质量分析报告_${dateRange.value?.[0] || ''}_${dateRange.value?.[1] || ''}.pdf`
    )

    // 清理
    app.unmount()
    document.body.removeChild(tempContainer)

    ElMessage.success('PDF报告导出成功！')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
@import '../../styles/dashboard.css';

.dashboard-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.data-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.mt-20 {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 按钮样式优化 */
.show-all-btn {
  min-width: 120px;
  padding: 10px 20px;
  margin-right: 12px;
}

.export-dropdown {
  margin-left: 0;
}

.export-btn {
  min-width: 140px;
  padding: 10px 20px;
}

.anomaly-btn {
  min-width: 120px;
  padding: 10px 20px;
  margin-left: 12px;
}
</style>
