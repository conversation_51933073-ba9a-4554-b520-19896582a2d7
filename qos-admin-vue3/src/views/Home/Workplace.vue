<template>
  <div class="workplace-container">
    <h1>工作台</h1>
    <div class="workplace-content">
      <!-- 生产环境区域 -->
      <div class="resource-section">
        <div class="section-header">
          <div class="title-icon">
            <el-icon>
              <Setting />
            </el-icon>
          </div>
          <h2 class="section-title">生产环境</h2>
        </div>

        <div class="resource-grid">
          <!-- 第一行资源卡片 -->
          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://docs.dingtalk.com/i/spaces/r9xmybZ4nqwYQGEO/overview'
              )
            "
          >
            <h3 class="card-title">切片研发空间</h3>
            <p class="card-desc">切片研发钉钉空间，用于研发人员交流</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('https://aiportal.chinaunicom.cn/')"
          >
            <h3 class="card-title">联通OA</h3>
            <p class="card-desc">官方OA系统，可跳转天梯研发平台</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://************/assets-platform/rd/feature-team-metric'
              )
            "
          >
            <h3 class="card-title">研发效能指标</h3>
            <p class="card-desc">研发效能平台 - 特性小组指标</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'http://**************:8080/sonar-2/dashboard?id=slice-qos'
              )
            "
          >
            <h3 class="card-title card-title-atrust">天梯SonarQube</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://172.24.127.176:40612/#/overview?namespace=default'
              )
            "
          >
            <h3 class="card-title">天宫K8s</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.122.5:38088/xxl-job-admin/')"
          >
            <h3 class="card-title">任务调度中心</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.122.5:38848/nacos/#/')"
          >
            <h3 class="card-title">Nacos</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.122.5:48080/#/')"
          >
            <h3 class="card-title">DubboAdmin</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://10.184.173.169:5601/app/home#/')"
          >
            <h3 class="card-title card-title-atrust">Elastic</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.122.5:58088/?orgId=4')"
          >
            <h3 class="card-title">Grafana</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'http://172.24.122.5:15200/General-Service/Services'
              )
            "
          >
            <h3 class="card-title">Skywalking</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://cke.console.tg.ncmp.unicom.local/#/overview'
              )
            "
          >
            <h3 class="card-title">天宫CKE</h3>
          </div>

          <!-- 第三行资源卡片 -->
          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://10.184.153.188:10443/uss2/#/asset/my-asset'
              )
            "
          >
            <h3 class="card-title card-title-atrust">水滴云管理平台</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
              class="resource-card"
              @click="
              handleCardClick(
                'http://10.186.2.232:25008/netoper/networkoption/networkserve/policyopt'
              )
            "
          >
            <h3 class="card-title card-title-atrust">切片平台网络运营模块</h3>
            <p class="card-desc">aTrust</p>
          </div>
        </div>
      </div>

      <!-- 测试环境区域 -->
      <div class="resource-section">
        <div class="section-header">
          <div class="title-icon title-icon-blue">
            <el-icon>
              <Monitor />
            </el-icon>
          </div>
          <h2 class="section-title">测试环境</h2>
        </div>

        <div class="resource-grid">
          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://172.24.128.18:41546/#/overview?namespace=slice'
              )
            "
          >
            <h3 class="card-title">K8s</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick('http://172.24.131.134:38088/xxl-job-admin/')
            "
          >
            <h3 class="card-title">任务调度中心</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.131.134:38848/nacos/')"
          >
            <h3 class="card-title">Nacos</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.131.134:38080/#/')"
          >
            <h3 class="card-title">DubboAdmin</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.131.134:5999/app/home#/')"
          >
            <h3 class="card-title">Elastic</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://172.24.131.134:30000/?orgId=1')"
          >
            <h3 class="card-title">Grafana</h3>
            <p class="card-desc">v5VPN</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'http://172.24.131.134:15200/General-Service/Services'
              )
            "
          >
            <h3 class="card-title">Skywalking</h3>
            <p class="card-desc">v5VPN</p>
          </div>
        </div>
      </div>

      <!-- 联通云环境区域 -->
      <div class="resource-section">
        <div class="section-header">
          <div class="title-icon title-icon-green">
            <el-icon>
              <Service />
            </el-icon>
          </div>
          <h2 class="section-title">联通云环境</h2>
        </div>

        <div class="resource-grid">
          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://10.184.254.5:38443/#/pod?namespace=slice-qos'
              )
            "
          >
            <h3 class="card-title card-title-atrust">K8s</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://10.184.254.5:38088/xxl-job-admin')"
          >
            <h3 class="card-title card-title-atrust">任务调度中心</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://10.184.254.5:38848/nacos')"
          >
            <h3 class="card-title card-title-atrust">Nacos</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://10.184.254.5:38080/')"
          >
            <h3 class="card-title card-title-atrust">DubboAdmin</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="handleCardClick('http://10.184.254.5:3000/')"
          >
            <h3 class="card-title card-title-atrust">Grafana</h3>
            <p class="card-desc">aTrust</p>
          </div>

          <div
            class="resource-card"
            @click="
              handleCardClick(
                'https://************:31090/wocloud/res/compute/ecs'
              )
            "
          >
            <h3 class="card-title card-title-atrust">联通云CSK</h3>
            <p class="card-desc">aTrust</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="Workplace">
import {Monitor, Service, Setting} from '@element-plus/icons-vue'

const handleCardClick = (url: string) => {
  window.open(url, '_blank', 'noopener')
}
</script>

<style scoped>
.workplace-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.workplace-content {
  margin-top: 20px;
}

.resource-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.title-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: #e6f7ff;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.title-icon-blue {
  background-color: #e8f4ff;
  color: #3f6ad8;
}

.title-icon-green {
  background-color: #e6fff7;
  color: #18b566;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.resource-card {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.resource-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #18b566;
  margin: 0 0 8px 0;
}

.card-title-atrust {
  color: #3f6ad8;
}

.card-desc {
  font-size: 12px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.card-link {
  font-size: 12px;
  color: #999;
  margin: 4px 0 0 0;
}

@media (max-width: 1200px) {
  .resource-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .resource-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .resource-grid {
    grid-template-columns: 1fr;
  }
}
</style>
