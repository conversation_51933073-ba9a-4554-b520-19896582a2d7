<script setup lang="ts">
import { MoreFilled, Plus, Reading, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref } from 'vue'
import { MockRule, MockRules } from '../../types'
import request from '../../utils/request'

let queryForm = reactive({
  mockAppName: '',
})

let dialogFormVisible = ref(false)
const formLabelWidth = '140px'
let loading = ref(false)

const emptyForm = reactive({
  id: 0,
  mockAppName: '',
  serviceName: '',
  serviceType: '',
  methodName: '',
  appName: '',
  mockFlag: false,
  mockType: '',
  mockContent: '',
  faultFlag: false,
  faultType: '',
  faultDetail: '',
})

let saveForm: MockRule = emptyForm

let mockRuleTable: MockRules = reactive([])
let currentPage = ref(1)
let pageSize = ref(10)

const pagedMockRuleTable = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return mockRuleTable.slice(start, end)
})

function onClick() {
  loading.value = true
  const params = {
    filter: queryForm.mockAppName,
  }

  request
    .get('/admin/mock/list', { params })
    .then((res) => {
      mockRuleTable.length = 0
      res.data.forEach((item: MockRule) => mockRuleTable.push(item))
    })
    .catch((error) => {
      // 如果错误已在拦截器中处理，不再重复显示
      if (!error.isHandled) {
        ElMessage.error('获取模拟规则失败：' + (error.message || '未知错误'))
      }
      console.error('获取模拟规则失败:', error)
    })
    .finally(() => {
      loading.value = false
    })
}

function onCreate() {
  saveForm = emptyForm
  dialogFormVisible.value = true
}

function onSave() {
  loading.value = true
  request
    .post('/admin/mock/save', saveForm)
    .then(() => {
      dialogFormVisible.value = false
      ElMessage.success('保存成功')
      onClick()
    })
    .catch((error) => {
      // 如果错误已在拦截器中处理，不再重复显示
      if (!error.isHandled) {
        ElMessage.error('保存失败：' + (error.message || '未知错误'))
      }
      console.error('保存失败:', error)
    })
    .finally(() => {
      loading.value = false
    })
}

function onEdit(mockRule: MockRule) {
  saveForm = mockRule
  dialogFormVisible.value = true
}

function onCopy(mockRule: MockRule) {
  saveForm = { ...mockRule, id: 0 }
  dialogFormVisible.value = true
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}
</script>

<template>
  <!--按模拟应用名称查询表单-->
  <el-form :model="queryForm" :inline="true" class="query-form">
    <el-form-item class="search-item">
      <el-input
        v-model="queryForm.mockAppName"
        placeholder="请输入模拟应用名称"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="button-group">
      <el-button
        type="primary"
        class="search-button"
        :loading="loading"
        @click="onClick"
      >
        <el-icon><Search /></el-icon>
        查询
      </el-button>
      <el-button type="success" class="create-button" @click="onCreate">
        <el-icon><Plus /></el-icon>
        创建
      </el-button>
    </el-form-item>
  </el-form>

  <!--创建表单-->
  <el-dialog v-model="dialogFormVisible" title="保存模拟规则">
    <el-form :model="saveForm">
      <el-form-item label="模拟应用名" :label-width="formLabelWidth">
        <el-input v-model="saveForm.mockAppName" autocomplete="off" />
      </el-form-item>
      <el-form-item label="应用名" :label-width="formLabelWidth">
        <el-input v-model="saveForm.appName" autocomplete="off" />
      </el-form-item>
      <el-form-item label="服务名" :label-width="formLabelWidth">
        <el-input v-model="saveForm.serviceName" autocomplete="off" />
      </el-form-item>
      <el-form-item label="服务类型" :label-width="formLabelWidth">
        <el-select v-model="saveForm.serviceType" placeholder="确定服务的类型">
          <el-option value="http" />
          <el-option value="dubbo" />
          <el-option value="kafka" />
        </el-select>
      </el-form-item>
      <el-form-item label="方法名" :label-width="formLabelWidth">
        <el-input v-model="saveForm.methodName" autocomplete="off" />
      </el-form-item>

      <el-form-item label="模拟数据构造方式" :label-width="formLabelWidth">
        <el-select
          v-model="saveForm.mockType"
          placeholder="确定模拟数据的构造方式"
        >
          <el-option value="plain text" />
          <el-option value="script" />
        </el-select>
      </el-form-item>
      <el-form-item label="模拟数据" :label-width="formLabelWidth">
        <el-input
          v-model="saveForm.mockContent"
          :rows="5"
          type="textarea"
          placeholder="Please input"
        />
      </el-form-item>
      <el-form-item label="模拟开关" :label-width="formLabelWidth">
        <el-switch
          v-model="saveForm.mockFlag"
          inline-prompt
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>

      <el-form-item label="故障类型" :label-width="formLabelWidth">
        <el-select
          v-model="saveForm.faultType"
          placeholder="确定模拟数据的构造方式"
        >
          <el-option value="exception" />
          <el-option value="delay" />
        </el-select>
      </el-form-item>
      <el-form-item label="故障配置" :label-width="formLabelWidth">
        <el-input
          v-model="saveForm.faultDetail"
          :rows="3"
          type="textarea"
          placeholder="Please input"
        />
      </el-form-item>
      <el-form-item label="故障开关" :label-width="formLabelWidth">
        <el-switch
          v-model="saveForm.faultFlag"
          inline-prompt
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onSave"
          >提交</el-button
        >
      </span>
    </template>
  </el-dialog>

  <!-- 规则列表表格 -->
  <p class="subtitle">规则列表</p>

  <el-table
    v-loading="loading"
    :data="pagedMockRuleTable"
    border
    class="custom-table"
  >
    <el-table-column prop="id" label="ID" min-width="60" show-overflow-tooltip>
    </el-table-column>

    <el-table-column
      prop="mockAppName"
      label="模拟应用名"
      min-width="120"
      show-overflow-tooltip
    >
    </el-table-column>

    <el-table-column
      prop="appName"
      label="应用名"
      min-width="120"
      show-overflow-tooltip
    >
    </el-table-column>

    <el-table-column prop="serviceName" label="服务名/URL" min-width="200">
      <template #default="{ row }">
        <el-tag type="danger" round class="service-type-tag">{{
          row.serviceType
        }}</el-tag>
        <span class="service-name">{{ row.serviceName }}</span>
      </template>
    </el-table-column>

    <el-table-column
      prop="methodName"
      label="方法名"
      min-width="120"
      show-overflow-tooltip
    >
    </el-table-column>

    <el-table-column label="模拟数据" min-width="160">
      <template #default="{ row }">
        <div class="mock-data">
          <template v-if="row.mockContent">
            <el-tag round>{{ row.mockType }}</el-tag>
            <el-popover placement="top-start" width="450" trigger="hover">
              <template #reference>
                <el-icon size="20px" class="more-icon">
                  <MoreFilled />
                </el-icon>
              </template>
              <p class="popover-content">{{ row.mockContent }}</p>
            </el-popover>
          </template>
          <template v-else>
            {{ '-' }}
          </template>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="模拟开关" min-width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.mockFlag ? 'success' : 'info'" disable-transitions>
          {{ row.mockFlag ? '是' : '否' }}
        </el-tag>
      </template>
    </el-table-column>

    <el-table-column label="故障配置" min-width="160">
      <template #default="{ row }">
        <div class="mock-data">
          <template v-if="row.faultDetail">
            <el-tag type="success" round>{{ row.faultType }}</el-tag>
            <el-popover placement="top-start" width="450" trigger="hover">
              <template #reference>
                <el-icon size="20px" class="more-icon">
                  <Reading />
                </el-icon>
              </template>
              <p class="popover-content">{{ row.faultDetail }}</p>
            </el-popover>
          </template>
          <template v-else>
            {{ '-' }}
          </template>
        </div>
      </template>
    </el-table-column>

    <el-table-column label="故障开关" min-width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.faultFlag ? 'danger' : 'info'" disable-transitions>
          {{ row.faultFlag ? '是' : '否' }}
        </el-tag>
      </template>
    </el-table-column>

    <el-table-column label="操作" min-width="120" fixed="right">
      <template #default="{ row }">
        <div class="operation-buttons">
          <el-button type="primary" size="small" @click="onEdit(row)"
            >编辑</el-button
          >
          <el-button type="primary" size="small" @click="onCopy(row)"
            >复制</el-button
          >
        </div>
      </template>
    </el-table-column>
  </el-table>

  <el-pagination
    background
    layout="total, sizes, prev, pager, next"
    class="pagination"
    :total="mockRuleTable.length"
    :current-page="currentPage"
    :page-size="pageSize"
    @current-change="handleCurrentChange"
    @size-change="handleSizeChange"
  >
  </el-pagination>
</template>

<style scoped>
.query-form {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  background: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.search-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-input {
  width: 300px;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  padding-left: 8px;
  transition: all 0.3s;
}

.search-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-input :deep(.el-input__prefix) {
  color: #909399;
  font-size: 16px;
  margin-right: 4px;
}

.button-group {
  margin: 0;
}

.button-group :deep(.el-button) {
  margin-right: 12px;
  padding: 8px 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  transition: all 0.3s;
}

.button-group :deep(.el-button:last-child) {
  margin-right: 0;
}

.button-group :deep(.el-icon) {
  margin-right: 4px;
  font-size: 16px;
}

.search-button {
  background: var(--el-color-primary);
}

.search-button:hover {
  background: var(--el-color-primary-light-3);
}

.create-button {
  background: var(--el-color-success);
}

.create-button:hover {
  background: var(--el-color-success-light-3);
}

.subtitle {
  text-align: left;
  font-weight: 500;
  margin-bottom: 16px;
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}

.custom-table {
  width: 100%;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table__header) {
  font-weight: 600;
}

:deep(.el-table__row) {
  transition: background-color 0.3s;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.mock-data {
  display: flex;
  align-items: center;
  gap: 8px;
}

.more-icon {
  cursor: pointer;
  color: #909399;
}

.more-icon:hover {
  color: #409eff;
}

.service-type-tag {
  margin-right: 8px;
}

.service-name {
  word-break: break-all;
}

.operation-buttons {
  display: flex;
  gap: 8px;
}

.popover-content {
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
