<script setup lang="ts">
import {
  CircleCheck,
  Clock,
  Connection,
  CreditCard,
  Document,
  Location,
  LocationInformation,
  Monitor,
  Phone,
  Platform,
  Star,
  User,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import QueryByPhoneNumberForm from '../../components/QueryByPhoneForm.vue' // NOSONAR
import { OpenAccountRecords, UserInfo } from '../../types'
import { getProvinceNameByCode } from '../../utils/provinceUtil'
import request from '../../utils/request'

const route = useRoute()

// 用于存储从URL传递过来的查询参数
const urlParams = reactive({
  phoneNumber: '',
})

let userInfo = reactive<UserInfo>({
  msisdn: '',
  imsi: '',
  homeProvince: 0,
  visitProvince: 0,
  signNetworkType: 0,
  isOpenUser: 0,
  openTime: '',
  qosLevel4g: 0,
  qosLevel5g: 0,
  userIpv4: '',
  userIpv6: '',
})

let openAccountRecordTable: OpenAccountRecords = reactive([])
let currentPage = ref(1)
let pageSize = ref(10)
let loading = ref(false)

const pagedOpenAccountRecordTable = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return openAccountRecordTable.slice(start, end)
})

const recordSize = computed(() => openAccountRecordTable.length)

function getSignNetworkType(signNetworkType: number) {
  return signNetworkType === 0 ? '4G' : '5G'
}

function getOpenStatus(openStatus: number) {
  return openStatus != null && openStatus === 0 ? '开户' : '销户'
}

function getQosLevel(qosLevel: number) {
  return qosLevel === 0 ? '-' : qosLevel.toString()
}

function onSubmit(phoneNumber: number) {
  loading.value = true
  const params = {
    userId: phoneNumber,
  }

  // 获取用户信息
  request
    .get('/admin/user-info/1.0', { params })
    .then((res) => {
      const data = res.data
      Object.assign(userInfo, data) // 保持响应式对象不变，只更新其属性
    })
    .catch((error) => {
      // 如果错误已在拦截器中处理，不再重复显示
      if (!error.isHandled) {
        ElMessage.error('获取用户信息失败：' + (error.message || '未知错误'))
      }
      console.error('获取用户信息失败:', error)
    })
    .finally(() => {
      loading.value = false
    })

  // 获取开销户记录
  request
    .get('/admin/user-open-account-record/1.0', { params })
    .then((res) => {
      openAccountRecordTable.length = 0 // 清空数组
      openAccountRecordTable.push(...res.data) // 添加新数据
    })
    .catch((error) => {
      // 如果错误已在拦截器中处理，不再重复显示
      if (!error.isHandled) {
        ElMessage.error('获取开销户记录失败：' + (error.message || '未知错误'))
      }
      console.error('获取开销户记录失败:', error)
    })
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

// 处理查询组件的加载状态
function handleQueryLoading(isLoading: boolean) {
  loading.value = isLoading
}

// 页面加载时检查URL参数
onMounted(() => {
  // 从URL查询参数中获取手机号
  const phoneNumberFromUrl = route.query.phoneNumber as string

  if (phoneNumberFromUrl) {
    urlParams.phoneNumber = phoneNumberFromUrl
    // 自动执行查询
    const phoneNumber = parseInt(phoneNumberFromUrl)
    if (!isNaN(phoneNumber)) {
      onSubmit(phoneNumber)
    }
  }
})
</script>

<template>
  <div class="user-info-container">
    <!-- 查询表单 -->
    <div class="query-section">
      <QueryByPhoneNumberForm
        :initial-phone-number="urlParams.phoneNumber"
        @click="onSubmit"
        @loading="handleQueryLoading"
      />
    </div>

    <!-- 用户信息展示 -->
    <el-card v-loading="loading" class="info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><User /></el-icon>
          <span class="header-title">用户信息</span>
        </div>
      </template>

      <div v-if="userInfo.msisdn !== ''" class="user-info-content">
        <el-descriptions
          class="user-descriptions"
          :column="2"
          size="large"
          border
          direction="horizontal"
        >
          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Phone /></el-icon>
                <span>手机号码</span>
              </div>
            </template>
            <el-tag type="primary" size="large">{{ userInfo.msisdn }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><CreditCard /></el-icon>
                <span>IMSI</span>
              </div>
            </template>
            {{ userInfo.imsi }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Location /></el-icon>
                <span>归属省</span>
              </div>
            </template>
            {{ getProvinceNameByCode(userInfo.homeProvince) }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><LocationInformation /></el-icon>
                <span>拜访省</span>
              </div>
            </template>
            {{ getProvinceNameByCode(userInfo?.visitProvince) || '-' }}
          </el-descriptions-item>

          <el-descriptions-item :span="2">
            <template #label>
              <div class="label-with-icon">
                <el-icon><Connection /></el-icon>
                <span>网络类型</span>
              </div>
            </template>
            <el-tag
              :type="userInfo.signNetworkType === 1 ? 'success' : 'info'"
              size="large"
            >
              {{ getSignNetworkType(userInfo.signNetworkType) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><CircleCheck /></el-icon>
                <span>开户状态</span>
              </div>
            </template>
            <el-tag
              :type="userInfo.isOpenUser === 0 ? 'success' : 'danger'"
              size="large"
              effect="dark"
            >
              {{ getOpenStatus(userInfo.isOpenUser) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Clock /></el-icon>
                <span>开户时间</span>
              </div>
            </template>
            {{ userInfo?.openTime || '-' }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Star /></el-icon>
                <span>4G QoS等级</span>
              </div>
            </template>
            <el-tag
              v-if="userInfo.qosLevel4g !== 0"
              type="warning"
              size="large"
            >
              {{ getQosLevel(userInfo.qosLevel4g) }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Star /></el-icon>
                <span>5G QoS等级</span>
              </div>
            </template>
            <el-tag
              v-if="userInfo.qosLevel5g !== 0"
              type="success"
              size="large"
            >
              {{ getQosLevel(userInfo.qosLevel5g) }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Monitor /></el-icon>
                <span>私网IPv4</span>
              </div>
            </template>
            <code class="ip-address">{{ userInfo.userIpv4 || '-' }}</code>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="label-with-icon">
                <el-icon><Monitor /></el-icon>
                <span>私网IPv6</span>
              </div>
            </template>
            <code class="ip-address">{{ userInfo.userIpv6 || '-' }}</code>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-else class="table-container">
        <div class="empty-table-style">暂无用户信息</div>
      </div>
    </el-card>

    <!-- 开销户记录表格 -->
    <el-card v-loading="loading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">用户开销户记录</span>
          <el-tag v-if="recordSize > 0" type="info" class="record-count">
            共 {{ recordSize }} 条记录
          </el-tag>
        </div>
      </template>

      <div class="table-container">
        <el-table
          :data="pagedOpenAccountRecordTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无记录数据"
        >
          <el-table-column
            prop="orderTime"
            label="请求时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Clock /></el-icon>
                {{ row.orderTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="messageId"
            label="MessageId"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code class="message-id">{{ row.messageId }}</code>
            </template>
          </el-table-column>

          <el-table-column
            prop="operType"
            label="操作类型"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.operType === 0 ? 'success' : 'danger'"
                effect="dark"
                size="small"
              >
                {{ getOpenStatus(row.operType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="signNetworkType"
            label="网络类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.signNetworkType === 1 ? 'success' : 'info'"
                size="small"
              >
                {{ getSignNetworkType(row.signNetworkType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="respCode"
            label="响应码"
            min-width="80"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.respCode === 0 ? 'success' : 'danger'"
                size="small"
                round
              >
                {{ row.respCode }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="respDesc"
            label="响应描述"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                :class="{
                  'success-text': row.respCode === 0,
                  'error-text': row.respCode !== 0,
                }"
              >
                {{ row.respDesc }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            prop="operSource"
            label="请求来源"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="source-cell">
                <el-icon class="source-icon"><Platform /></el-icon>
                {{ row.operSource }}
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="recordSize"
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            class="custom-pagination"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.user-info-container {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 200px);
}

/* 查询区域样式 */
.query-section {
  margin-bottom: 24px;
}

/* 卡片样式 */
.info-card,
.records-card {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover,
.records-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  font-size: 18px;
  color: #3b82f6;
}

.header-title {
  font-size: 16px;
}

.record-count {
  margin-left: auto;
}

/* 用户信息内容 */
.user-info-content {
  padding: 16px 0;
}

.user-descriptions {
  border-radius: 8px;
  overflow: hidden;
}

.user-descriptions :deep(.el-descriptions__label) {
  width: 160px !important;
  min-width: 160px !important;
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #374151;
}

.label-with-icon .el-icon {
  font-size: 14px;
  color: #6b7280;
}

.ip-address {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1f2937;
  border: 1px solid #e5e7eb;
}

.no-data {
  color: #9ca3af;
  font-style: italic;
}

/* 空状态样式已移至 .empty-table-style */

.empty-table-style {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 60px 0;
  font-size: 14px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 表格容器 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.custom-table :deep(.el-table__header) {
  background: #f8fafc;
}

.custom-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.custom-table :deep(.el-table__cell) {
  padding: 16px 12px;
}

/* 表格内容样式 */
.time-cell,
.source-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-icon,
.source-icon {
  font-size: 14px;
  color: #6b7280;
}

.message-id {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.success-text {
  color: #059669;
  font-weight: 500;
}

.error-text {
  color: #dc2626;
  font-weight: 500;
}

/* 分页样式 */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.custom-pagination {
  background: transparent;
}

.custom-pagination :deep(.el-pagination__total) {
  color: #6b7280;
  font-weight: 500;
}

.custom-pagination :deep(.el-pager li) {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-descriptions__label) {
  font-weight: 600 !important;
  color: #374151 !important;
  background: #f9fafb !important;
}

:deep(.el-descriptions__content) {
  color: #1f2937 !important;
}

:deep(.el-card__header) {
  background: #ffffff;
  border-bottom: 1px solid #f3f4f6;
  padding: 20px 24px;
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info-container {
    padding: 16px;
  }

  .user-descriptions :deep(.el-descriptions__label) {
    width: 100px !important;
    min-width: 100px !important;
  }

  .user-descriptions {
    --el-descriptions-table-header-cell-width: 120px;
  }

  .pagination-container {
    justify-content: center;
  }

  .custom-table :deep(.el-table__cell) {
    padding: 12px 8px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .record-count {
    margin-left: 0;
  }

  .user-descriptions :deep(.el-descriptions__label) {
    width: 120px !important;
    min-width: 120px !important;
  }
}
</style>
