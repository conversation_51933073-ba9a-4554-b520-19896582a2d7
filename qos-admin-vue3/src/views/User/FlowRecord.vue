<script setup lang="ts">
import { Document, Timer } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import CmServiceInfoDialog from '../../components/CmServiceInfoDialog.vue'
import QosProductInfoDialog from '../../components/QosProductInfoDialog.vue'
import QueryByPhoneNumberAndOrderForm from '../../components/QueryByPhoneNumberAndOrderForm.vue'
import {
  DedOrderFlowRecords,
  DefOrderFlowRecords,
  UserSubscribeFlowRecords,
} from '../../types'
import { getProvinceNameByCode } from '../../utils/provinceUtil'
import request from '../../utils/request'

const route = useRoute()

let defOrderRecordTable: DefOrderFlowRecords = reactive([])
let dedOrderRecordTable: DedOrderFlowRecords = reactive([])
let loading = ref(false)
let dedLoading = ref(false)
let currentPage = ref(1)
let pageSize = ref(10)
let dedCurrentPage = ref(1)
let dedPageSize = ref(10)
let subscribeRecordTable: UserSubscribeFlowRecords = reactive([])
let subscribeLoading = ref(false)
let subscribeCurrentPage = ref(1)
let subscribePageSize = ref(10)

// 用于存储从URL传递过来的查询参数
const urlParams = reactive({
  phoneNumber: '',
  orderId: '',
  startTime: '',
  dateRangeStart: '',
  dateRangeEnd: '',
})

// 默认日期范围，用于存储通过startTime构建的日期范围或直接传入的日期范围
const defaultDateRange = ref<[string, string] | []>([])

// QoS产品信息弹窗相关状态
const productDialogVisible = ref(false)
const selectedProductId = ref<number | null>(null)

// 显示产品信息弹窗
const showProductInfo = (qosProductId: number) => {
  selectedProductId.value = qosProductId
  productDialogVisible.value = true
}

// 通信服务信息弹窗相关状态
const serviceDialogVisible = ref(false)
const selectedServiceId = ref<string | null>(null)

// 显示通信服务信息弹窗
const showServiceInfo = (cmServiceId: string) => {
  selectedServiceId.value = cmServiceId
  serviceDialogVisible.value = true
}

const pagedDefOrderRecordTable = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return defOrderRecordTable.slice(start, end)
})

const pagedDedOrderRecordTable = computed(() => {
  const start = (dedCurrentPage.value - 1) * dedPageSize.value
  const end = start + dedPageSize.value
  return dedOrderRecordTable.slice(start, end)
})

const pagedSubscribeRecordTable = computed(() => {
  const start = (subscribeCurrentPage.value - 1) * subscribePageSize.value
  const end = start + subscribePageSize.value
  return subscribeRecordTable.slice(start, end)
})

// 组件挂载时从URL读取参数
onMounted(() => {
  // 从URL获取参数
  if (route.query.phoneNumber) {
    urlParams.phoneNumber = route.query.phoneNumber as string
  }

  if (route.query.orderId) {
    urlParams.orderId = route.query.orderId as string
  }

  if (route.query.dateRangeStart) {
    urlParams.dateRangeStart = route.query.dateRangeStart as string
  }

  if (route.query.dateRangeEnd) {
    urlParams.dateRangeEnd = route.query.dateRangeEnd as string
  }

  if (route.query.startTime) {
    urlParams.startTime = route.query.startTime as string
  }

  // 优先使用dateRangeStart和dateRangeEnd，如果没有则使用startTime创建范围
  if (urlParams.dateRangeStart && urlParams.dateRangeEnd) {
    // 直接使用传入的日期范围
    defaultDateRange.value = [urlParams.dateRangeStart, urlParams.dateRangeEnd]
  } else if (urlParams.startTime) {
    // 通过startTime创建一个日期范围（前后各15天）
    try {
      const startDate = new Date(urlParams.startTime)

      // 计算开始日期（startTime前15天）
      const beginDate = new Date(startDate)
      beginDate.setDate(beginDate.getDate() - 15)

      // 计算结束日期（startTime后15天）
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 15)

      // 格式化日期
      const formatDate = (date: Date): string => {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${year}-${month}-${day}`
      }

      defaultDateRange.value = [formatDate(beginDate), formatDate(endDate)]
    } catch (e) {
      console.error('无法解析开始时间:', e)
    }
  }

  // 如果有URL参数，自动触发查询
  if (urlParams.phoneNumber) {
    // 延迟一下以确保组件已经加载完成
    setTimeout(() => {
      const phoneNumber = Number(urlParams.phoneNumber)
      onSubmit({
        phoneNumber,
        orderId: urlParams.orderId,
        dateRange: defaultDateRange.value.length ? defaultDateRange.value : [],
      })
    }, 100)
  }
})

function onSubmit({
  phoneNumber,
  orderId,
  dateRange,
}: {
  phoneNumber: number
  orderId: string
  dateRange: [string, string] | []
}) {
  loading.value = true
  dedLoading.value = true
  subscribeLoading.value = true

  // 检查日期范围是否已选择
  if (!dateRange || dateRange.length !== 2) {
    // 如果未选择日期范围，显示错误提示
    loading.value = false
    dedLoading.value = false
    subscribeLoading.value = false
    ElMessage.error('请选择日期范围，查询时间跨度不能超过1个月')
    return
  }

  // 构建请求参数
  const params: any = {
    createTimeBegin: `${dateRange[0]} 00:00:00`,
    createTimeEnd: `${dateRange[1]} 23:59:59`,
  }

  // 根据输入条件添加查询参数
  if (phoneNumber) {
    params.msisdn = String(phoneNumber)
  }

  if (orderId) {
    params.orderId = orderId
  }

  // 如果没有提供手机号，提示用户
  if (!phoneNumber) {
    loading.value = false
    dedLoading.value = false
    subscribeLoading.value = false
    ElMessage.warning('至少输入手机号进行查询')
    return
  }

  // 调用默载流水记录接口
  request
    .post('/manage/user/single/defbearer/queryOrderRecords/1.0', params)
    .then((res) => {
      defOrderRecordTable.length = 0
      if (res.data) {
        defOrderRecordTable.push(...res.data)

        // 按订单时间倒序排序
        defOrderRecordTable.sort((a, b) => {
          return (
            new Date(b.orderTime).getTime() - new Date(a.orderTime).getTime()
          )
        })
      }
    })
    .catch((error) => {
      console.error('获取默载流水记录失败:', error)
      if (error.response?.data?.msg) {
        ElMessage.error(error.response.data.msg)
      } else {
        ElMessage.error('获取默载流水记录失败')
      }
    })
    .finally(() => {
      loading.value = false
    })

  // 调用专载流水记录接口
  request
    .post('/manage/user/single/dedbearer/queryOrderRecords/1.0', params)
    .then((res) => {
      dedOrderRecordTable.length = 0
      if (res.data) {
        dedOrderRecordTable.push(...res.data)

        // 按订单时间倒序排序
        dedOrderRecordTable.sort((a, b) => {
          return (
            new Date(b.orderTime).getTime() - new Date(a.orderTime).getTime()
          )
        })
      }
    })
    .catch((error) => {
      console.error('获取专载流水记录失败:', error)
      if (error.response?.data?.msg) {
        ElMessage.error(error.response.data.msg)
      } else {
        ElMessage.error('获取专载流水记录失败')
      }
    })
    .finally(() => {
      dedLoading.value = false
    })

  // 订购退订流水记录接口
  request
    .post('/manage/user/single/queryUserSubscribeRecords/1.0', {
      msisdn: phoneNumber ? String(phoneNumber) : undefined,
      orderId: orderId || undefined,
      createTimeBegin: dateRange[0] + ' 00:00:00',
      createTimeEnd: dateRange[1] + ' 23:59:59',
    })
    .then((res) => {
      subscribeRecordTable.length = 0
      if (res.data) {
        subscribeRecordTable.push(...res.data)
        // 按创建时间倒序
        subscribeRecordTable.sort((a, b) => {
          return (
            new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
          )
        })
      }
    })
    .catch((error) => {
      console.error('获取订购退订流水失败:', error)
      if (error.response?.data?.msg) {
        ElMessage.error(error.response.data.msg)
      } else {
        ElMessage.error('获取订购退订流水失败')
      }
    })
    .finally(() => {
      subscribeLoading.value = false
    })
}

// 复制JSON数据到剪贴板
function copyToClipboard(text: string) {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      ElMessage.success('已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 获取操作类型文本 - 默载
function getDefOperTypeText(operType: number): string {
  switch (operType) {
    case 0:
      return '申请'
    case 1:
      return '修改'
    case 2:
      return '终止'
    case 3:
      return '按用户终止'
    case 4:
      return '系统终止'
    case 5:
      return '到时终止'
    default:
      return '未知'
  }
}

// 获取操作类型文本 - 专载
function getDedOperTypeText(operType: number): string {
  switch (operType) {
    case 0:
      return '申请'
    case 1:
      return '修改'
    case 2:
      return '终止'
    case 3:
      return '系统终止'
    case 4:
      return '到时终止'
    default:
      return '未知'
  }
}

// 获取操作类型文本 - 订购退订
function getSubscribeOperTypeText(operType: number): string {
  switch (operType) {
    case 0:
      return '订购'
    case 1:
      return '退订'
    default:
      return '未知'
  }
}

// 获取响应结果类型（用于标签颜色）
function getRespCodeType(respCode: number): string {
  if (respCode === 0) {
    return 'success'
  } else {
    return 'danger'
  }
}

// 获取承载模式文本
function getBearerModeText(bearerMode: number): string {
  switch (bearerMode) {
    case 0:
      return '专载'
    case 1:
      return '默载'
    case 2:
      return 'AM-PCF'
    default:
      return '未知'
  }
}

// 获取速度类型文本
function getSpeedTypeText(speedType: number): string {
  switch (speedType) {
    case 0:
      return '加速'
    case 1:
      return '限速'
    case 2:
      return '其他'
    default:
      return '未知'
  }
}

// 默载分页处理函数
const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

// 专载分页处理函数
const handleDedCurrentChange = (val: number) => {
  dedCurrentPage.value = val
}

const handleDedSizeChange = (val: number) => {
  dedPageSize.value = val
  dedCurrentPage.value = 1
}

const handleSubscribeCurrentChange = (val: number) => {
  subscribeCurrentPage.value = val
}

const handleSubscribeSizeChange = (val: number) => {
  subscribePageSize.value = val
  subscribeCurrentPage.value = 1
}
</script>

<template>
  <div class="flow-container">
    <!-- 查询表单 -->
    <div class="query-section">
      <QueryByPhoneNumberAndOrderForm
        :initial-phone-number="urlParams.phoneNumber"
        :initial-order-id="urlParams.orderId"
        :initial-date-range="defaultDateRange"
        @search="onSubmit"
      />
    </div>

    <!-- 产品订购退订流水卡片 -->
    <el-card v-loading="subscribeLoading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon">
            <Document />
          </el-icon>
          <span class="header-title">产品订购退订流水</span>
          <el-tag
            v-if="subscribeRecordTable.length > 0"
            type="info"
            class="record-count"
          >
            共 {{ subscribeRecordTable.length }} 条记录
          </el-tag>
        </div>
      </template>
      <div class="table-container">
        <el-table
          :data="pagedSubscribeRecordTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无订购退订流水记录"
        >
          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="operType"
            label="操作类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag type="warning" size="small">
                {{ getSubscribeOperTypeText(row.operType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="effectiveTime"
            label="生效时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.effectiveTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="expireTime"
            label="失效时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.expireTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.createTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="respCode"
            label="响应码"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="getRespCodeType(row.respCode)"
                effect="dark"
                size="small"
              >
                {{ row.respCode }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="respDesc"
            label="响应描述"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.respDesc || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="subscribeRecordTable.length"
            :current-page="subscribeCurrentPage"
            :page-size="subscribePageSize"
            :page-sizes="[10, 20, 50, 100]"
            class="custom-pagination"
            @current-change="handleSubscribeCurrentChange"
            @size-change="handleSubscribeSizeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 默载流水记录内容区域 -->
    <el-card v-loading="loading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon">
            <Document />
          </el-icon>
          <span class="header-title">默载流水记录</span>
          <el-tag
            v-if="defOrderRecordTable.length > 0"
            type="success"
            class="record-count"
          >
            共 {{ defOrderRecordTable.length }} 条记录
          </el-tag>
        </div>
      </template>

      <div class="table-container">
        <el-table
          :data="pagedDefOrderRecordTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无默载流水记录"
        >
          <el-table-column
            prop="orderIdStr"
            label="订单号"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-popover
                placement="right"
                :width="400"
                trigger="hover"
                popper-class="json-popover"
              >
                <template #reference>
                  <code class="order-id hover-effect">{{
                    row.orderIdStr
                  }}</code>
                </template>
                <div class="json-content">
                  <div class="json-header">
                    <span>流水详细数据</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="copyToClipboard(JSON.stringify(row, null, 2))"
                    >
                      复制
                    </el-button>
                  </div>
                  <pre>{{ JSON.stringify(row, null, 2) }}</pre>
                </div>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="cmServiceId"
            label="通信服务ID"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code
                v-if="row.cmServiceId"
                class="service-id clickable-service-id"
                @click="showServiceInfo(row.cmServiceId)"
              >
                {{ row.cmServiceId }}
              </code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="bearerMode"
            label="承载模式"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.bearerMode !== undefined"
                type="info"
                size="small"
              >
                {{ getBearerModeText(row.bearerMode) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="operType"
            label="操作类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag type="warning" size="small">
                {{ getDefOperTypeText(row.operType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="respCode"
            label="响应结果"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="resp-cell">
                <el-tag
                  :type="getRespCodeType(row.respCode)"
                  effect="dark"
                  size="small"
                  class="resp-code"
                >
                  {{ row.respCode }}
                </el-tag>
                <span v-if="row.respDesc" class="resp-desc">{{
                  row.respDesc
                }}</span>
                <span v-else class="resp-desc">-</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="speedType"
            label="速度类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.speedType !== undefined"
                type="info"
                size="small"
              >
                {{ getSpeedTypeText(row.speedType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderTime"
            label="订单时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon">
                  <Timer />
                </el-icon>
                {{ row.orderTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="returnTime"
            label="返回时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon">
                  <Timer />
                </el-icon>
                {{ row.returnTime || '-' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="duration"
            label="时长(秒)"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ row.duration || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="afid"
            label="网络策略ID"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.afid" class="af-id">{{ row.afid }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="homeProvince"
            label="归属省"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ getProvinceNameByCode(row.homeProvince) || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="visitProvince"
            label="拜访省"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ getProvinceNameByCode(row.visitProvince) || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderSource"
            label="订单来源"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.orderSource"
                type="info"
                size="small"
                effect="plain"
              >
                {{ row.orderSource }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="traceId"
            label="TraceId"
            min-width="240"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.traceId" class="trace-id">{{ row.traceId }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="defOrderRecordTable.length"
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            class="custom-pagination"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 专载流水记录内容区域 -->
    <el-card v-loading="dedLoading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon">
            <Document />
          </el-icon>
          <span class="header-title">专载流水记录</span>
          <el-tag
            v-if="dedOrderRecordTable.length > 0"
            type="primary"
            class="record-count"
          >
            共 {{ dedOrderRecordTable.length }} 条记录
          </el-tag>
        </div>
      </template>

      <div class="table-container">
        <el-table
          :data="pagedDedOrderRecordTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无专载流水记录"
        >
          <el-table-column
            prop="orderIdStr"
            label="订单号"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-popover
                placement="right"
                :width="400"
                trigger="hover"
                popper-class="json-popover"
              >
                <template #reference>
                  <code class="order-id hover-effect">{{
                    row.orderIdStr
                  }}</code>
                </template>
                <div class="json-content">
                  <div class="json-header">
                    <span>流水详细数据</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="copyToClipboard(JSON.stringify(row, null, 2))"
                    >
                      复制
                    </el-button>
                  </div>
                  <pre>{{ JSON.stringify(row, null, 2) }}</pre>
                </div>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="cmServiceId"
            label="通信服务ID"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code
                class="service-id clickable-service-id"
                @click="showServiceInfo(row.cmServiceId)"
              >
                {{ row.cmServiceId }}
              </code>
            </template>
          </el-table-column>

          <el-table-column
            prop="operType"
            label="操作类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag type="warning" size="small">
                {{ getDedOperTypeText(row.operType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="respCode"
            label="响应结果"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="resp-cell">
                <el-tag
                  :type="getRespCodeType(row.respCode)"
                  effect="dark"
                  size="small"
                  class="resp-code"
                >
                  {{ row.respCode }}
                </el-tag>
                <span v-if="row.respDesc" class="resp-desc">{{
                  row.respDesc
                }}</span>
                <span v-else class="resp-desc">-</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderTime"
            label="订单时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon">
                  <Timer />
                </el-icon>
                {{ row.orderTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="returnTime"
            label="返回时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon">
                  <Timer />
                </el-icon>
                {{ row.returnTime || '-' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="publicIp"
            label="公网IP"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.publicIp" class="ip-address">{{
                row.publicIp
              }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="privateIp"
            label="私网IP"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.privateIp" class="ip-address">{{
                row.privateIp
              }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="targetIp"
            label="目标IP"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.targetIp" class="ip-address">{{
                row.targetIp
              }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="duration"
            label="时长(秒)"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ row.duration || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="correlationId"
            label="会话ID"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.correlationId" class="correlation-id">{{
                row.correlationId
              }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="afid"
            label="网络策略ID"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.afid" class="af-id">{{ row.afid }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="neId"
            label="网元ID"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.neId" class="ne-id">{{ row.neId }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="homeProvince"
            label="归属省"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ getProvinceNameByCode(row.homeProvince) || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="visitProvince"
            label="拜访省"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ getProvinceNameByCode(row.visitProvince) || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderSource"
            label="订单来源"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.orderSource"
                type="info"
                size="small"
                effect="plain"
              >
                {{ row.orderSource }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="traceId"
            label="TraceId"
            min-width="240"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code v-if="row.traceId" class="trace-id">{{ row.traceId }}</code>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="dedOrderRecordTable.length"
            :current-page="dedCurrentPage"
            :page-size="dedPageSize"
            :page-sizes="[10, 20, 50, 100]"
            class="custom-pagination"
            @current-change="handleDedCurrentChange"
            @size-change="handleDedSizeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- QoS产品信息弹窗 -->
    <QosProductInfoDialog
      v-model:visible="productDialogVisible"
      :qos-product-id="selectedProductId"
    />

    <!-- 通信服务信息弹窗 -->
    <CmServiceInfoDialog
      v-model:visible="serviceDialogVisible"
      :cm-service-id="selectedServiceId"
    />
  </div>
</template>

<style scoped>
.flow-container {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 200px);
}

/* 查询区域样式 */
.query-section {
  margin-bottom: 24px;
}

/* 卡片样式 */
.records-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.records-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  font-size: 18px;
  color: #3b82f6;
}

.header-title {
  font-size: 16px;
}

.record-count {
  margin-left: auto;
}

/* 备注信息样式 */
.table-notice {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #d1ecf1;
  background-color: #f8f9fa;
}

.table-notice :deep(.el-alert__content) {
  font-size: 14px;
  color: #495057;
}

.table-notice :deep(.el-alert__title) {
  font-weight: 600;
  color: #31708f;
  margin-bottom: 4px;
}

.table-notice :deep(.el-alert__icon) {
  color: #31708f;
}

/* 表格容器 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.custom-table :deep(.el-table__header) {
  background: #f8fafc;
}

.custom-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.custom-table :deep(.el-table__cell) {
  padding: 16px 12px;
}

/* 表格内容样式 */
.time-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-icon {
  font-size: 14px;
  color: #6b7280;
}

.order-id {
  background: #eff6ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1e40af;
  border: 1px solid #bfdbfe;
  font-weight: 600;
}

.service-id {
  background: #f0fdf4;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #15803d;
  border: 1px solid #bbf7d0;
}

.resp-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resp-code {
  flex-shrink: 0;
}

.resp-desc {
  color: #4b5563;
  font-size: 13px;
  font-style: italic;
}

.af-id {
  background: #f0f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #2563eb;
  border: 1px solid #dbeafe;
}

.ne-id {
  background: #f3e8ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #7e22ce;
  border: 1px solid #e9d5ff;
}

.correlation-id {
  background: #f5f3ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #7c3aed;
  border: 1px solid #ede9fe;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
  display: inline-block;
}

.trace-id {
  background: #fef3f2;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #b91c1c;
  border: 1px solid #fee2e2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  display: inline-block;
}

.ip-address {
  background: #f0f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #2563eb;
  border: 1px solid #dbeafe;
}

/* 分页样式 */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.custom-pagination {
  background: transparent;
}

.custom-pagination :deep(.el-pagination__total) {
  color: #6b7280;
  font-weight: 500;
}

.custom-pagination :deep(.el-pager li) {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__header) {
  background: #ffffff;
  border-bottom: 1px solid #f3f4f6;
  padding: 20px 24px;
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flow-container {
    padding: 16px;
  }
}

/* 专载和默载记录卡片之间的间距 */
.records-card + .records-card {
  margin-top: 24px;
}

/* JSON悬浮窗样式 */
.json-content {
  max-height: 500px;
  overflow: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.json-content pre {
  margin: 0;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  white-space: pre-wrap;
  word-break: break-all;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
}

:deep(.json-popover) {
  max-width: 50vw;
  padding: 16px;
}

.hover-effect {
  cursor: pointer;
  transition: all 0.2s ease;
}

.hover-effect:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

/* QoS产品ID可点击样式 */
.clickable-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 通信服务ID可点击样式 */
.clickable-service-id {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 2px 6px;
  border-radius: 4px;
}

.clickable-service-id:hover {
  background-color: #f0f9ff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}
</style>
