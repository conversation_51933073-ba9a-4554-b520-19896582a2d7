<script setup lang="ts">
import {
  Box,
  Document,
  Phone,
  QuestionFilled,
  ShoppingCart,
  Timer,
} from '@element-plus/icons-vue'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import CmServiceInfoDialog from '../../components/CmServiceInfoDialog.vue'
import QosProductInfoDialog from '../../components/QosProductInfoDialog.vue'
import QueryByPhoneAndDateForm from '../../components/QueryByPhoneAndDateForm.vue'
import { useUserInvoke } from '../../composables/useUserInvoke'

const route = useRoute()

// 用于存储从URL传递过来的查询参数
const urlParams = reactive({
  phoneNumber: '',
  dateRangeStart: '',
  dateRangeEnd: '',
})

// 默认日期范围
const defaultDateRange = ref<[string, string] | []>([])

const {
  // State
  productSubscribeInfoTable,
  allOrderTable,
  allHistoryOrderTable,
  pagedHistoryOrderTable,
  dedOrderTable,
  defOrderTable,
  dedHistoryOrderTable,
  defHistoryOrderTable,
  loading,
  orderLoading,
  historyOrderLoading,
  historyCurrentPage,
  historyPageSize,

  // Methods & Handlers
  onSubmit,
  handleHistoryCurrentChange,
  handleHistorySizeChange,
  goToFlowRecord,

  // Helpers
  getStatusType,
  getStatusText,
  getOrderStatusType,
  getOrderStatusText,
  getBearerModeText,
  getSpeedTypeText,
  formatDuration,
} = useUserInvoke()

// 组件挂载时从URL读取参数
onMounted(() => {
  // 从URL获取参数
  if (route.query.phoneNumber) {
    urlParams.phoneNumber = route.query.phoneNumber as string
  }

  if (route.query.dateRangeStart) {
    urlParams.dateRangeStart = route.query.dateRangeStart as string
  }

  if (route.query.dateRangeEnd) {
    urlParams.dateRangeEnd = route.query.dateRangeEnd as string
  }

  // 如果有日期范围参数，设置默认日期范围
  if (urlParams.dateRangeStart && urlParams.dateRangeEnd) {
    defaultDateRange.value = [urlParams.dateRangeStart, urlParams.dateRangeEnd]
  }

  // 如果有URL参数，自动触发查询
  if (urlParams.phoneNumber) {
    // 延迟一下以确保组件已经加载完成
    setTimeout(() => {
      const phoneNumber = Number(urlParams.phoneNumber)
      onSubmit({
        phoneNumber,
        dateRange: defaultDateRange.value.length ? defaultDateRange.value : [],
      })
    }, 100)
  }
})

// QoS产品信息弹窗相关状态
const productDialogVisible = ref(false)
const selectedProductId = ref<number | null>(null)

// 显示产品信息弹窗
const showProductInfo = (qosProductId: number) => {
  selectedProductId.value = qosProductId
  productDialogVisible.value = true
}

// 通信服务信息弹窗相关状态
const serviceDialogVisible = ref(false)
const selectedServiceId = ref<string | null>(null)

// 显示通信服务信息弹窗
const showServiceInfo = (cmServiceId: string) => {
  selectedServiceId.value = cmServiceId
  serviceDialogVisible.value = true
}
</script>

<template>
  <div class="subscribe-container">
    <!-- 查询表单 -->
    <div class="query-section">
      <QueryByPhoneAndDateForm
        :initial-phone-number="urlParams.phoneNumber"
        :initial-date-range="defaultDateRange"
        @search="onSubmit"
      />
    </div>

    <!-- 产品订购记录表格 -->
    <el-card v-loading="loading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><ShoppingCart /></el-icon>
          <span class="header-title">产品订购记录</span>
          <el-tag
            v-if="productSubscribeInfoTable.length > 0"
            type="info"
            class="record-count"
          >
            共 {{ productSubscribeInfoTable.length }} 条记录
          </el-tag>
        </div>
      </template>

      <!-- 备注信息 -->
      <el-alert
        title="操作说明"
        type="info"
        show-icon
        :closable="false"
        class="table-notice"
      >
        <template #default>
          QoS产品详情、通信服务详情、PCC策略详情可点击查看
        </template>
      </el-alert>

      <div class="table-container">
        <el-table
          :data="productSubscribeInfoTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无订购记录"
        >
          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="productName"
            label="产品名称"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="product-name-cell">
                <el-icon class="product-icon"><Box /></el-icon>
                <span class="product-name">{{ row.productName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="systemCode"
            label="业务系统编码"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code
                v-if="row.systemCode && row.systemCode.trim() !== ''"
                class="system-code"
              >
                {{ row.systemCode }}
              </code>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                effect="dark"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="effectiveTime"
            label="生效时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.effectiveTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="expireTime"
            label="失效时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.expireTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="关联通信服务列表"
            min-width="220"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="service-list">
                <el-tag
                  v-for="item in row.cmServiceIdList"
                  :key="item"
                  class="service-tag clickable-service-tag"
                  size="small"
                  type="info"
                  round
                  @click="showServiceInfo(item)"
                >
                  {{ item }}
                </el-tag>
                <span
                  v-if="
                    !row.cmServiceIdList || row.cmServiceIdList.length === 0
                  "
                  class="no-service"
                >
                  暂无关联服务
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 当前订单记录表格 -->
    <el-card v-loading="orderLoading" class="records-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Phone /></el-icon>
          <span class="header-title">当前订单记录</span>
          <el-tag
            v-if="allOrderTable.length > 0"
            type="info"
            class="record-count"
          >
            专载 {{ dedOrderTable.length }} 条，默载
            {{ defOrderTable.length }} 条，共 {{ allOrderTable.length }} 条记录
          </el-tag>
        </div>
      </template>

      <!-- 合并订单表格 -->
      <div class="table-container">
        <el-table
          :data="allOrderTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无当前订单记录"
        >
          <el-table-column
            prop="orderIdStr"
            label="订单号"
            min-width="160"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <div class="order-id-container">
                <code class="order-id">{{ row.orderIdStr }}</code>
                <el-button
                  type="primary"
                  size="small"
                  plain
                  class="flow-btn"
                  @click="goToFlowRecord(row.orderIdStr, row.startTime)"
                >
                  流水
                </el-button>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderType"
            label="订单类型"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.orderType === '专载' ? 'primary' : 'success'"
                size="small"
                effect="plain"
              >
                {{ row.orderType }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="cmServiceId"
            label="通信服务ID"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code
                class="service-id clickable-service-id"
                @click="showServiceInfo(row.cmServiceId)"
              >
                {{ row.cmServiceId }}
              </code>
            </template>
          </el-table-column>

          <el-table-column
            prop="speedType"
            label="速度类型"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.speedType !== undefined"
                type="warning"
                size="small"
              >
                {{ getSpeedTypeText(row.speedType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="bearerMode"
            label="承载模式"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.bearerMode !== undefined"
                type="info"
                size="small"
              >
                {{ getBearerModeText(row.bearerMode) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="startTime"
            label="开始时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.startTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="userEndTime"
            label="期望结束时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.userEndTime || '-' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="duration"
            label="计划加速时长(秒)"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <div>
                <el-tag type="info" size="small">{{ row.duration }}</el-tag>
                <div v-if="row.duration" class="duration-format">
                  {{ formatDuration(row.duration) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="getOrderStatusType(row.status)"
                effect="dark"
                size="small"
              >
                {{ getOrderStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 历史订单记录表格 -->
    <el-card
      v-loading="historyOrderLoading"
      class="records-card"
      shadow="hover"
    >
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">历史订单记录</span>
          <el-tag
            v-if="allHistoryOrderTable.length > 0"
            type="info"
            class="record-count"
          >
            专载 {{ dedHistoryOrderTable.length }} 条，默载
            {{ defHistoryOrderTable.length }} 条，共
            {{ allHistoryOrderTable.length }} 条记录
          </el-tag>
        </div>
      </template>

      <!-- 历史订单表格 -->
      <div class="table-container">
        <el-table
          :data="pagedHistoryOrderTable"
          stripe
          border
          class="custom-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#1f2937',
            fontWeight: '600',
          }"
          :row-style="{ transition: 'all 0.3s' }"
          empty-text="暂无历史订单记录"
        >
          <el-table-column
            prop="orderIdStr"
            label="订单号"
            min-width="180"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <div class="order-id-container">
                <code class="order-id">{{ row.orderIdStr }}</code>
                <el-button
                  type="primary"
                  size="small"
                  plain
                  class="flow-btn"
                  @click="goToFlowRecord(row.orderIdStr, row.startTime)"
                >
                  流水
                </el-button>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderType"
            label="订单类型"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="row.orderType === '专载' ? 'primary' : 'success'"
                size="small"
                effect="plain"
              >
                {{ row.orderType }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="qosProductId"
            label="QoS产品ID"
            min-width="120"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                type="primary"
                size="small"
                round
                class="clickable-tag"
                @click="showProductInfo(row.qosProductId)"
              >
                {{ row.qosProductId }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="cmServiceId"
            label="通信服务ID"
            min-width="140"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <code
                class="service-id clickable-service-id"
                @click="showServiceInfo(row.cmServiceId)"
              >
                {{ row.cmServiceId }}
              </code>
            </template>
          </el-table-column>

          <el-table-column
            prop="speedType"
            label="速度类型"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.speedType !== undefined"
                type="warning"
                size="small"
              >
                {{ getSpeedTypeText(row.speedType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="bearerMode"
            label="承载模式"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.bearerMode !== undefined"
                type="info"
                size="small"
              >
                {{ getBearerModeText(row.bearerMode) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="startTime"
            label="开始时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.startTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="userEndTime"
            label="期望结束时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.userEndTime || '-' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="endTime"
            label="实际结束时间"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon class="time-icon"><Timer /></el-icon>
                {{ row.endTime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="delSource"
            min-width="120"
            show-overflow-tooltip
          >
            <template #header>
              <div class="column-with-tooltip">
                结束原因
                <el-tooltip
                  effect="dark"
                  placement="top"
                  popper-class="reason-tooltip"
                >
                  <template #content>
                    <div class="reason-tooltip-content">
                      <p><strong>TimedTermination</strong>：到时删除</p>
                      <p><strong>OverdueHandler</strong>：超时未归档</p>
                      <p>
                        <strong>OrderReplacement</strong>：重复申请时终止原订单
                      </p>
                      <p>
                        <strong>NetBreakEvent</strong
                        >：网络中断事件（不重建场景，或者重建失败场景）
                      </p>
                      <p><strong>NetDeleteEvent</strong>：网络删除事件</p>
                      <p>
                        <strong>AccessTypechangeEvent</strong
                        >：接入类型改变事件导致的订单终止（5G接入拒绝场景）
                      </p>
                      <p><strong>ConflictHandler</strong>：策略覆盖</p>
                      <p><strong>SameCsDel</strong>：同通信服务删除</p>
                      <p><strong>其他</strong>：上游调用系统主动删除</p>
                    </div>
                  </template>
                  <el-icon class="question-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="{ row }">
              <el-tag
                v-if="row.delSource"
                type="info"
                size="small"
                effect="plain"
              >
                {{ row.delSource }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="duration"
            label="实际加速时长(秒)"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <div>
                <el-tag type="info" size="small">{{ row.duration }}</el-tag>
                <div v-if="row.duration" class="duration-format">
                  {{ formatDuration(row.duration) }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            min-width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag
                :type="getOrderStatusType(row.status)"
                effect="dark"
                size="small"
              >
                {{ getOrderStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="allHistoryOrderTable.length"
            :current-page="historyCurrentPage"
            :page-size="historyPageSize"
            :page-sizes="[10, 20, 50, 100]"
            class="custom-pagination"
            @current-change="handleHistoryCurrentChange"
            @size-change="handleHistorySizeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- QoS产品信息弹窗 -->
    <QosProductInfoDialog
      v-model:visible="productDialogVisible"
      :qos-product-id="selectedProductId"
    />

    <!-- 通信服务信息弹窗 -->
    <CmServiceInfoDialog
      v-model:visible="serviceDialogVisible"
      :cm-service-id="selectedServiceId"
    />
  </div>
</template>

<style scoped>
.subscribe-container {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 200px);
}

/* 查询区域样式 */
.query-section {
  margin-bottom: 24px;
}

/* 卡片样式 */
.records-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.records-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  font-size: 18px;
  color: #3b82f6;
}

.header-title {
  font-size: 16px;
}

.record-count {
  margin-left: auto;
}

/* 备注信息样式 */
.table-notice {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #d1ecf1;
  background-color: #f8f9fa;
}

.table-notice :deep(.el-alert__content) {
  font-size: 14px;
  color: #495057;
}

.table-notice :deep(.el-alert__title) {
  font-weight: 600;
  color: #31708f;
  margin-bottom: 4px;
}

.table-notice :deep(.el-alert__icon) {
  color: #31708f;
}

/* 表格容器 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.custom-table :deep(.el-table__header) {
  background: #f8fafc;
}

.custom-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

.custom-table :deep(.el-table__cell) {
  padding: 16px 12px;
}

/* 表格内容样式 */
.product-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-icon {
  font-size: 14px;
  color: #6b7280;
}

.product-name {
  font-weight: 500;
  color: #1f2937;
}

.system-code {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-icon {
  font-size: 14px;
  color: #6b7280;
}

.service-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.service-tag {
  margin: 0;
  font-size: 12px;
}

.no-service {
  color: #9ca3af;
  font-style: italic;
  font-size: 14px;
}

/* QoS链接样式 */
.qos-link {
  text-decoration: none;
  display: inline-block;
  transition: transform 0.2s ease;
}

.qos-link:hover {
  transform: scale(1.05);
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 通信服务链接样式 */
.service-link {
  text-decoration: none;
  display: inline-block;
  transition: transform 0.2s ease;
}

.service-link:hover {
  transform: translateY(-1px);
}

.clickable-service-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 4px;
  margin-bottom: 4px;
}

.clickable-service-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__header) {
  background: #ffffff;
  border-bottom: 1px solid #f3f4f6;
  padding: 20px 24px;
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subscribe-container {
    padding: 16px;
  }

  .custom-table :deep(.el-table__cell) {
    padding: 12px 8px;
  }

  .product-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .record-count {
    margin-left: 0;
  }

  .service-list {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 新增样式 - 订单相关 */
.order-section {
  margin-bottom: 24px;
}

.order-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.section-title {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
}

.order-id {
  background: #eff6ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1e40af;
  border: 1px solid #bfdbfe;
  font-weight: 600;
}

.service-id {
  background: #f0fdf4;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #15803d;
  border: 1px solid #bbf7d0;
}

.no-data {
  padding: 40px 20px;
  text-align: center;
}

/* 卡片间距调整 */
.records-card + .records-card {
  margin-top: 24px;
}

/* 优化表格样式 */
.custom-table :deep(.el-table__empty-text) {
  color: #9ca3af;
  font-style: italic;
}

/* 标签悬停效果 */
.el-tag {
  transition: all 0.2s ease;
}

.el-tag:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 分页样式 */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.custom-pagination {
  background: transparent;
}

.custom-pagination :deep(.el-pagination__total) {
  color: #6b7280;
  font-weight: 500;
}

.custom-pagination :deep(.el-pager li) {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

/* 时间格式化样式 */
.duration-format {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 带有提示的列标题样式 */
.column-with-tooltip {
  display: flex;
  align-items: center;
  gap: 6px;
}

.question-icon {
  font-size: 14px;
  color: #6b7280;
  cursor: help;
  transition: all 0.2s ease;
}

.question-icon:hover {
  color: #3b82f6;
  transform: scale(1.1);
}

/* 提示框样式 */
:deep(.reason-tooltip) {
  max-width: 500px;
  min-width: 400px;
}

:deep(.reason-tooltip-content) {
  text-align: left;
  line-height: 1.5;
  padding: 4px 0;
}

:deep(.reason-tooltip-content p) {
  margin: 6px 0;
}

:deep(.reason-tooltip-content strong) {
  color: #dbeafe;
  font-weight: 600;
}

/* 订单号容器样式 */
.order-id-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.flow-btn {
  margin-left: 8px;
  padding: 2px 8px;
  font-size: 12px;
  height: 24px;
  line-height: 1;
}

/* QoS产品ID可点击样式 */

/* 通信服务ID可点击样式 */

.clickable-service-id {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 2px 6px;
  border-radius: 4px;
}

.clickable-service-id:hover {
  background-color: #f0f9ff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}
</style>
