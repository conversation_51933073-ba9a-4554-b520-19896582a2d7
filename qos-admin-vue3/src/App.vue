<template>
  <div class="app-wrapper">
    <!-- 调试信息 -->
    <div
      v-if="false"
      style="
        position: fixed;
        top: 0;
        right: 0;
        background: #000;
        color: #fff;
        padding: 10px;
        z-index: 9999;
      "
    >
      登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}
    </div>

    <!-- 登录页面 -->
    <template v-if="!isLoggedIn">
      <RouterView />
    </template>

    <!-- 已登录布局 -->
    <template v-else>
      <el-container class="app-container">
        <el-header class="header">
          <div class="header-left">
            <img src="./assets/logo.svg" alt="logo" class="logo" />
            <div class="title-container">
              <h1 class="title">动态编排底座管理后台</h1>
              <el-tag
                :type="environmentTagType"
                :effect="environmentTagEffect"
                size="small"
                class="env-tag"
              >
                {{ environmentLabel }}
              </el-tag>
            </div>
          </div>
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar
                  :size="32"
                  :src="defaultAvatar"
                />
                <span class="username">{{
                  userInfo?.realName || '管理员'
                }}</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile"
                    >个人信息</el-dropdown-item
                  >
                  <el-dropdown-item command="logout" divided
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-container>
          <!--导航区-->
          <el-aside class="sidebar-container">
            <el-menu
              :default-openeds="['0']"
              class="sidebar-menu"
              background-color="#304156"
              text-color="#bfcbd9"
              active-text-color="#409EFF"
            >
              <template
                v-for="(group, groupIdx) in filteredMenu"
                :key="groupIdx"
              >
                <el-sub-menu :index="String(groupIdx)">
                  <template #title>
                    <el-icon
                      ><component
                        :is="iconMap[group.icon as keyof typeof iconMap]"
                    /></el-icon>
                    <span>{{ group.label }}</span>
                  </template>
                  <el-menu-item
                    v-for="(item, itemIdx) in group.children"
                    :key="itemIdx"
                    :index="`${groupIdx}-${itemIdx}`"
                  >
                    <RouterLink :to="item.path" class="menu-link">{{
                      item.label
                    }}</RouterLink>
                  </el-menu-item>
                </el-sub-menu>
              </template>
            </el-menu>
          </el-aside>

          <!--展示区-->
          <el-main class="main-content">
            <RouterView />
          </el-main>
        </el-container>
      </el-container>
    </template>
  </div>

  <!-- 个人信息弹窗 -->
  <el-dialog
    v-model="profileDialogVisible"
    title="个人信息"
    width="500px"
    :before-close="handleCloseProfile"
  >
    <div class="profile-content">
      <div class="profile-avatar">
        <el-avatar
          :size="80"
          :src="defaultAvatar"
        />
      </div>

      <div class="profile-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户名">
            <span class="info-value">{{ userInfo?.userName || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="真实姓名">
            <span class="info-value">{{ userInfo?.realName || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            <span class="info-value">{{ userInfo?.phone || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            <span class="info-value">{{ userInfo?.email || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="profileDialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="App">
import { HomeFilled, Setting, User } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { RouterLink, RouterView } from 'vue-router'
import useAuth from './composables/useAuth'
import useMenu from './composables/useMenu'
import { env } from './config'
import defaultAvatar from './assets/avatars/default-user-avatar.svg'

const { userInfo, isLoggedIn, logout } = useAuth()

const iconMap = { HomeFilled, Setting, User }
const { filteredMenu } = useMenu()

// 个人信息弹窗状态
const profileDialogVisible = ref(false)

// 环境标签配置
const environmentConfig = {
  development: {
    label: '开发环境',
    type: 'info' as const,
    effect: 'dark' as const,
  },
  test: {
    label: '测试环境',
    type: 'warning' as const,
    effect: 'dark' as const,
  },
  production: {
    label: '生产环境',
    type: 'danger' as const,
    effect: 'dark' as const,
  },
  backup: {
    label: '备用环境',
    type: 'success' as const,
    effect: 'dark' as const,
  },
} as const

// 计算属性
const currentEnvConfig = computed(() => {
  return (
    environmentConfig[env as keyof typeof environmentConfig] ||
    environmentConfig.development
  )
})

const environmentLabel = computed(() => currentEnvConfig.value.label)
const environmentTagType = computed(() => currentEnvConfig.value.type)
const environmentTagEffect = computed(() => currentEnvConfig.value.effect)

// 确保初始化时检查登录状态
onMounted(() => {
  if (import.meta.env.DEV) {
    console.log('App组件已挂载，环境:', env, '认证状态:', isLoggedIn.value ? '已登录' : '未登录')
  }
})

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 显示个人信息弹窗
      profileDialogVisible.value = true
      break
    case 'logout':
      // 确认对话框
      ElMessageBox.confirm('确定要退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 执行登出
          logout()
        })
        .catch(() => {
          // 取消登出，不做操作
        })
      break
  }
}

// 关闭个人信息弹窗
const handleCloseProfile = () => {
  profileDialogVisible.value = false
}
</script>

<style scoped>
.app-wrapper {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  height: 56px;
  background: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
  margin-right: 16px;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: 0.5px;
}

.env-tag {
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  margin-left: 4px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 8px;
  font-size: 14px;
  color: #606266;
}

.el-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  height: calc(100vh - 56px);
}

.sidebar-container {
  width: 200px;
  background: #304156;
  transition: width 0.28s;
  overflow-y: auto;
  flex-shrink: 0;
  height: 100%;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu :deep(.el-sub-menu__title) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px 0 48px;
  position: relative;
}

.menu-link {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
  height: 100%;
}

.el-menu-item:hover {
  background-color: #263445 !important;
}

.main-content {
  flex: 1;
  padding: 24px;
  background: #f0f2f5;
  overflow-y: auto;
  overflow-x: hidden;
  min-width: 0;
  height: 100%;
}

:deep(.el-sub-menu__title):hover {
  background-color: #263445 !important;
}

.el-menu-item.is-active {
  background-color: #1890ff !important;
  color: #fff !important;
}

.el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #fff;
}

:deep(.el-sub-menu__title) {
  display: flex;
  align-items: center;
}

:deep(.el-sub-menu__title) .el-icon {
  margin-right: 12px;
  font-size: 18px;
}

:deep(.el-menu-item) .el-icon {
  margin-right: 12px;
  font-size: 18px;
}

/* 个人信息弹窗样式 */
.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.profile-avatar {
  display: flex;
  justify-content: center;
}

.profile-info {
  width: 100%;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

/* 描述列表样式优化 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
  width: 100px;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>
