interface EnvConfig {
  apiBaseUrl: string
}

export const env = import.meta.env.MODE || 'development'

const config: Record<string, EnvConfig> = {
  development: {
    apiBaseUrl: 'http://localhost:31200/qos',
  },
  test: {
    apiBaseUrl: 'http://**************:58008/qos',
  },
  production: {
    apiBaseUrl: 'http://************:58008/qos',
  },
  backup: {
    apiBaseUrl: 'http://************:58008/qos',
  },
}

export const currentConfig = config[env] || config.development
