<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { onMounted, reactive, watch } from 'vue'

// 定义props，用于接收初始值
const props = defineProps({
  initialPhoneNumber: {
    type: [String, Number],
    default: '',
  },
  initialDateRange: {
    type: Array,
    default: () => [],
  },
})

// 获取最近一个月的日期范围
function getLastMonthDateRange(): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30) // 30天前

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return [formatDate(start), formatDate(end)]
}

let queryForm = reactive({
  phoneNumber: '',
  dateRange: [] as [string, string] | [],
})

// 在组件挂载时设置默认值
onMounted(() => {
  // 如果有初始值，则使用初始值，否则使用默认值
  if (props.initialPhoneNumber) {
    queryForm.phoneNumber = String(props.initialPhoneNumber)
  }

  if (props.initialDateRange && props.initialDateRange.length === 2) {
    queryForm.dateRange = [...props.initialDateRange] as [string, string]
  } else {
    queryForm.dateRange = getLastMonthDateRange()
  }
})

// 监听props变化
watch(
  () => props.initialPhoneNumber,
  (newVal) => {
    if (newVal) {
      queryForm.phoneNumber = String(newVal)
    }
  },
  { immediate: true }
)

watch(
  () => props.initialDateRange,
  (newVal) => {
    if (newVal && newVal.length === 2) {
      queryForm.dateRange = [...newVal] as [string, string]
    }
  },
  { immediate: true }
)

const emit = defineEmits(['search'])

function handleSearch() {
  emit('search', {
    phoneNumber: queryForm.phoneNumber ? Number(queryForm.phoneNumber) : '',
    dateRange: queryForm.dateRange,
  })
}
</script>

<template>
  <!-- 查询表单 -->
  <el-form :model="queryForm" :inline="true" class="query-form">
    <el-form-item
      class="search-item"
      prop="phoneNumber"
      :rules="[
        { required: true, message: '手机号必填' },
        {
          validator: (_rule: any, value: any, callback: any) => {
            if (value && !/^\d+$/.test(String(value))) {
              callback(new Error('手机号必须是数字'))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        },
      ]"
    >
      <el-input
        v-model="queryForm.phoneNumber"
        placeholder="请输入手机号"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="date-range-item" label="开始时间">
      <el-date-picker
        v-model="queryForm.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        style="width: 260px"
        clearable
        :shortcuts="[
          {
            text: '最近一周',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
          {
            text: '最近一个月',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            },
          },
          {
            text: '最近三个月',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              return [start, end]
            },
          },
        ]"
      />
    </el-form-item>
    <el-form-item class="button-group">
      <el-button type="primary" class="search-button" @click="handleSearch">
        <el-icon><Search /></el-icon>
        查询
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style scoped>
.query-form {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  background: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.search-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-input {
  width: 300px;
}

.date-range-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.button-group {
  margin: 0;
}

.button-group :deep(.el-button) {
  padding: 8px 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  transition: all 0.3s;
}

.search-button {
  background: var(--el-color-primary);
}

.search-button:hover {
  background: var(--el-color-primary-light-3);
}

:deep(.el-form-item__error) {
  padding-top: 4px;
  font-size: 12px;
}
</style>
