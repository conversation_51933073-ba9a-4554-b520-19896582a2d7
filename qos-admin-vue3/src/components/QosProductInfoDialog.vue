<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { ProductBaseInfoDTO, queryProductBaseInfoByIds } from '../api/product'

// 组件属性
interface Props {
  visible: boolean
  qosProductId: number | null
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const productInfo = ref<ProductBaseInfoDTO | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
})

// 状态映射
const statusMap = {
  0: { text: '启用', type: 'success' },
  1: { text: '停用', type: 'danger' },
} as const

// 冲突检查标识映射
const conflictCheckMap = {
  0: { text: '参与产品订购互斥校验', type: 'warning' },
  '-1': { text: '不参与产品订购互斥校验', type: 'info' },
} as const

// 平台产品存在标识映射
const existPlatProductMap = {
  0: { text: '不存在', type: 'info' },
  1: { text: '存在', type: 'success' },
} as const

// 获取状态显示信息
const getStatusInfo = (status: number) => {
  return (
    statusMap[status as keyof typeof statusMap] || {
      text: '未知',
      type: 'info',
    }
  )
}

// 获取冲突检查显示信息
const getConflictCheckInfo = (flag: number) => {
  return (
    conflictCheckMap[flag as keyof typeof conflictCheckMap] || {
      text: '未知',
      type: 'info',
    }
  )
}

// 获取平台产品存在显示信息
const getExistPlatProductInfo = (flag: number) => {
  return (
    existPlatProductMap[flag as keyof typeof existPlatProductMap] || {
      text: '未知',
      type: 'info',
    }
  )
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 获取产品信息
const fetchProductInfo = async (qosProductId: number) => {
  if (!qosProductId) return

  loading.value = true
  try {
    const response = await queryProductBaseInfoByIds({
      qosProductIdList: [qosProductId],
    })

    if (response.code === 0 && response.data && response.data.length > 0) {
      productInfo.value = response.data[0]
    } else {
      ElMessage.error(response.msg || '获取产品信息失败')
      productInfo.value = null
    }
  } catch (error: any) {
    if (!error.isHandled) {
      ElMessage.error('获取产品信息失败：' + (error.message || '未知错误'))
    }
    console.error('获取产品信息失败:', error)
    productInfo.value = null
  } finally {
    loading.value = false
  }
}

// 监听产品ID变化
watch(
  () => props.qosProductId,
  (newId) => {
    if (newId && props.visible) {
      fetchProductInfo(newId)
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.qosProductId) {
      fetchProductInfo(props.qosProductId)
    } else if (!visible) {
      productInfo.value = null
    }
  }
)

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="QoS产品信息"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    @close="handleClose"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><InfoFilled /></el-icon>
        <span class="header-title">QoS产品信息</span>
        <el-tag
          v-if="productInfo"
          type="primary"
          size="small"
          class="product-id-tag"
        >
          ID: {{ productInfo.qosProductId }}
        </el-tag>
      </div>
    </template>

    <div v-loading="loading" class="dialog-content">
      <div v-if="!loading && !productInfo" class="empty-state">
        <el-empty description="暂无产品信息" />
      </div>

      <div v-else-if="productInfo" class="product-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品ID" :span="1">
            <el-tag type="primary" size="small">{{
              productInfo.qosProductId
            }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="产品名称" :span="1">
            <span class="product-name">{{
              productInfo.qosProductName || '-'
            }}</span>
          </el-descriptions-item>

          <el-descriptions-item label="业务归属" :span="1">
            <el-tag type="info" size="small">{{
              productInfo.businessOwnership || '-'
            }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="状态" :span="1">
            <el-tag :type="getStatusInfo(productInfo.status).type" size="small">
              {{ getStatusInfo(productInfo.status).text }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="平台产品" :span="1">
            <el-tag
              :type="getExistPlatProductInfo(productInfo.existPlatProduct).type"
              size="small"
            >
              {{ getExistPlatProductInfo(productInfo.existPlatProduct).text }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="冲突检查" :span="1">
            <el-tag
              :type="getConflictCheckInfo(productInfo.conflictCheckFlag).type"
              size="small"
            >
              {{ getConflictCheckInfo(productInfo.conflictCheckFlag).text }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="订购优先级" :span="2">
            <span class="priority-value">{{
              productInfo.orderPriority || '-'
            }}</span>
          </el-descriptions-item>

          <el-descriptions-item label="创建时间" :span="1">
            <span class="time-value">{{
              formatDate(productInfo.createTime)
            }}</span>
          </el-descriptions-item>

          <el-descriptions-item label="更新时间" :span="1">
            <span class="time-value">{{
              formatDate(productInfo.updateTime)
            }}</span>
          </el-descriptions-item>

          <el-descriptions-item label="产品描述" :span="2">
            <div class="product-desc">
              {{ productInfo.qosProductDesc || '暂无描述' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-id-tag {
  margin-left: auto;
}

.dialog-content {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.product-info {
  padding: 16px 0;
}

.product-name {
  font-weight: 500;
  color: #303133;
}

.priority-value {
  font-weight: 500;
  color: #409eff;
}

.time-value {
  color: #606266;
  font-size: 14px;
}

.product-desc {
  max-height: 100px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

:deep(.el-loading-mask) {
  border-radius: 4px;
}
</style>
