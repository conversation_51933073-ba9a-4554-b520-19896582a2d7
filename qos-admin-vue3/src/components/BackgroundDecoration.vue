<template>
  <div class="background-decoration">
    <!-- 主渐变背景 -->
    <div class="gradient-bg"></div>

    <div v-if="enableEffects" class="geometric-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>

    <div v-if="enableParticles" class="floating-particles">
      <div v-for="i in particleCount" :key="i" class="particle" :style="getParticleStyle()"></div>
    </div>
    <div v-if="enableEffects" class="glow-effects">
      <div class="glow glow-1"></div>
      <div class="glow glow-2"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const enableEffects = ref(true)
const enableParticles = ref(true)
const particleCount = ref(8)

const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
if (prefersReducedMotion) {
  enableEffects.value = false
  enableParticles.value = false
}

const getParticleStyle = () => {
  const size = Math.random() * 3 + 1.5
  const left = Math.random() * 100
  const animationDelay = Math.random() * 15
  const animationDuration = Math.random() * 8 + 12

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`,
    transform: 'translate3d(0, 0, 0)'
  }
}
</script>

<style scoped>
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

/* 主渐变背景 */
.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    #0f0c29 0%,
    #24243e 35%,
    #302b63 70%,
    #24243e 100%
  );
  will-change: background;
  transform: translate3d(0, 0, 0);
  animation: gradientShift 25s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background: linear-gradient(135deg, #0f0c29 0%, #24243e 35%, #302b63 70%, #24243e 100%);
  }
  50% {
    background: linear-gradient(135deg, #24243e 0%, #302b63 35%, #0f0c29 70%, #302b63 100%);
  }
}

/* 几何装饰形状 */
.geometric-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));
  will-change: transform;
  transform: translate3d(0, 0, 0);
  animation: float 18s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 5s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  top: 20%;
  left: 10%;
  animation-delay: 10s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 15%;
  animation-delay: 7s;
}

.shape-5 {
  width: 250px;
  height: 250px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 12s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(20px) rotate(240deg);
  }
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  will-change: transform, opacity;
  animation: particleFloat linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 光晕效果 */
.glow-effects {
  position: absolute;
  width: 100%;
  height: 100%;
}

.glow {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: glowPulse 8s ease-in-out infinite;
}

.glow-1 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.glow-2 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.2) 0%, transparent 70%);
  bottom: 20%;
  right: 30%;
  animation-delay: 4s;
}

.glow-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 6s;
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@media (prefers-reduced-motion: reduce) {
  .gradient-bg,
  .shape,
  .particle,
  .glow {
    animation: none !important;
  }

  .geometric-shapes,
  .floating-particles,
  .glow-effects {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .shape {
    display: none;
  }

  .glow {
    width: 150px !important;
    height: 150px !important;
  }

  .floating-particles {
    display: none;
  }
}
</style>
