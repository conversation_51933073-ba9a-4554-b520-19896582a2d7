<template>
  <el-dialog
    v-model="visible"
    title="异常数据检测"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="anomaly-detection-modal"
  >
    <div class="anomaly-content">
      <div v-if="allAnomalies.length > 0" class="anomaly-tables">
        <AnomalyTable
          :anomalies="allAnomalies"
          :loading="loading"
        />
      </div>
      <div v-else-if="!detecting && !loading" class="no-anomalies">
        <el-empty description="暂无异常数据" />
      </div>
    </div>
    <template #footer>
      <div class="footer-actions">
        <el-button
          v-if="allAnomalies.length > 0"
          type="success"
          @click="exportToImage"
        >
          📸 保存为图片
        </el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import html2canvas from 'html2canvas'
import AnomalyTable from './AnomalyTable.vue'
import {
  detectHttpFailAnomalies,
  detectSubscriptionAnomalies,
  detectDefbearerAnomalies,
  detectDedbearerAnomalies,
} from '../api/anomalyDetection'
import type { AnomalyResult } from '../types/anomalyDetection'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const detecting = ref(false)
const loading = ref(false)
const allAnomalies = ref<AnomalyResult[]>([])

// 检测所有异常
const detectAllAnomalies = async () => {
  detecting.value = true
  loading.value = true
  
  try {
    const results = await Promise.allSettled([
      detectHttpFailAnomalies(),
      detectSubscriptionAnomalies(),
      detectDefbearerAnomalies(),
      detectDedbearerAnomalies(),
    ])
    
    const anomalies: AnomalyResult[] = []
    const failedApis: string[] = []
    
    // 处理每个API的结果
    const apiNames = ['httpfail', 'subscription', 'defbearer', 'dedbearer']
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        // API调用成功
        const apiAnomalies = result.value.map((item: any) => ({
          ...item,
          businessType: apiNames[index]
        }))
        anomalies.push(...apiAnomalies)
      } else {
        // API调用失败
        const apiName = apiNames[index]
        failedApis.push(apiName)
        console.error(`${apiName} 异常检测失败:`, result.reason)
      }
    })
    
    allAnomalies.value = anomalies
    
    // 根据结果情况给出不同的用户反馈
    if (anomalies.length > 0) {
      if (failedApis.length > 0) {
        ElMessage.warning(
          `异常检测完成，发现 ${anomalies.length} 个异常，但 ${failedApis.join('、')} 检测失败，请检查网络连接或联系管理员`
        )
      } else {
        ElMessage.success(`异常检测完成，发现 ${anomalies.length} 个异常`)
      }
    } else {
      if (failedApis.length > 0) {
        ElMessage.error(`异常检测失败：${failedApis.join('、')} 检测失败，请检查网络连接或联系管理员`)
      } else {
        ElMessage.success('异常检测完成，未发现异常数据')
      }
    }
  } catch (error: any) {
    console.error('异常检测过程中发生未知错误:', error)
    ElMessage.error('异常检测失败: ' + (error.message || '未知错误'))
  } finally {
    detecting.value = false
    loading.value = false
  }
}

// 导出为图片
const exportToImage = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成异常检测报告图片...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  
  try {
    const modalContent = document.querySelector('.anomaly-detection-modal .el-dialog__body') as HTMLElement
    if (!modalContent) {
      throw new Error('未找到要导出的内容')
    }
    
    const canvas = await html2canvas(modalContent, {
      scale: 2,
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
    })
    
    const link = document.createElement('a')
    link.download = `异常检测报告_${new Date().toISOString().split('T')[0]}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()
    
    ElMessage.success('异常检测报告图片导出成功！')
  } catch (error) {
    console.error('图片导出失败:', error)
    ElMessage.error('图片导出失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

// 清空结果 - 暂时注释掉，未使用
// const clearResults = () => {
//   allAnomalies.value = []
// }

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗打开，自动检测
watch(visible, (newVal: boolean) => {
  if (newVal) {
    detectAllAnomalies()
  }
})
</script>

<style scoped>
.anomaly-detection-modal {
  :deep(.el-dialog__body) {
    padding: 32px 32px 16px 32px;
    background: #f8fafc;
    border-radius: 12px;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08);
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 0;
    padding-bottom: 12px;
  }
}
.anomaly-content {
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
  padding: 24px 16px 8px 16px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
}
.anomaly-tables {
  margin-top: 0;
}
.no-anomalies {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-right: 8px;
}
</style> 