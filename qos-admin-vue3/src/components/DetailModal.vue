<template>
  <el-dialog
    v-model="visible"
    :title="modalTitle"
    width="90%"
    :before-close="handleClose"
    class="detail-modal"
  >
    <div v-loading="loading" class="modal-content">
      <!-- 统计信息头部 -->
      <div class="stats-header">
        <!-- 订购统计的详细信息 -->
        <div
          v-if="statsType === 'subscription' && queryParams"
          class="subscription-details"
        >
          <!-- 统计信息 -->
          <div class="summary-info">
            <el-tag type="warning" size="large">
              总数量: {{ getTotalCount() }}
            </el-tag>
            <el-tag type="success" size="large">
              实际显示: {{ detailData.length }} 条记录
            </el-tag>
            <el-tag type="info" size="large">
              操作类型: {{ getOperTypeName(queryParams.operType) }}
            </el-tag>
            <el-tag type="danger" size="large">
              响应码: {{ queryParams.respCode }}
            </el-tag>
            <el-tag
              v-if="queryParams.respDesc"
              type="primary"
              size="large"
              :title="queryParams.respDesc"
            >
              响应描述: {{ queryParams.respDesc }}
            </el-tag>
          </div>
        </div>

        <!-- 专载和默载调用统计的详细信息 -->
        <div
          v-else-if="
            (statsType === 'defbearer' || statsType === 'dedbearer') &&
            queryParams
          "
          class="bearer-details"
        >
          <!-- 统计信息 -->
          <div class="summary-info">
            <el-tag type="warning" size="large">
              总数量: {{ getTotalCount() }}
            </el-tag>
            <el-tag type="success" size="large">
              实际显示: {{ detailData.length }} 条记录
            </el-tag>
            <el-tag v-if="queryParams.orderSourceName" type="info" size="large">
              订单来源: {{ queryParams.orderSourceName }}
            </el-tag>
            <el-tag type="primary" size="large">
              操作类型:
              {{
                queryParams.operTypeName ||
                getOperTypeName(queryParams.operType)
              }}
            </el-tag>
            <el-tag type="danger" size="large">
              响应码: {{ queryParams.respCode }}
            </el-tag>
            <el-tag
              v-if="queryParams.respDesc"
              type="warning"
              size="large"
              :title="queryParams.respDesc"
            >
              响应描述: {{ queryParams.respDesc }}
            </el-tag>
          </div>
        </div>

        <!-- 其他类型的统计信息 -->
        <div v-else class="stats-info-tags">
          <el-tag type="info" size="large">
            {{ statsInfo }}
          </el-tag>
          <el-tag v-if="detailData.length > 0" type="success" size="large">
            共 {{ detailData.length }} 条记录
          </el-tag>
        </div>
      </div>

      <!-- 详情表格 -->
      <el-table
        v-if="!hasError"
        :data="detailData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-style="rowStyle"
        empty-text="暂无详细记录"
        :class="[
          'modern-table',
          {
            'bearer-table':
              statsType === 'defbearer' || statsType === 'dedbearer',
          },
        ]"
        :row-key="(_: any, index: number) => index"
        size="large"
      >
        <!-- 关键字段列 -->
        <el-table-column
          v-for="field in keyFields"
          :key="field.prop"
          :prop="field.prop"
          :label="field.label"
          :width="field.width"
          :min-width="field.minWidth || field.width || 100"
          :max-width="field.maxWidth"
          :fixed="field.fixed"
          :show-overflow-tooltip="field.showOverflowTooltip !== false"
          align="center"
        >
          <template #default="{ row }">
            <!-- 用户ID字段特殊处理：显示为可点击链接 -->
            <span
              v-if="field.prop === 'userId'"
              :class="
                formatFieldValue(field.prop, row[field.prop]) === '-' ||
                formatFieldValue(field.prop, row[field.prop]) === '' ||
                !formatFieldValue(field.prop, row[field.prop])
                  ? 'empty-value-field'
                  : 'user-id-link'
              "
              @click="goToUserInfoPage(row[field.prop])"
            >
              {{ formatFieldValue(field.prop, row[field.prop]) || '-' }}
            </span>
            <!-- 其他字段正常显示 -->
            <span
              v-else
              :class="
                formatFieldValue(field.prop, row[field.prop]) === '-' ||
                formatFieldValue(field.prop, row[field.prop]) === '' ||
                !formatFieldValue(field.prop, row[field.prop])
                  ? 'empty-value-field'
                  : getFieldClass(field.prop, row[field.prop])
              "
            >
              {{ formatFieldValue(field.prop, row[field.prop]) || '-' }}
            </span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <!-- 第一行：查看订单和查看流水 -->
              <div class="action-row">
                <el-button
                  v-for="action in getActions().filter(a => a.key !== 'viewUserInfo')"
                  :key="action.key"
                  :type="action.type"
                  size="small"
                  @click="handleAction(action.key, row)"
                >
                  {{ action.label }}
                </el-button>
              </div>

              <!-- 第二行：用户信息和详情 -->
              <div class="action-row">
                <!-- 用户信息按钮（如果存在） -->
                <el-button
                  v-if="getActions().some(a => a.key === 'viewUserInfo')"
                  type="info"
                  size="small"
                  @click="handleAction('viewUserInfo', row)"
                >
                  用户信息
                </el-button>

                <!-- 查看详情按钮 -->
                <el-button
                  type="info"
                  size="small"
                  icon="View"
                  @click="showRowDetails(row)"
                >
                  详情
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 错误状态 -->
      <div v-if="!loading && hasError" class="error-state">
        <el-alert
          :title="errorMessage"
          type="error"
          show-icon
          :closable="false"
          class="error-alert"
        />
      </div>

      <!-- 空状态 -->
      <div
        v-if="!loading && !hasError && detailData.length === 0"
        class="empty-state"
      >
        <el-empty description="暂无详细记录" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 详情抽屉 -->
  <el-drawer
    v-model="detailDrawerVisible"
    title="记录详情"
    :size="600"
    direction="rtl"
  >
    <div v-if="selectedRowData" class="row-details">
      <div class="detail-section">
        <h3>基本信息</h3>
        <div class="detail-grid">
          <div
            v-for="(value, key) in selectedRowData"
            :key="key"
            class="detail-item"
            :class="{ highlight: isKeyField(key) }"
          >
            <div class="detail-label">{{ getFieldLabel(key) }}</div>
            <div class="detail-value">{{ formatFieldValue(key, value) }}</div>
          </div>
        </div>
      </div>

      <!-- JSON格式展示 -->
      <div class="detail-section">
        <h3>JSON格式</h3>
        <el-input
          v-model="selectedRowJson"
          type="textarea"
          :rows="15"
          readonly
          class="json-display"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '../utils/request'

// 字段配置接口
interface FieldConfig {
  prop: string
  label: string
  width?: number
  minWidth?: number
  maxWidth?: number
  fixed?: string
  showOverflowTooltip?: boolean
}

// Props定义
interface Props {
  modelValue: boolean
  title: string
  statsType: 'defbearer' | 'dedbearer' | 'subscription' | 'defbearerBatch'
  statsInfo: string
  queryParams: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '详情',
  statsType: 'defbearer',
  statsInfo: '',
  queryParams: () => ({}),
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const detailData = ref<any[]>([])
const errorMessage = ref('')
const hasError = ref(false)
const detailDrawerVisible = ref(false)
const selectedRowData = ref<any>(null)
const selectedRowJson = ref('')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const modalTitle = computed(() => {
  return `${props.title} - 详细记录`
})

// 字段配置
const fieldConfigs: Record<
  string,
  { keyFields: FieldConfig[]; actions: any[] }
> = {
  defbearer: {
    keyFields: [
      { prop: 'userId', label: '用户ID', minWidth: 120, fixed: 'left' },
      {
        prop: 'cmServiceName',
        label: '通信服务',
        minWidth: 120,
        maxWidth: 200,
      },
      { prop: 'speedTypeName', label: '速率类型', minWidth: 70, maxWidth: 90 },
      { prop: 'homeProvinceName', label: '归属省', minWidth: 60, maxWidth: 80 },
      {
        prop: 'signNetworkTypeName',
        label: '签约网络类型',
        minWidth: 80,
        maxWidth: 100,
      },
      { prop: 'duration', label: '时长(秒)', minWidth: 80 },
      { prop: 'afid', label: '网络策略ID', minWidth: 180, maxWidth: 250 },
      { prop: 'orderTime', label: '请求时间', minWidth: 140 },
      { prop: 'returnTime', label: '返回时间', minWidth: 140 },
    ],
    actions: [
      { key: 'viewOrder', label: '查看订单', type: 'primary' },
      { key: 'viewFlow', label: '查看流水', type: 'success' },
      { key: 'viewUserInfo', label: '用户信息', type: 'info' },
    ],
  },
  dedbearer: {
    keyFields: [
      { prop: 'userId', label: '用户ID', minWidth: 100, maxWidth: 120 },
      {
        prop: 'cmServiceName',
        label: '通信服务',
        minWidth: 160,
        maxWidth: 240,
      },
      { prop: 'publicIp', label: '公网IP', minWidth: 110 },
      { prop: 'privateIp', label: '私网IP', minWidth: 110 },
      { prop: 'targetIp', label: '目标IP', minWidth: 110 },
      { prop: 'homeProvinceName', label: '归属省', minWidth: 60, maxWidth: 80 },
      {
        prop: 'visitProvinceName',
        label: '拜访省',
        minWidth: 60,
        maxWidth: 80,
      },
      { prop: 'duration', label: '时长(秒)', minWidth: 70, maxWidth: 90 },
      { prop: 'neId', label: '网元ID', minWidth: 80, maxWidth: 100 },
      { prop: 'afid', label: '网络策略ID', minWidth: 140, maxWidth: 180 },
      { prop: 'orderTime', label: '请求时间', minWidth: 140 },
      { prop: 'returnTime', label: '返回时间', minWidth: 140 },
    ],
    actions: [
      { key: 'viewOrder', label: '查看订单', type: 'primary' },
      { key: 'viewFlow', label: '查看流水', type: 'success' },
      { key: 'viewUserInfo', label: '用户信息', type: 'info' },
    ],
  },
  subscription: {
    keyFields: [
      {
        prop: 'userId',
        label: '用户ID',
        width: 180,
        showOverflowTooltip: true,
      },
      {
        prop: 'qosProductName',
        label: 'QoS产品名称',
        width: 300,
        showOverflowTooltip: true,
      },
      {
        prop: 'effectiveTime',
        label: '生效时间',
        width: 200,
        showOverflowTooltip: true,
      },
      {
        prop: 'expireTime',
        label: '失效时间',
        width: 200,
        showOverflowTooltip: true,
      },
    ],
    actions: [
      { key: 'viewOrder', label: '查看订购', type: 'primary' },
      { key: 'viewFlow', label: '查看流水', type: 'success' },
      { key: 'viewUserInfo', label: '用户信息', type: 'info' },
    ],
  },
  defbearerBatch: {
    keyFields: [
      { prop: 'taskId', label: '任务ID', width: 160 },
      { prop: 'homeProvinceName', label: '省份', width: 100 },
      { prop: 'createTime', label: '创建时间', width: 160 },
      { prop: 'respCode', label: '响应码', width: 100 },
      { prop: 'respDesc', label: '响应描述', width: 150 },
    ],
    actions: [{ key: 'viewTask', label: '查看任务', type: 'primary' }],
  },
}

// 动态计算subscription类型字段的最佳宽度
const getDynamicFieldWidth = (fieldProp: string) => {
  if (props.statsType !== 'subscription' || !detailData.value.length) {
    return { minWidth: 200, maxWidth: 400 }
  }

  const maxLength = Math.max(
    ...detailData.value.map((item) => {
      const value = (item[fieldProp] || '').toString()
      // 中文字符按2个字符计算
      return fieldProp === 'qosProductName'
        ? value.replace(/[\u4e00-\u9fa5]/g, 'aa').length
        : value.length
    })
  )

  const charWidth = fieldProp === 'qosProductName' ? 6 : 8
  const baseWidth = Math.max(
    200,
    maxLength * charWidth + (fieldProp === 'qosProductName' ? 50 : 40)
  )
  const maxLimit = fieldProp === 'qosProductName' ? 500 : 400

  return {
    minWidth: Math.min(baseWidth, 200),
    maxWidth: Math.min(baseWidth, maxLimit),
  }
}

const keyFields = computed(() => {
  const fields = fieldConfigs[props.statsType]?.keyFields || []

  // 如果是subscription类型，动态更新字段宽度
  if (props.statsType === 'subscription') {
    return fields.map((field) => {
      if (field.prop === 'qosProductName') {
        const { minWidth, maxWidth } = getDynamicFieldWidth(field.prop)
        return { ...field, minWidth, maxWidth }
      }
      return field
    })
  }

  return fields
})

const headerCellStyle = computed(() => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: '#ffffff',
  fontWeight: '600',
  fontSize: '14px',
  textAlign: 'center',
  padding: '16px 12px',
  borderBottom: '2px solid #e2e8f0',
}))

const rowStyle = computed(() => ({
  height: '80px', // 增加高度以适应两行按钮
}))

// 方法
const isKeyField = (key: string | number) => {
  return keyFields.value.some((field) => field.prop === String(key))
}

const getFieldLabel = (key: string | number) => {
  const field = keyFields.value.find((f) => f.prop === String(key))
  return field?.label || String(key)
}

const formatFieldValue = (key: string | number, value: any) => {
  if (value === null || value === undefined) return '-'

  // -1值统一转换为"-"（特别处理这些字段）
  if (value === -1 || value === '-1') return '-'

  // 时间字段格式化
  if (String(key).includes('Time') && value) {
    const date = new Date(value)
    const formattedTime = date.toLocaleString('zh-CN')

    // 特殊处理：如果是1970/1/1 08:00:00，显示为"-"
    if (
      formattedTime === '1970/1/1 08:00:00' ||
      formattedTime === '1970/1/1 上午8:00:00'
    ) {
      return '-'
    }

    return formattedTime
  }

  // 操作类型格式化
  if (String(key) === 'operType') {
    const operTypeMap: Record<number, string> = {
      1: '申请',
      2: '释放',
      3: '修改',
    }
    return operTypeMap[value] || value
  }

  // 时长格式化（直接显示数字，不加单位）
  if (String(key) === 'duration') {
    return value && value !== -1 && value !== '-1' ? String(value) : '-'
  }

  // 签约网络类型特殊处理：删除_PACKAGE后缀
  if (String(key) === 'signNetworkTypeName' && typeof value === 'string') {
    const processedValue = value.endsWith('_PACKAGE')
      ? value.slice(0, -8)
      : value
    return processedValue === '-1' ? '-' : processedValue
  }

  // 特定字段的-1值处理（确保这些字段的-1都显示为"-"）
  const fieldsToHandleMinusOne = [
    'speedTypeName',
    'homeProvinceName',
    'signNetworkTypeName',
    'visitProvinceName',
    'publicIp',
    'privateIp',
    'targetIp',
    'cmServiceName',
    'afid',
    'neId',
  ]

  if (
    fieldsToHandleMinusOne.includes(String(key)) &&
    (value === -1 || value === '-1')
  ) {
    return '-'
  }

  return String(value)
}

const getFieldClass = (prop: string, value: any) => {
  // 基础字段样式映射
  const fieldClassMap: Record<string, string> = {
    userId: 'user-id-field',
    cmServiceName: 'service-name-field',
    speedTypeName: 'speed-type-field',
    homeProvinceName: 'province-field',
    visitProvinceName: 'province-field',
    signNetworkTypeName: 'network-type-field',
    duration: 'duration-field',
    afid: 'afid-field',
    neId: 'ne-id-field',
    publicIp: 'ip-field',
    privateIp: 'ip-field',
    targetIp: 'ip-field',
    orderTime: 'time-field',
    returnTime: 'time-field',
    effectiveTime: 'time-field',
    expireTime: 'time-field',
    messageId: 'message-id-field',
    qosProductName: 'qos-product-name-field',
  }

  // 特殊处理响应码
  if (prop === 'respCode') {
    return value === 0 ? 'success-code' : 'error-code'
  }

  return fieldClassMap[prop] || ''
}

const getActions = () => {
  return fieldConfigs[props.statsType]?.actions || []
}

const getOperTypeName = (operType: number) => {
  const operTypeMap: Record<number, string> = {
    0: '订购',
    1: '退订',
    2: '修改',
  }
  return operTypeMap[operType] || `操作类型${operType}`
}

const getTotalCount = () => {
  // 从queryParams中获取count，或者从statsInfo中提取
  return props.queryParams.count || props.statsInfo.match(/\d+/)?.[0] || '未知'
}

const showRowDetails = (row: any) => {
  selectedRowData.value = row
  selectedRowJson.value = JSON.stringify(row, null, 2)
  detailDrawerVisible.value = true
}

const handleAction = (actionKey: string, row: any) => {
  switch (actionKey) {
    case 'viewOrder':
      goToOrderPage(row)
      break
    case 'viewFlow':
      goToFlowPage(row)
      break
    case 'viewTask':
      goToTaskPage(row)
      break
    case 'viewUserInfo':
      goToUserInfoPage(row.userId)
      break
  }
}

const goToOrderPage = (row: any) => {
  // 对于默载、专载调用统计和订购统计，使用userId作为号码
  const phoneNumber = row.userId

  if (
    phoneNumber &&
    phoneNumber !== '-' &&
    phoneNumber !== '' &&
    phoneNumber !== null &&
    phoneNumber !== undefined
  ) {
    // 获取dashboard传入的日期范围，用于订单记录页面的时间框
    const startTime = props.queryParams.startTime
    const endTime = props.queryParams.endTime

    const queryParams = new URLSearchParams()
    queryParams.set('phoneNumber', phoneNumber)

    // 传递日期范围给订单记录页面
    if (startTime && endTime) {
      queryParams.set('dateRangeStart', startTime)
      queryParams.set('dateRangeEnd', endTime)
    }

    const url = `${window.location.origin}${window.location.pathname}#/user/invoke?${queryParams.toString()}`
    window.open(url, '_blank', 'noopener')
  } else {
    ElMessage.warning('用户号码为空无法跳转')
  }
}

const goToFlowPage = (row: any) => {
  // 对于默载、专载调用统计和订购统计，使用userId作为号码
  const phoneNumber = row.userId

  if (
    phoneNumber &&
    phoneNumber !== '-' &&
    phoneNumber !== '' &&
    phoneNumber !== null &&
    phoneNumber !== undefined
  ) {
    // 获取dashboard传入的日期范围，用于流水记录页面的时间框
    const startTime = props.queryParams.startTime
    const endTime = props.queryParams.endTime

    const queryParams = new URLSearchParams()
    queryParams.set('phoneNumber', phoneNumber)

    // 流水记录页面传递日期范围
    if (startTime && endTime) {
      // 将dashboard的日期范围传递给流水记录页面
      queryParams.set('dateRangeStart', startTime)
      queryParams.set('dateRangeEnd', endTime)
    }

    const url = `${window.location.origin}${window.location.pathname}#/user/flow?${queryParams.toString()}`
    window.open(url, '_blank', 'noopener')
  } else {
    ElMessage.warning('用户号码为空无法跳转')
  }
}

const goToTaskPage = (row: any) => {
  // 跳转到批量任务详情页面
  const taskId = row.taskId || row.planId
  if (taskId) {
    const url = `${window.location.origin}${window.location.pathname}#/batch/task/${taskId}`
    window.open(url, '_blank', 'noopener')
  } else {
    ElMessage.warning('任务ID为空无法跳转')
  }
}

const goToUserInfoPage = (userId: any) => {
  // 跳转到用户信息页面
  if (
    userId &&
    userId !== '-' &&
    userId !== '' &&
    userId !== null &&
    userId !== undefined
  ) {
    const queryParams = new URLSearchParams()
    queryParams.set('phoneNumber', String(userId))

    const url = `${window.location.origin}${window.location.pathname}#/user/userinfo?${queryParams.toString()}`
    window.open(url, '_blank', 'noopener')
  } else {
    ElMessage.warning('用户ID为空无法跳转')
  }
}

const handleClose = () => {
  visible.value = false
  detailData.value = []
}

const handleRefresh = () => {
  emit('refresh')
}

const fetchDetailData = async () => {
  loading.value = true
  hasError.value = false
  errorMessage.value = ''
  detailData.value = []

  try {
    let response

    // 根据统计类型调用不同的API
    if (props.statsType === 'subscription') {
      // 调用用户订购与退订详单API
      response = await request.post(
        '/admin/inspection/subscribeOrderRecords/1.0',
        props.queryParams
      )
    } else if (props.statsType === 'defbearer') {
      // 调用默载详单API
      response = await request.post(
        '/admin/inspection/defbearerOrderRecords/1.0',
        props.queryParams
      )
    } else if (props.statsType === 'dedbearer') {
      // 调用专载详单API
      response = await request.post(
        '/admin/inspection/dedbearerOrderRecords/1.0',
        props.queryParams
      )
    }

    if (response && response.data) {
      detailData.value = response.data || []
    } else {
      hasError.value = true
      errorMessage.value = '获取数据失败：服务器返回空数据'
    }
  } catch (error: any) {
    hasError.value = true
    errorMessage.value = '获取详情数据失败: ' + (error.message || '未知错误')

    if (!error.isHandled) {
      ElMessage.error(errorMessage.value)
    }
    console.error('获取详情数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    fetchDetailData()
  }
})
</script>

<style scoped>
.detail-modal :deep(.el-dialog__body) {
  padding: 20px;
}

.modal-content {
  min-height: 400px;
}

.modern-table {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  width: 100%;
  table-layout: auto;
}

/* 专载和默载调用统计表格样式优化 */
.modern-table.bearer-table {
  table-layout: auto; /* 改为自动布局，让表格自适应 */
  width: 100%;
}

/* 表格容器样式优化 */
.modern-table :deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 表格列宽度控制 */
.modern-table :deep(.el-table__cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modern-table .el-table__header-wrapper {
  border-radius: 12px 12px 0 0;
}

.modern-table .el-table__body-wrapper {
  border-radius: 0 0 12px 12px;
}

.modern-table .el-table__row {
  transition: all 0.3s ease;
}

.modern-table .el-table__row:hover {
  background-color: #f8fafc !important;
}

.modern-table .el-table__cell {
  border-bottom: 1px solid #f1f5f9;
  padding: 14px 12px;
  vertical-align: middle;
}

.modern-table .el-table__header .el-table__cell {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: #ffffff !important;
  font-weight: 600;
  text-align: center;
  border: none;
  font-size: 13px;
  letter-spacing: 0.3px;
}

.modern-table .el-table__body .el-table__cell {
  font-size: 14px;
  color: #374151;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-row {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}

.success-code {
  color: #059669;
  font-weight: 600;
}

.error-code {
  color: #dc2626;
  font-weight: 600;
}

/* 字段样式 - 统一设计 */
/* 空值统一样式 */
.empty-value-field {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 20px;
}

.user-id-field {
  background: #dcfce7;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #166534;
  font-weight: 600;
  display: inline-block;
}

/* 用户ID链接样式 */
.user-id-link {
  background: #dcfce7;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1e40af;
  font-weight: 600;
  display: inline-block;
  cursor: pointer;
  text-decoration: underline;
  transition: all 0.2s ease;
}

.user-id-link:hover {
  background: #bfdbfe;
  color: #1d4ed8;
  transform: translateY(-1px);
}

.service-name-field {
  background: #dbeafe;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #1e40af;
  font-weight: 500;
  display: inline-block;
}

.speed-type-field,
.network-type-field {
  background: #fef3c7;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #92400e;
  font-weight: 500;
  display: inline-block;
}

.province-field {
  background: #f3e8ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #7c2d12;
  font-weight: 500;
  display: inline-block;
}

.duration-field {
  background: #ecfdf5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #15803d;
  font-weight: 600;
  text-align: center;
  display: inline-block;
}

.afid-field,
.ne-id-field {
  background: #e0f2fe;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #0369a1;
  font-weight: 500;
  display: inline-block;
}

.ip-field {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #475569;
  font-weight: 500;
  display: inline-block;
}

/* 消息ID字段样式 - 等宽字体便于阅读 */
.message-id-field {
  background: #e0f2fe;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #0369a1;
  font-weight: 500;
  letter-spacing: 0.3px;
  display: inline-block;
}

/* QoS产品名称字段样式 - 支持长文本显示 */
.qos-product-name-field {
  background: #fae8ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #7c2d12;
  font-weight: 500;
  line-height: 1.3;
  text-align: left;
  display: inline-block;
  max-width: 100%;
}

/* 时间字段样式 - 小背景色包裹文字 */
.time-field {
  background: #fef3c7;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #92400e;
  font-weight: 500;
  display: inline-block;
  letter-spacing: 0.2px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 20px;
}

.error-alert {
  max-width: 600px;
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 统计信息头部样式 */
.stats-header {
  margin-bottom: 20px;
}

.stats-info-tags {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.subscription-details,
.bearer-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-info {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

/* 详情抽屉样式 */
.row-details {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.detail-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.detail-item.highlight {
  background: #fef3c7;
  border-color: #fbbf24;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
  word-break: break-all;
}

.json-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.json-display .el-textarea__inner {
  background: #1f2937;
  color: #f9fafb;
  border: none;
}
</style>
