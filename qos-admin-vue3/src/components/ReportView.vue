<template>
  <div class="report-container">
    <!-- 报告头部 -->
    <div class="report-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">📊</div>
          <div class="title-section">
            <h1 class="main-title">QoS质量分析报告</h1>
            <p class="subtitle">Quality of Service Analysis Report</p>
          </div>
        </div>
        <div class="report-info">
          <div class="info-item">
            <span class="info-label">查询时间</span>
            <span class="info-value">{{ dateRangeText }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">生成时间</span>
            <span class="info-value">{{ generateTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="stats-overview">
      <div v-for="stat in statsCards" :key="stat.title" class="stats-card">
        <div class="stat-icon" :style="{ backgroundColor: stat.color }">
          {{ stat.icon }}
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.title }}</div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="tables-section">
      <!-- 网关异常统计 -->
      <div v-if="httpErrorData.length > 0" class="table-card">
        <h3 class="table-title">
          <span class="title-icon">🚨</span>
          网关异常统计 ({{ httpErrorData.length }}条)
        </h3>
        <div class="table-wrapper">
          <table class="data-table">
            <caption class="sr-only">
              网关HTTP异常统计数据表
            </caption>
            <thead>
              <tr>
                <th>接口URI</th>
                <th>响应码</th>
                <th>响应描述</th>
                <th>异常次数</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in sortedHttpErrorData"
                :key="item.requestUri + item.respCode"
              >
                <td class="uri-cell">{{ item.requestUri }}</td>
                <td class="status-cell">
                  <span class="status-badge error">{{ item.respCode }}</span>
                </td>
                <td class="desc-cell">{{ item.respDesc }}</td>
                <td class="count-cell">{{ item.count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 订购退订统计 -->
      <div v-if="subscriptionData.length > 0" class="table-card">
        <h3 class="table-title">
          <span class="title-icon">📱</span>
          用户订购与退订统计 ({{ subscriptionData.length }}条)
        </h3>
        <div class="table-wrapper">
          <table class="data-table">
            <caption class="sr-only">
              用户订购与退订统计数据表
            </caption>
            <thead>
              <tr>
                <th>操作类型</th>
                <th>响应代码</th>
                <th>响应描述</th>
                <th>操作次数</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in sortedSubscriptionData"
                :key="item.operTypeName + item.respCode"
              >
                <td class="type-cell">{{ item.operTypeName }}</td>
                <td class="code-cell">
                  <span
                    class="code-badge"
                    :class="item.respCode === 0 ? 'success' : 'warning'"
                  >
                    {{ item.respCode }}
                  </span>
                </td>
                <td class="desc-cell">{{ item.respDesc }}</td>
                <td class="count-cell">{{ item.count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 默载调用统计 -->
      <div v-if="defbearerData.length > 0" class="table-card">
        <h3 class="table-title">
          <span class="title-icon">📊</span>
          默载调用统计 ({{ defbearerData.length }}条)
        </h3>
        <div class="table-wrapper">
          <table class="data-table">
            <caption class="sr-only">
              默载调用统计数据表
            </caption>
            <thead>
              <tr>
                <th>订单来源</th>
                <th>操作类型</th>
                <th>响应代码</th>
                <th>响应描述</th>
                <th>调用次数</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in sortedDefbearerData"
                :key="item.orderSource + item.operTypeName + item.respCode"
              >
                <td class="source-cell">{{ item.orderSource }}</td>
                <td class="type-cell">{{ item.operTypeName }}</td>
                <td class="code-cell">
                  <span
                    class="code-badge"
                    :class="item.respCode === 0 ? 'success' : 'warning'"
                  >
                    {{ item.respCode }}
                  </span>
                </td>
                <td class="desc-cell">{{ item.respDesc }}</td>
                <td class="count-cell">{{ item.count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 专载调用统计 -->
      <div v-if="dedbearerData.length > 0" class="table-card">
        <h3 class="table-title">
          <span class="title-icon">🎯</span>
          专载调用统计 ({{ dedbearerData.length }}条)
        </h3>
        <div class="table-wrapper">
          <table class="data-table">
            <caption class="sr-only">
              专载调用统计数据表
            </caption>
            <thead>
              <tr>
                <th>订单来源</th>
                <th>操作类型</th>
                <th>响应代码</th>
                <th>响应描述</th>
                <th>调用次数</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in sortedDedbearerData"
                :key="item.orderSource + item.operTypeName + item.respCode"
              >
                <td class="source-cell">{{ item.orderSource }}</td>
                <td class="type-cell">{{ item.operTypeName }}</td>
                <td class="code-cell">
                  <span
                    class="code-badge"
                    :class="item.respCode === 0 ? 'success' : 'warning'"
                  >
                    {{ item.respCode }}
                  </span>
                </td>
                <td class="desc-cell">{{ item.respDesc }}</td>
                <td class="count-cell">{{ item.count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 默载批量调用统计 -->
      <div v-if="defbearerBatchData.length > 0" class="table-card">
        <h3 class="table-title">
          <span class="title-icon">📈</span>
          默载批量调用统计 ({{ defbearerBatchData.length }}条)
        </h3>
        <div class="table-wrapper">
          <table class="data-table">
            <caption class="sr-only">
              默载批量调用统计数据表
            </caption>
            <thead>
              <tr>
                <th>归属省</th>
                <th>响应代码</th>
                <th>响应描述</th>
                <th>调用次数</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in sortedDefbearerBatchData"
                :key="item.homeProvinceName + item.respCode"
              >
                <td class="province-cell">{{ item.homeProvinceName }}</td>
                <td class="code-cell">
                  <span
                    class="code-badge"
                    :class="item.respCode === 0 ? 'success' : 'warning'"
                  >
                    {{ item.respCode }}
                  </span>
                </td>
                <td class="desc-cell">{{ item.respDesc }}</td>
                <td class="count-cell">{{ item.count }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 报告底部 -->
    <div class="report-footer">
      <div class="footer-content">
        <div class="footer-left">
          <span class="footer-text">本报告由QoS管理系统自动生成</span>
        </div>
        <div class="footer-right">
          <span class="footer-text">第 1 页，共 1 页</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  dateRange?: [string, string] | null
  httpErrorData: Array<{
    requestUri: string
    respCode: number
    respDesc: string
    count: number
  }>
  subscriptionData: Array<{
    operTypeName: string
    respCode: number
    respDesc: string
    count: number
  }>
  defbearerData: Array<any>
  dedbearerData: Array<any>
  defbearerBatchData: Array<any>
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: null,
  httpErrorData: () => [],
  subscriptionData: () => [],
  defbearerData: () => [],
  dedbearerData: () => [],
  defbearerBatchData: () => [],
})

const dateRangeText = computed(() => {
  if (!props.dateRange) return '全部时间'
  return `${props.dateRange[0]} 至 ${props.dateRange[1]}`
})

const generateTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

// 对数据进行排序（按count倒序）
const sortedHttpErrorData = computed(() => {
  return [...props.httpErrorData].sort((a, b) => b.count - a.count)
})

const sortedSubscriptionData = computed(() => {
  return [...props.subscriptionData].sort((a, b) => b.count - a.count)
})

const sortedDefbearerData = computed(() => {
  return [...props.defbearerData].sort((a, b) => b.count - a.count)
})

const sortedDedbearerData = computed(() => {
  return [...props.dedbearerData].sort((a, b) => b.count - a.count)
})

const sortedDefbearerBatchData = computed(() => {
  return [...props.defbearerBatchData].sort((a, b) => b.count - a.count)
})

const statsCards = computed(() => [
  {
    title: '网关异常',
    value: props.httpErrorData.length,
    icon: '🚨',
    color: '#ff6b6b',
  },
  {
    title: '订购退订',
    value: props.subscriptionData.length,
    icon: '📱',
    color: '#4ecdc4',
  },
  {
    title: '默载调用',
    value: props.defbearerData.length,
    icon: '📊',
    color: '#45b7d1',
  },
  {
    title: '专载调用',
    value: props.dedbearerData.length,
    icon: '🎯',
    color: '#f9ca24',
  },
  {
    title: '批量调用',
    value: props.defbearerBatchData.length,
    icon: '📈',
    color: '#6c5ce7',
  },
])
</script>

<style scoped>
.report-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40px;
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

.report-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-icon {
  font-size: 48px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.main-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 5px 0 0 0;
  font-weight: 300;
}

.report-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-label {
  font-weight: 600;
  color: #34495e;
  min-width: 80px;
}

.info-value {
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
  color: #2c3e50;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

.tables-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.table-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 24px;
}

.table-wrapper {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  border: none;
}

.data-table th:first-child {
  border-radius: 10px 0 0 10px;
}

.data-table th:last-child {
  border-radius: 0 10px 10px 0;
}

.data-table td {
  padding: 14px 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.data-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.data-table tr:hover {
  background-color: #e3f2fd;
}

.uri-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
}

.status-badge,
.code-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 12px;
}

.status-badge.error {
  background: #ffebee;
  color: #d32f2f;
}

.code-badge.success {
  background: #e8f5e8;
  color: #2e7d32;
}

.code-badge.warning {
  background: #fff3e0;
  color: #f57c00;
}

.count-cell {
  font-weight: 600;
  color: #667eea;
  text-align: right;
}

.source-cell,
.province-cell {
  font-weight: 500;
  color: #2c3e50;
  background: rgba(102, 126, 234, 0.05);
}

.type-cell {
  font-weight: 500;
  color: #667eea;
}

.report-footer {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px 30px;
  margin-top: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-text {
  color: #7f8c8d;
  font-size: 14px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
