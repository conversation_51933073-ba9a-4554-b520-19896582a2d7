<script setup lang="ts">
import { Search, Phone, Monitor } from '@element-plus/icons-vue'
import { reactive, ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '../utils/request'

// Props定义
interface Props {
  initialPhoneNumber?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialPhoneNumber: '',
})

// 查询类型枚举
enum QueryType {
  PHONE = 'phone',
  IP = 'ip',
}

// 查询表单数据
let queryForm = reactive({
  phoneNumber: '' as string | number,
  privateIpv4: '',
  publicIpv4: '',
  ipv6: '',
  messageId: '', // takeNum接口需要的消息序号
})

// 当前查询类型
const currentQueryType = ref<QueryType>(QueryType.PHONE)

// 计算属性：当前查询方式的标题
const queryTypeTitle = computed(() => {
  return currentQueryType.value === QueryType.PHONE
    ? '按手机号查询'
    : '按IP地址查询'
})

// 计算属性：IP查询时是否有输入值
const hasIpInput = computed(() => {
  return (
    (queryForm.privateIpv4.trim() !== '' &&
      queryForm.publicIpv4.trim() !== '') ||
    queryForm.ipv6.trim() !== ''
  )
})

const emit = defineEmits(['click', 'loading'])

// 监听初始手机号变化
watch(
  () => props.initialPhoneNumber,
  (newValue) => {
    if (newValue && newValue.trim() !== '') {
      // 将字符串转换为数字，以符合表单验证规则和v-model.number
      const phoneNumber = parseInt(newValue)
      if (!isNaN(phoneNumber) && phoneNumber > 0) {
        queryForm.phoneNumber = phoneNumber
      }
    }
  },
  { immediate: true }
)

// 切换查询类型
function switchQueryType() {
  currentQueryType.value =
    currentQueryType.value === QueryType.PHONE ? QueryType.IP : QueryType.PHONE
  // 清空表单数据
  clearForm()
}

// 清空表单
function clearForm() {
  queryForm.phoneNumber = '' // 重置为空字符串，页面显示为空
  queryForm.privateIpv4 = ''
  queryForm.publicIpv4 = ''
  queryForm.ipv6 = ''
  queryForm.messageId = ''
}

// 生成消息序号
function generateMessageId() {
  return 'MSG_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 处理查询点击
async function handleClick() {
  if (currentQueryType.value === QueryType.PHONE) {
    // 按手机号查询
    if (!queryForm.phoneNumber) {
      ElMessage.warning('请输入手机号')
      return
    }
    emit('click', queryForm.phoneNumber)
  } else {
    // 按IP地址查询
    if (!hasIpInput.value) {
      ElMessage.warning('请至少输入一个IP地址')
      return
    }

    try {
      emit('loading', true)

      // 生成消息序号
      queryForm.messageId = generateMessageId()

      // 调用takeNum接口获取手机号
      const takeNumParams = {
        messageId: queryForm.messageId,
        privateIpv4: queryForm.privateIpv4 || undefined,
        publicIpv4: queryForm.publicIpv4 || undefined,
        ipv6: queryForm.ipv6 || undefined,
      }

      const response = await request.post(
        '/admin/user-takenum/1.0',
        takeNumParams
      )

      if (response.data && response.data.msisdn) {
        const phoneNumber = response.data.msisdn
        ElMessage.success(`通过IP地址获取到手机号：${phoneNumber}`)
        // 使用获取到的手机号进行查询
        emit('click', phoneNumber)
      } else {
        ElMessage.error('未能通过IP地址获取到手机号')
      }
    } catch (error: any) {
      console.error('通过IP获取手机号失败:', error)
      if (!error.isHandled) {
        ElMessage.error(
          '通过IP获取手机号失败：' + (error.message || '未知错误')
        )
      }
    } finally {
      emit('loading', false)
    }
  }
}
</script>

<template>
  <!-- 查询表单 -->
  <div class="query-container">
    <!-- 查询类型切换 -->
    <div class="query-type-header">
      <div class="query-type-title">
        <el-icon class="title-icon">
          <Phone v-if="currentQueryType === 'phone'" />
          <Monitor v-else />
        </el-icon>
        <span>{{ queryTypeTitle }}</span>
      </div>
      <el-button type="text" class="switch-button" @click="switchQueryType">
        <el-icon>
          <Monitor v-if="currentQueryType === 'phone'" />
          <Phone v-else />
        </el-icon>
        {{ currentQueryType === 'phone' ? '切换到IP查询' : '切换到手机号查询' }}
      </el-button>
    </div>

    <!-- 手机号查询表单 -->
    <el-form
      v-if="currentQueryType === 'phone'"
      :model="queryForm"
      :inline="true"
      class="query-form"
    >
      <el-form-item
        class="search-item"
        prop="phoneNumber"
        :rules="[
          { required: true, message: '手机号必填' },
          {
            validator: (_rule: any, value: any, callback: any) => {
              // 空值通过验证（由 required 规则处理）
              if (!value) {
                callback()
                return
              }

              // 检查是否为纯数字（支持字符串和数字类型）
              const valueStr = String(value)
              if (!/^\d+$/.test(valueStr)) {
                callback(new Error('手机号必须是数字'))
                return
              }

              callback()
            },
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          v-model.number="queryForm.phoneNumber"
          placeholder="请输入手机号"
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon>
              <Phone />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="button-group">
        <el-button type="primary" class="search-button" @click="handleClick">
          <el-icon>
            <Search />
          </el-icon>
          查询
        </el-button>
      </el-form-item>
    </el-form>

    <!-- IP地址查询表单 -->
    <el-form v-else :model="queryForm" class="query-form ip-query-form">
      <div class="ip-input-group">
        <el-form-item label="私网IPv4" class="ip-form-item">
          <el-input
            v-model="queryForm.privateIpv4"
            placeholder="请输入私网IPv4地址"
            clearable
            class="ip-input"
          >
            <template #prefix>
              <el-icon>
                <Monitor />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="公网IPv4" class="ip-form-item">
          <el-input
            v-model="queryForm.publicIpv4"
            placeholder="请输入公网IPv4地址"
            clearable
            class="ip-input"
          >
            <template #prefix>
              <el-icon>
                <Monitor />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="IPv6地址" class="ip-form-item">
          <el-input
            v-model="queryForm.ipv6"
            placeholder="请输入IPv6地址"
            clearable
            class="ip-input"
          >
            <template #prefix>
              <el-icon>
                <Monitor />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </div>

      <div class="ip-query-tip">
        <el-alert
          title="提示：请至少输入一个IP地址，系统将通过IP地址获取对应的手机号进行查询"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-form-item class="button-group">
        <el-button type="primary" class="search-button" @click="handleClick">
          <el-icon>
            <Search />
          </el-icon>
          查询
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.query-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 21, 41, 0.08);
  overflow: hidden;
}

/* 查询类型头部 */
.query-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.query-type-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.title-icon {
  font-size: 18px;
  color: #3b82f6;
}

.switch-button {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.switch-button:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 查询表单基础样式 */
.query-form {
  padding: 24px;
}

/* 手机号查询表单 */
.query-form:not(.ip-query-form) {
  display: flex;
  align-items: center;
}

.search-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-input {
  width: 300px;
}

/* IP查询表单 */
.ip-query-form {
  display: block;
}

.ip-input-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.ip-form-item {
  margin-bottom: 0;
}

.ip-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.ip-input {
  width: 100%;
}

.ip-query-tip {
  margin-bottom: 20px;
}

.ip-query-tip :deep(.el-alert) {
  border-radius: 6px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
}

.ip-query-tip :deep(.el-alert__title) {
  font-size: 13px;
  color: #0369a1;
}

/* 输入框样式 */
.search-input :deep(.el-input__wrapper),
.ip-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  padding-left: 8px;
  transition: all 0.3s;
  border-radius: 6px;
}

.search-input :deep(.el-input__wrapper):hover,
.ip-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-input :deep(.el-input__wrapper.is-focus),
.ip-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.search-input :deep(.el-input__prefix),
.ip-input :deep(.el-input__prefix) {
  color: #909399;
  font-size: 16px;
  margin-right: 4px;
}

/* 按钮样式 */
.button-group {
  margin: 0;
  text-align: center;
}

.button-group :deep(.el-button) {
  padding: 10px 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  transition: all 0.3s;
  border-radius: 6px;
  min-width: 120px;
}

.search-button {
  background: var(--el-color-primary);
}

.search-button:hover {
  background: var(--el-color-primary-light-3);
}

/* 表单验证错误样式 */
:deep(.el-form-item__error) {
  padding-top: 4px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .query-type-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .switch-button {
    align-self: flex-end;
  }

  .ip-input-group {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .query-form:not(.ip-query-form) {
    flex-direction: column;
    align-items: stretch;
  }

  .search-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input {
    width: 100%;
  }
}
</style>
