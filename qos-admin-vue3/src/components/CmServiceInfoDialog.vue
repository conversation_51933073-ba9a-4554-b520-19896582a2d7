<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { CmServiceDetailDTO, getCmServiceDetail } from '../api/cmService'
import { getProvinceNameByCode } from '../utils/provinceUtil'
import PccStrategyInfoDialog from './PccStrategyInfoDialog.vue'

// 组件属性
interface Props {
  visible: boolean
  cmServiceId: string | null
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const serviceInfo = ref<CmServiceDetailDTO | null>(null)

// PCC策略信息弹窗相关状态
const pccStrategyDialogVisible = ref(false)
const selectedPccStrategyId = ref<number | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
})

// 服务类型映射
const serviceTypeMap = {
  0: { text: '商用服务', type: 'primary' },
  1: { text: '测试服务', type: 'warning' },
} as const

// 服务模式映射
const serviceModeMap = {
  0: { text: '2C', type: 'primary' },
  1: { text: 'B2C', type: 'warning' },
} as const

// 核心网子类型映射
const coreNetworkSubtypeMap = {
  0: { text: '人网', type: 'primary' },
  1: { text: '物网', type: 'success' },
} as const

// 网络模式映射
const networkModeMap = {
  0: { text: '4G', type: 'primary' },
  1: { text: '5G', type: 'success' },
  2: { text: '4G&5G', type: 'warning' },
} as const

// 承载模式映射
const bearerModeMap = {
  0: { text: '专载', type: 'primary' },
  1: { text: '默载', type: 'success' },
  2: { text: 'AM-PCF', type: 'warning' },
} as const

// 速度类型映射
const speedTypeMap = {
  0: { text: '加速', type: 'success' },
  1: { text: '限速', type: 'warning' },
  2: { text: '其他', type: 'info' },
} as const

// 无线网网络模式映射
const ranNetworkModeMap = {
  0: { text: '4G', type: 'success' },
  1: { text: '5G', type: 'warning' },
  2: { text: '4G&5G', type: 'info' },
} as const

// 状态映射
const statusMap = {
  0: { text: '启用', type: 'success' },
  '-1': { text: '停用', type: 'danger' },
} as const

// 检查配置键映射
const checkKeyMap = {
  targetIpWhite: '目标IP白名单校验',
  targetIpBlack: '目标IP黑名单校验',
  visitProvinceCheck: '拜访省校验',
  durationCheck: '时长校验',
  delTimeCheck: '未达删除时限校验',
  allow5GConnect: '是否允许5G接入',
} as const

// 获取映射信息的通用函数
const getMapInfo = (value: number | string, map: Record<string, any>) => {
  return map[value] || { text: '未知', type: 'info' }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 格式化归属省列表
const formatProvinceList = (provinceList: number[]) => {
  if (!provinceList || provinceList.length === 0) return '-'
  return provinceList.map((code) => getProvinceNameByCode(code)).join('、')
}

// 格式化检查配置键
const formatCheckKey = (key: string) => {
  return checkKeyMap[key as keyof typeof checkKeyMap] || key
}

// 显示PCC策略信息弹窗
const showPccStrategyInfo = (pccStrategyId: number) => {
  if (!pccStrategyId) return
  selectedPccStrategyId.value = pccStrategyId
  pccStrategyDialogVisible.value = true
}

// 获取通信服务详情
const fetchServiceDetail = async (cmServiceId: string) => {
  if (!cmServiceId) return

  loading.value = true
  try {
    const response = await getCmServiceDetail({ cmServiceId })

    if (response.code === 0 && response.data) {
      serviceInfo.value = response.data
    } else {
      ElMessage.error(response.msg || '获取通信服务信息失败')
      serviceInfo.value = null
    }
  } catch (error: any) {
    if (!error.isHandled) {
      ElMessage.error('获取通信服务信息失败：' + (error.message || '未知错误'))
    }
    console.error('获取通信服务信息失败:', error)
    serviceInfo.value = null
  } finally {
    loading.value = false
  }
}

// 监听服务ID变化
watch(
  () => props.cmServiceId,
  (newId) => {
    if (newId && props.visible) {
      fetchServiceDetail(newId)
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.cmServiceId) {
      fetchServiceDetail(props.cmServiceId)
    } else if (!visible) {
      serviceInfo.value = null
    }
  }
)

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="通信服务信息"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    @close="handleClose"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><InfoFilled /></el-icon>
        <span class="header-title">通信服务信息</span>
        <el-tag
          v-if="serviceInfo"
          type="primary"
          size="small"
          class="service-id-tag"
        >
          ID: {{ serviceInfo.cmServiceId }}
        </el-tag>
      </div>
    </template>

    <div v-loading="loading" class="dialog-content">
      <div v-if="!loading && !serviceInfo" class="empty-state">
        <el-empty description="暂无通信服务信息" />
      </div>

      <div v-else-if="serviceInfo" class="service-info">
        <!-- 基础信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基础信息</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="服务ID" :span="1">
              <el-tag type="primary" size="small">{{
                serviceInfo.cmServiceId
              }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="服务名称" :span="1">
              <span class="service-name">{{
                serviceInfo.cmServiceName || '-'
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="服务类型" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.serviceType, serviceTypeMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.serviceType, serviceTypeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="服务模式" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.serviceMode, serviceModeMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.serviceMode, serviceModeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="核心网子类型" :span="1">
              <el-tag
                :type="
                  getMapInfo(
                    serviceInfo.coreNetworkSubtype,
                    coreNetworkSubtypeMap
                  ).type
                "
                size="small"
              >
                {{
                  getMapInfo(
                    serviceInfo.coreNetworkSubtype,
                    coreNetworkSubtypeMap
                  ).text
                }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="网络模式" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.networkMode, networkModeMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.networkMode, networkModeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="承载模式" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.bearerMode, bearerModeMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.bearerMode, bearerModeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="速度类型" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.speedType, speedTypeMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.speedType, speedTypeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="无线网网络模式" :span="1">
              <el-tag
                :type="
                  getMapInfo(serviceInfo.ranNetworkMode, ranNetworkModeMap).type
                "
                size="small"
              >
                {{
                  getMapInfo(serviceInfo.ranNetworkMode, ranNetworkModeMap).text
                }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="状态" :span="1">
              <el-tag
                :type="getMapInfo(serviceInfo.status, statusMap).type"
                size="small"
              >
                {{ getMapInfo(serviceInfo.status, statusMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="归属省份" :span="2">
              <span class="province-list">{{
                formatProvinceList(serviceInfo.homeProvinceList)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="4G PCC策略" :span="1">
              <el-tag
                v-if="serviceInfo.pccStrategy4g"
                type="primary"
                size="small"
                class="pcc-strategy-tag clickable-pcc-strategy"
                @click="showPccStrategyInfo(serviceInfo.pccStrategy4g)"
              >
                {{ serviceInfo.pccStrategy4g }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>

            <el-descriptions-item label="5G PCC策略" :span="1">
              <el-tag
                v-if="serviceInfo.pccStrategy5g"
                type="success"
                size="small"
                class="pcc-strategy-tag clickable-pcc-strategy"
                @click="showPccStrategyInfo(serviceInfo.pccStrategy5g)"
              >
                {{ serviceInfo.pccStrategy5g }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间" :span="1">
              <span class="time-value">{{
                formatDate(serviceInfo.createTime)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="更新时间" :span="1">
              <span class="time-value">{{
                formatDate(serviceInfo.updateTime)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="备注" :span="2">
              <div class="comment">
                {{ serviceInfo.comment || '暂无备注' }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 速率配置信息 (仅专载服务显示) -->
        <el-card
          v-if="serviceInfo.bearerMode === 0 && serviceInfo.bitrateConfig"
          class="info-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span class="card-title">速率配置</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="4G上行MBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.uplinkMbr4g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="4G下行MBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.downlinkMbr4g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="4G上行GBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.uplinkGbr4g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="4G下行GBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.downlinkGbr4g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="5G上行MBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.uplinkMbr5g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="5G下行MBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.downlinkMbr5g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="5G上行GBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.uplinkGbr5g || '-' }} Mbps</span
              >
            </el-descriptions-item>

            <el-descriptions-item label="5G下行GBR" :span="1">
              <span class="bitrate-value"
                >{{ serviceInfo.bitrateConfig.downlinkGbr5g || '-' }} Mbps</span
              >
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 检查配置信息 -->
        <el-card
          v-if="
            serviceInfo.checkConfigList &&
            serviceInfo.checkConfigList.length > 0
          "
          class="info-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span class="card-title">检查配置</span>
            </div>
          </template>

          <el-table :data="serviceInfo.checkConfigList" border size="small">
            <el-table-column prop="checkKey" label="检查项" min-width="150">
              <template #default="{ row }">
                <span class="check-key">{{
                  formatCheckKey(row.checkKey)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="checkValue" label="检查值" min-width="200">
              <template #default="{ row }">
                <span class="check-value">{{ row.checkValue || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <el-tag
                  :type="getMapInfo(row.status, statusMap).type"
                  size="small"
                >
                  {{ getMapInfo(row.status, statusMap).text }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 网元配置信息 (仅专载服务显示) -->
        <el-card
          v-if="
            serviceInfo.bearerMode === 0 &&
            serviceInfo.neConfigList &&
            serviceInfo.neConfigList.length > 0
          "
          class="info-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span class="card-title">网元配置</span>
            </div>
          </template>

          <el-table :data="serviceInfo.neConfigList" border size="small">
            <el-table-column
              prop="homeProvince"
              label="归属省份"
              width="120"
              align="center"
            >
              <template #default="{ row }">
                <span class="province-name">{{
                  getProvinceNameByCode(row.homeProvince)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="aacId" label="AAC网元ID" min-width="150">
              <template #default="{ row }">
                <code v-if="row.aacId" class="ne-id">{{ row.aacId }}</code>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="nefId" label="NEF网元ID" min-width="150">
              <template #default="{ row }">
                <code v-if="row.nefId" class="ne-id">{{ row.nefId }}</code>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- PCC策略信息弹窗 -->
    <PccStrategyInfoDialog
      v-model:visible="pccStrategyDialogVisible"
      :pcc-strategy-id="selectedPccStrategyId"
    />
  </el-dialog>
</template>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.service-id-tag {
  margin-left: auto;
}

.dialog-content {
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.service-info {
  padding: 8px 0;
}

.info-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.service-name {
  font-weight: 500;
  color: #303133;
}

.province-list {
  color: #606266;
  line-height: 1.5;
}

.pcc-strategy {
  font-weight: 500;
  color: #409eff;
}

.pcc-strategy-tag {
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.clickable-pcc-strategy {
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.clickable-pcc-strategy:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  filter: brightness(1.1);
}

.clickable-pcc-strategy:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.time-value {
  color: #606266;
  font-size: 14px;
}

.comment {
  max-height: 80px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

:deep(.el-loading-mask) {
  border-radius: 4px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 16px;
}

.bitrate-value {
  font-weight: 500;
  color: #409eff;
}

.check-key {
  font-weight: 500;
  color: #303133;
}

.check-value {
  color: #606266;
  word-break: break-all;
}

.province-name {
  font-weight: 500;
  color: #303133;
}

.ne-id {
  background-color: #f5f7fa;
  color: #606266;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px 0;
}
</style>
