<template>
  <div class="anomaly-table">
    <!-- 网关异常 -->
    <el-row :gutter="20" v-if="httpfailData.length > 0">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>网关异常</span>
            </div>
          </template>
          <el-table
            :data="httpfailData"
            :loading="loading"
            border
            style="width: 100%"
          >
            <!-- 动态维度列 -->
            <el-table-column
              v-for="dimensionKey in allDimensionKeys"
              :key="dimensionKey"
              :label="formatDimensionKey(dimensionKey)"
              :prop="`dimensions.${dimensionKey}`"
              :width="getColumnWidth(dimensionKey)"
            >
              <template #default="{ row }">
                <div class="cell-content">
                  {{ row.dimensions?.[dimensionKey] ?? '-' }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="currentCount" label="当前值" width="100" align="center">
              <template #default="{ row }">
                {{ Number(row.currentCount).toLocaleString() }}
              </template>
            </el-table-column>
            
            <el-table-column prop="thresholdValue" label="阈值" width="100" align="center">
              <template #default="{ row }">
                {{ row.thresholdValue }}
              </template>
            </el-table-column>
            
            <el-table-column prop="currentProportion" label="当前占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.currentProportion !== undefined && row.currentProportion !== null">
                  {{ (Number(row.currentProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="historicalProportion" label="历史占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.historicalProportion !== undefined && row.historicalProportion !== null">
                  {{ (Number(row.historicalProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="anomalyType" label="异常类型" width="120">
              <template #default="{ row }">
                {{ getAnomalyTypeText(row.anomalyType) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订购退订 -->
    <el-row :gutter="20" class="mt-20" v-if="subscriptionData.length > 0">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>订购退订</span>
            </div>
          </template>
          <el-table
            :data="subscriptionData"
            :loading="loading"
            border
            style="width: 100%"
          >
            <!-- 动态维度列 -->
            <el-table-column
              v-for="dimensionKey in allDimensionKeys"
              :key="dimensionKey"
              :label="formatDimensionKey(dimensionKey)"
              :prop="`dimensions.${dimensionKey}`"
              :width="getColumnWidth(dimensionKey)"
            >
              <template #default="{ row }">
                <div class="cell-content">
                  {{ row.dimensions?.[dimensionKey] ?? '-' }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="currentCount" label="当前值" width="100" align="center">
              <template #default="{ row }">
                {{ Number(row.currentCount).toLocaleString() }}
              </template>
            </el-table-column>
            
            <el-table-column prop="thresholdValue" label="阈值" width="100" align="center">
              <template #default="{ row }">
                {{ row.thresholdValue }}
              </template>
            </el-table-column>
            
            <el-table-column prop="currentProportion" label="当前占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.currentProportion !== undefined && row.currentProportion !== null">
                  {{ (Number(row.currentProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="historicalProportion" label="历史占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.historicalProportion !== undefined && row.historicalProportion !== null">
                  {{ (Number(row.historicalProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="anomalyType" label="异常类型" width="120">
              <template #default="{ row }">
                {{ getAnomalyTypeText(row.anomalyType) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 默载调用 -->
    <el-row :gutter="20" class="mt-20" v-if="defbearerData.length > 0">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>默载调用</span>
            </div>
          </template>
          <el-table
            :data="defbearerData"
            :loading="loading"
            border
            style="width: 100%"
          >
            <!-- 动态维度列 -->
            <el-table-column
              v-for="dimensionKey in allDimensionKeys"
              :key="dimensionKey"
              :label="formatDimensionKey(dimensionKey)"
              :prop="`dimensions.${dimensionKey}`"
              :width="getColumnWidth(dimensionKey)"
            >
              <template #default="{ row }">
                <div class="cell-content">
                  {{ row.dimensions?.[dimensionKey] ?? '-' }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="currentCount" label="当前值" width="100" align="center">
              <template #default="{ row }">
                {{ Number(row.currentCount).toLocaleString() }}
              </template>
            </el-table-column>
            
            <el-table-column prop="thresholdValue" label="阈值" width="100" align="center">
              <template #default="{ row }">
                {{ row.thresholdValue }}
              </template>
            </el-table-column>
            
            <el-table-column prop="currentProportion" label="当前占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.currentProportion !== undefined && row.currentProportion !== null">
                  {{ (Number(row.currentProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="historicalProportion" label="历史占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.historicalProportion !== undefined && row.historicalProportion !== null">
                  {{ (Number(row.historicalProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="anomalyType" label="异常类型" width="120">
              <template #default="{ row }">
                {{ getAnomalyTypeText(row.anomalyType) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 专载调用 -->
    <el-row :gutter="20" class="mt-20" v-if="dedbearerData.length > 0">
      <el-col :span="24">
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span>专载调用</span>
            </div>
          </template>
          <el-table
            :data="dedbearerData"
            :loading="loading"
            border
            style="width: 100%"
          >
            <!-- 动态维度列 -->
            <el-table-column
              v-for="dimensionKey in allDimensionKeys"
              :key="dimensionKey"
              :label="formatDimensionKey(dimensionKey)"
              :prop="`dimensions.${dimensionKey}`"
              :width="getColumnWidth(dimensionKey)"
            >
              <template #default="{ row }">
                <div class="cell-content">
                  {{ row.dimensions?.[dimensionKey] ?? '-' }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="currentCount" label="当前值" width="100" align="center">
              <template #default="{ row }">
                {{ Number(row.currentCount).toLocaleString() }}
              </template>
            </el-table-column>
            
            <el-table-column prop="thresholdValue" label="阈值" width="100" align="center">
              <template #default="{ row }">
                {{ row.thresholdValue }}
              </template>
            </el-table-column>
            
            <el-table-column prop="currentProportion" label="当前占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.currentProportion !== undefined && row.currentProportion !== null">
                  {{ (Number(row.currentProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="historicalProportion" label="历史占比" width="100" align="center">
              <template #default="{ row }">
                <span v-if="row.historicalProportion !== undefined && row.historicalProportion !== null">
                  {{ (Number(row.historicalProportion) * 100).toFixed(2) }}%
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="anomalyType" label="异常类型" width="120">
              <template #default="{ row }">
                {{ getAnomalyTypeText(row.anomalyType) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 无数据提示 -->
    <el-row v-if="anomalies.length === 0 && !loading">
      <el-col :span="24">
        <el-empty description="暂无异常数据" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import type { AnomalyResult } from '../types/anomalyDetection'
import { computed } from 'vue'

interface Props {
  anomalies: AnomalyResult[]
  loading?: boolean
}

const props = defineProps<Props>()

// 按业务类型分组数据
const httpfailData = computed(() => 
  props.anomalies.filter(item => {
    if (item.businessType) {
      return item.businessType === 'httpfail'
    }
    // 如果没有businessType，则根据ruleId判断
    const ruleId = item.ruleId || ''
    return ruleId.startsWith('HTTP-')
  })
)

const subscriptionData = computed(() => 
  props.anomalies.filter(item => {
    if (item.businessType) {
      return item.businessType === 'subscription'
    }
    // 如果没有businessType，则根据ruleId判断
    const ruleId = item.ruleId || ''
    return ruleId.startsWith('SUB-')
  })
)

const defbearerData = computed(() => 
  props.anomalies.filter(item => {
    if (item.businessType) {
      return item.businessType === 'defbearer'
    }
    // 如果没有businessType，则根据ruleId判断
    const ruleId = item.ruleId || ''
    return ruleId.startsWith('DEF-')
  })
)

const dedbearerData = computed(() => 
  props.anomalies.filter(item => {
    if (item.businessType) {
      return item.businessType === 'dedbearer'
    }
    // 如果没有businessType，则根据ruleId判断
    const ruleId = item.ruleId || ''
    return ruleId.startsWith('DED-')
  })
)

// 提取所有维度键
const allDimensionKeys = computed(() => {
  const keys = new Set<string>()
  props.anomalies.forEach(anomaly => {
    if (anomaly.dimensions) {
      Object.keys(anomaly.dimensions).forEach(key => keys.add(key))
    }
  })
  
  // 定义业务逻辑顺序
  const orderMap: Record<string, number> = {
    orderSource: 1,      // 订单来源 - 第一
    operType: 2,         // 操作类型 - 第二
    respCode: 3,         // 响应码 - 第三
    respDesc: 4,         // 响应描述 - 第四
    errorCode: 5,        // 错误码
    apiPath: 6,          // API路径
    region: 7,           // 地区
    productType: 8,      // 产品类型
    channel: 9,          // 渠道
    userType: 10,        // 用户类型
    operation: 11,       // 操作类型
    qci: 12,             // QCI
    arp: 13,             // ARP
    homeProvinceName: 14 // 归属省
  }
  
  return Array.from(keys).sort((a, b) => {
    const orderA = orderMap[a] || 999
    const orderB = orderMap[b] || 999
    return orderA - orderB
  })
})

// 获取异常类型文本
const getAnomalyTypeText = (anomalyType: string) => {
  switch (anomalyType) {
    case 'THRESHOLD_EXCEEDED':
      return '阈值超限'
    case 'PROPORTION_FLUCTUATION':
      return '占比波动'
    case 'NEW_COMBINATION':
      return '新组合'
    default:
      return anomalyType
  }
}

// 格式化维度键名
const formatDimensionKey = (key: string | number) => {
  const keyMap: Record<string, string> = {
    errorCode: '错误码',
    apiPath: 'API路径',
    region: '地区',
    productType: '产品类型',
    channel: '渠道',
    userType: '用户类型',
    operation: '操作类型',
    qci: 'QCI',
    arp: 'ARP',
  }
  return keyMap[key] || key
}

// 根据维度键名设置列宽
const getColumnWidth = (key: string) => {
  switch (key) {
    case 'errorCode':
      return 100
    case 'apiPath':
      return 150
    case 'region':
      return 100
    case 'productType':
      return 100
    case 'channel':
      return 100
    case 'userType':
      return 100
    case 'operation':
      return 100
    case 'qci':
      return 80
    case 'arp':
      return 80
    case 'operType':
      return 100
    case 'orderSource':
      return 120
    case 'respCode':
      return 100
    case 'respDesc':
      return 150
    case 'homeProvinceName':
      return 100
    default:
      return 120
  }
}
</script>

<style scoped>
.anomaly-table {
  padding: 0;
}

.data-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.mt-20 {
  margin-top: 20px;
}

/* 表格单元格内容换行样式 */
.cell-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  padding: 4px 0;
}

/* 确保表格行高适应多行内容 */
:deep(.el-table .el-table__row) {
  min-height: 40px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
  vertical-align: top;
}
</style> 