<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { PccStrategyDetailDTO, getPccStrategyDetail } from '../api/pccStrategy'
import { getProvinceNameByCode } from '../utils/provinceUtil'

// 组件属性
interface Props {
  visible: boolean
  pccStrategyId: number | null
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const strategyInfo = ref<PccStrategyDetailDTO | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
})

// 核心网子类型映射
const coreNetworkSubtypeMap = {
  0: { text: '人网', type: 'primary' },
  1: { text: '物网', type: 'success' },
} as const

// 网络模式映射
const networkModeMap = {
  0: { text: '4G', type: 'primary' },
  1: { text: '5G', type: 'success' },
} as const

// 承载模式映射
const bearerModeMap = {
  0: { text: '专载', type: 'primary' },
  1: { text: '默载', type: 'success' },
  2: { text: 'AM-PCF', type: 'warning' },
} as const

// 速度类型映射
const speedTypeMap = {
  0: { text: '加速', type: 'success' },
  1: { text: '限速', type: 'warning' },
  2: { text: '其他', type: 'info' },
} as const

// 状态映射
const statusMap = {
  0: { text: '启用', type: 'success' },
  '-1': { text: '停用', type: 'danger' },
} as const

// 扩展信息字段映射
const extendInfoFieldMap = {
  bandwidthType: '默载带宽类型',
  uplinkAmbr: '默载最大聚合带宽-上行',
  downlinkAmbr: '默载最大聚合带宽-下行',
  amPcfCarrierWave: 'AM-PCF应用-载波',
  amPcfLogo: 'AM-PCF应用-LOGO',
  amPcfLogoFullName: 'LOGO全名称',
  amPcfLogoShortName: 'LOGO短名称',
} as const

// 获取映射信息的通用函数
const getMapInfo = (value: number | string, map: Record<string, any>) => {
  return map[value] || { text: '未知', type: 'info' }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 格式化归属省列表
const formatProvinceList = (provinceList: number[]) => {
  if (!provinceList || provinceList.length === 0) return '-'
  return provinceList.map((code) => getProvinceNameByCode(code)).join('、')
}

// 格式化扩展信息字段名
const formatExtendInfoField = (fieldName: string) => {
  return (
    extendInfoFieldMap[fieldName as keyof typeof extendInfoFieldMap] ||
    fieldName
  )
}

// 获取PCC策略详情
const fetchStrategyDetail = async (pccStrategyId: number) => {
  if (!pccStrategyId) return

  loading.value = true
  try {
    const response = await getPccStrategyDetail({ pccStrategyId })

    if (response.code === 0 && response.data) {
      strategyInfo.value = response.data
    } else {
      ElMessage.error(response.msg || '获取PCC策略信息失败')
      strategyInfo.value = null
    }
  } catch (error: any) {
    if (!error.isHandled) {
      ElMessage.error('获取PCC策略信息失败：' + (error.message || '未知错误'))
    }
    console.error('获取PCC策略信息失败:', error)
    strategyInfo.value = null
  } finally {
    loading.value = false
  }
}

// 监听策略ID变化
watch(
  () => props.pccStrategyId,
  (newId) => {
    if (newId && props.visible) {
      fetchStrategyDetail(newId)
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.pccStrategyId) {
      fetchStrategyDetail(props.pccStrategyId)
    } else if (!visible) {
      strategyInfo.value = null
    }
  }
)

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="PCC策略信息"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    @close="handleClose"
  >
    <template #header>
      <div class="dialog-header">
        <el-icon class="header-icon"><InfoFilled /></el-icon>
        <span class="header-title">PCC策略信息</span>
        <el-tag
          v-if="strategyInfo"
          type="primary"
          size="small"
          class="strategy-id-tag"
        >
          ID: {{ strategyInfo.pccStrategyId }}
        </el-tag>
      </div>
    </template>

    <div v-loading="loading" class="dialog-content">
      <div v-if="!loading && !strategyInfo" class="empty-state">
        <el-empty description="暂无PCC策略信息" />
      </div>

      <div v-else-if="strategyInfo" class="strategy-info">
        <!-- 基础信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基础信息</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="策略ID" :span="1">
              <el-tag type="primary" size="small">{{
                strategyInfo.pccStrategyId
              }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="策略名称" :span="1">
              <span class="strategy-name">{{
                strategyInfo.pccStrategyName || '-'
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="核心网子类型" :span="1">
              <el-tag
                :type="
                  getMapInfo(
                    strategyInfo.coreNetworkSubtype,
                    coreNetworkSubtypeMap
                  ).type
                "
                size="small"
              >
                {{
                  getMapInfo(
                    strategyInfo.coreNetworkSubtype,
                    coreNetworkSubtypeMap
                  ).text
                }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="网络模式" :span="1">
              <el-tag
                :type="
                  getMapInfo(strategyInfo.networkMode, networkModeMap).type
                "
                size="small"
              >
                {{ getMapInfo(strategyInfo.networkMode, networkModeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="承载模式" :span="1">
              <el-tag
                :type="getMapInfo(strategyInfo.bearerMode, bearerModeMap).type"
                size="small"
              >
                {{ getMapInfo(strategyInfo.bearerMode, bearerModeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="速度类型" :span="1">
              <el-tag
                :type="getMapInfo(strategyInfo.speedType, speedTypeMap).type"
                size="small"
              >
                {{ getMapInfo(strategyInfo.speedType, speedTypeMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="状态" :span="1">
              <el-tag
                :type="getMapInfo(strategyInfo.status, statusMap).type"
                size="small"
              >
                {{ getMapInfo(strategyInfo.status, statusMap).text }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="AFID/Service ID" :span="1">
              <code v-if="strategyInfo.afid" class="afid-value">{{
                strategyInfo.afid
              }}</code>
              <span v-else>-</span>
            </el-descriptions-item>

            <el-descriptions-item label="归属省份" :span="2">
              <span class="province-list">{{
                formatProvinceList(strategyInfo.homeProvinceList)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="QoS等级" :span="1">
              <span class="qos-level">{{ strategyInfo.qosLevel || '-' }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="业务优先级" :span="1">
              <span class="priority-value">{{
                strategyInfo.businessPriority || '-'
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="规则优先级" :span="1">
              <span class="priority-value">{{
                strategyInfo.rulePriority || '-'
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="RG SID" :span="1">
              <code v-if="strategyInfo.rgSid" class="rg-sid-value">{{
                strategyInfo.rgSid
              }}</code>
              <span v-else>-</span>
            </el-descriptions-item>

            <el-descriptions-item label="ARP" :span="1">
              <span class="arp-value">{{ strategyInfo.arp || '-' }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="APN" :span="1">
              <span class="apn-value">{{ strategyInfo.apn || '-' }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="DNN" :span="1">
              <span class="dnn-value">{{ strategyInfo.dnn || '-' }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="配置文件名" :span="1">
              <span class="config-file">{{
                strategyInfo.configFileName || '-'
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间" :span="1">
              <span class="time-value">{{
                formatDate(strategyInfo.createTime)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="更新时间" :span="1">
              <span class="time-value">{{
                formatDate(strategyInfo.updateTime)
              }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 扩展信息 -->
        <el-card
          v-if="
            strategyInfo.extendInfoList &&
            strategyInfo.extendInfoList.length > 0
          "
          class="info-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span class="card-title">扩展信息</span>
            </div>
          </template>

          <el-table :data="strategyInfo.extendInfoList" border size="small">
            <el-table-column prop="fieldName" label="字段名称" min-width="150">
              <template #default="{ row }">
                <span class="field-name">{{
                  formatExtendInfoField(row.fieldName)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fieldValue" label="字段值" min-width="200">
              <template #default="{ row }">
                <span class="field-value">{{ row.fieldValue || '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.strategy-id-tag {
  margin-left: auto;
}

.dialog-content {
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.strategy-info {
  padding: 8px 0;
}

.info-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.strategy-name {
  font-weight: 500;
  color: #303133;
}

.province-list {
  color: #606266;
  line-height: 1.5;
}

.qos-level {
  font-weight: 500;
  color: #409eff;
}

.priority-value {
  font-weight: 500;
  color: #409eff;
}

.afid-value,
.rg-sid-value {
  background-color: #f5f7fa;
  color: #606266;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.arp-value,
.apn-value,
.dnn-value,
.config-file {
  color: #606266;
}

.time-value {
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

:deep(.el-loading-mask) {
  border-radius: 4px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 16px;
}

.field-name {
  font-weight: 500;
  color: #303133;
}

.field-value {
  color: #606266;
  word-break: break-all;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px 0;
}
</style>
