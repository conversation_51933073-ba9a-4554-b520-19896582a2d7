/* Dashboard 页面样式 */

.count-button {
  font-weight: 600;
  font-size: 14px;
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.count-button:hover {
  background-color: var(--el-color-primary-light-9);
  transform: scale(1.05);
}

.dashboard-container {
  padding: 20px;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.show-all-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.export-dropdown {
  margin-left: 8px;
}

.export-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  color: white;
}

.data-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
}

.mt-20 {
  margin-top: 20px;
}

/* 表格样式优化 */
.dashboard-container :deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

.dashboard-container :deep(.el-table th) {
  background: #f8fafc !important;
  color: #1f2937 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #e5e7eb;
}

.dashboard-container :deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

.dashboard-container :deep(.el-table tr:hover td) {
  background-color: #f0f9ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .data-card {
    margin-bottom: 16px;
  }
}
