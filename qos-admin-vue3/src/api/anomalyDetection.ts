import { AnomalyResult } from '../types/anomalyDetection'
import request from '../utils/request'

/**
 * 网关异常统计检测
 */
export const detectHttpFailAnomalies = (): Promise<AnomalyResult[]> => {
  return request.post('/admin/httpFailAnomalies/1.0').then((res) => res.data)
}

/**
 * 订购退订统计检测
 */
export const detectSubscriptionAnomalies = (): Promise<AnomalyResult[]> => {
  return request.post('/admin/subscriptionAnomalies/1.0').then((res) => res.data)
}

/**
 * 默载调用统计检测
 */
export const detectDefbearerAnomalies = (): Promise<AnomalyResult[]> => {
  return request.post('/admin/defbearerAnomalies/1.0').then((res) => res.data)
}

/**
 * 专载调用统计检测
 */
export const detectDedbearerAnomalies = (): Promise<AnomalyResult[]> => {
  return request.post('/admin/dedbearerAnomalies/1.0').then((res) => res.data)
} 