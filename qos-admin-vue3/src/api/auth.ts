import {
  LoginResult,
  LoginUserInfo,
  FirstStepParams,
  FirstStepResult,
  SecondStepParams,
  SendSmsParams
} from '../types/auth'
import request from '../utils/request'
import { currentConfig } from '../config'

/**
 * 获取图形验证码URL
 */
export const getCaptcha = (sessionId: string): string => {
  return `${currentConfig.apiBaseUrl}/admin/auth/captcha/image?sessionId=${sessionId}&t=${Date.now()}`
}

/**
 * 第一步：用户名密码验证
 */
export const firstStepLogin = (params: FirstStepParams): Promise<FirstStepResult> => {
  return request.post('/admin/auth/login', params).then((res) => res.data)
}

/**
 * 发送短信验证码
 */
export const sendSmsCode = (params: SendSmsParams): Promise<void> => {
  return request.post(`/admin/auth/sms/send?userName=${params.userName}`).then((res) => res.data)
}

/**
 * 第二步：短信验证码验证
 */
export const secondStepLogin = (params: SecondStepParams): Promise<LoginResult> => {
  return request.post('/admin/auth/sms/verify', params).then((res) => res.data)
}



/**
 * 用户登出
 */
export const logout = (): Promise<void> => {
  return request.post('/admin/auth/logout').then((res) => res.data)
}

/**
 * 获取用户信息
 */
export const getUserInfo = (): Promise<LoginUserInfo> => {
  return request.get('/qos/admin/auth/user-info').then((res) => res.data)
}
