import request from '../utils/request'

/**
 * 查询PCC策略详情请求参数
 */
export interface QueryPccStrategyReq {
  pccStrategyId: number
}

/**
 * 扩展信息数据传输对象
 */
export interface ExtendInfoDTO {
  fieldName: string
  fieldValue: string
}

/**
 * PCC策略基础信息
 */
export interface PccStrategyDTO {
  pccStrategyId: number
  pccStrategyName: string
  coreNetworkSubtype: number
  networkMode: number
  bearerMode: number
  speedType: number
  afid: string
  homeProvinceList: number[]
  qosLevel: string
  businessPriority: string
  rulePriority: string
  status: number
  rgSid: string
  arp: string
  createTime: string
  updateTime: string
}

/**
 * PCC策略详情数据传输对象
 */
export interface PccStrategyDetailDTO extends PccStrategyDTO {
  apn: string
  dnn: string
  configFileName: string
  configFilePath: string
  extendInfoList: ExtendInfoDTO[]
}

/**
 * API响应格式
 */
export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
  traceId?: string
}

/**
 * 获取PCC策略详情
 * @param params 请求参数
 */
export const getPccStrategyDetail = (
  params: QueryPccStrategyReq
): Promise<ApiResponse<PccStrategyDetailDTO>> => {
  return request.post('/manage/pccStrategy/getDetailInfo/1.0', params)
}
