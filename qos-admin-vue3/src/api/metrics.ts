import request from '../utils/request'
import {
  ApiResponse,
  BusinessMetricsByServiceReq,
  BusinessMetricsByServiceDTO,
  BusinessMetricsReq,
  ProcessStageDataReq,
  ProcessStageDayDataPO,
  ReturnBusinessMetricsDTO,
} from '../types/metrics'

/**
 * 获取业务指标数据
 * @param params 请求参数
 */
export const getBusinessMetrics = (
  params: BusinessMetricsReq
): Promise<ApiResponse<ReturnBusinessMetricsDTO>> => {
  return request.post('/admin/business-metrics/getMetrics/1.0', params)
}

/**
 * 获取流程分阶段数据
 * @param params 请求参数
 */
export const getProcessStageData = (
  params: ProcessStageDataReq
): Promise<ApiResponse<ProcessStageDayDataPO[]>> => {
  return request.post('/admin/business-metrics/process/stage-data/1.0', params)
}

/**
 * 按服务分类获取指标数据
 * @param params 请求参数
 */
export const getMetricsByService = (
  params: BusinessMetricsByServiceReq
): Promise<ApiResponse<BusinessMetricsByServiceDTO>> => {
  return request.post('/admin/business-metrics/metricsByService/1.0', params)
}
