import request from '../utils/request'

/**
 * 通信服务列表查询请求
 */
export interface ListCmServiceReq {
  // 根据需要可以添加查询参数，例如按名称搜索
  name?: string
}

/**
 * 通信服务列表数据传输对象
 */
export interface CmServiceListDataDTO {
  cmServiceId: string
  cmServiceName: string
  bearerMode: number // 0: 专载, 1: 默载, 2: AM-PCF
}

/**
 * 查询通信服务详情请求参数
 */
export interface QueryCmServiceReq {
  cmServiceId: string
}

/**
 * 通信服务基础信息
 */
export interface CmServiceBasicDTO {
  cmServiceId: string
  cmServiceName: string
  serviceType: number
  serviceMode: number
  coreNetworkSubtype: number
  networkMode: number
  bearerMode: number
  speedType: number
  ranNetworkMode: number
  status: number
  homeProvinceList: number[]
  pccStrategy4g: number
  pccStrategy5g: number
  createTime: string
  updateTime: string
}

/**
 * 通信服务速率配置
 */
export interface CmServiceBitrateConfigDTO {
  uplinkMbr4g: number
  downlinkMbr4g: number
  uplinkGbr4g: number
  downlinkGbr4g: number
  uplinkMbr5g: number
  downlinkMbr5g: number
  uplinkGbr5g: number
  downlinkGbr5g: number
}

/**
 * 通信服务检查配置
 */
export interface CmServiceCheckConfigDTO {
  checkKey: string
  checkValue: string
  status: number
}

/**
 * 通信服务网元配置
 */
export interface CmServiceNeConfigDTO {
  homeProvince: number
  aacId: string
  nefId: string
}

/**
 * 通信服务详情数据传输对象
 */
export interface CmServiceDetailDTO extends CmServiceBasicDTO {
  comment: string
  attachmentFileName: string
  attachmentFilePath: string
  bitrateConfig: CmServiceBitrateConfigDTO
  checkConfigList: CmServiceCheckConfigDTO[]
  neConfigList: CmServiceNeConfigDTO[]
}

/**
 * API响应格式
 */
export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
  traceId?: string
}

/**
 * 获取通信服务列表
 * @param params
 */
export function listCmServices(params: ListCmServiceReq) {
  return request<CmServiceListDataDTO[]>({
    url: '/manage/cmService/list/1.0',
    method: 'post',
    data: params,
  })
}

/**
 * 获取通信服务详情
 * @param params 请求参数
 */
export const getCmServiceDetail = (
  params: QueryCmServiceReq
): Promise<ApiResponse<CmServiceDetailDTO>> => {
  return request.post('/manage/cmService/getDetailInfo/1.0', params)
}
