import request from '../utils/request'

/**
 * 产品基础信息数据传输对象
 */
export interface ProductBaseInfoDTO {
  qosProductId: number
  qosProductName: string
  businessOwnership: string
  existPlatProduct: number
  status: number
  orderPriority: number
  conflictCheckFlag: number
  updateTime: string
  createTime: string
  qosProductDesc: string
}

/**
 * 查询产品基础信息请求参数
 */
export interface ProductIdsReq {
  qosProductIdList: number[]
}

/**
 * API响应格式
 */
export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
  traceId?: string
}

/**
 * 根据产品ID列表查询产品基础信息
 * @param params 请求参数
 */
export const queryProductBaseInfoByIds = (
  params: ProductIdsReq
): Promise<ApiResponse<ProductBaseInfoDTO[]>> => {
  return request.post('/manage/product/queryProductBaseInfoByIds/1.0', params)
}
