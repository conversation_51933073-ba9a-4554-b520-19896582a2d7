import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { menuConfig } from '../config/menu'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/Login/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/',
    redirect: '/home/<USER>',
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/home/<USER>',
    name: 'workplace',
    component: () => import('../views/Home/Workplace.vue'),
    meta: {
      title: '工作台',
      requiresAuth: true,
    },
  },
  {
    path: '/home/<USER>',
    name: 'dashboard',
    component: () => import('../views/Home/Dashboard.vue'),
    meta: {
      title: '分析页',
      requiresAuth: true,
    },
  },
  {
    path: '/home/<USER>',
    name: 'businessMetrics',
    component: () => import('../views/Home/BusinessMetrics.vue'),
    meta: {
      title: '业务指标',
      requiresAuth: true,
    },
  },
  {
    path: '/user/userinfo',
    name: 'userinfo',
    component: () => import('../views/User/UserInfo.vue'),
    meta: {
      title: '用户信息',
      requiresAuth: true,
    },
  },
  {
    path: '/user/invoke',
    name: 'invoke',
    component: () => import('../views/User/UserInvoke.vue'),
    meta: {
      title: '订单记录',
      requiresAuth: true,
    },
  },
  {
    path: '/user/flow',
    name: 'flow',
    component: () => import('../views/User/FlowRecord.vue'),
    meta: {
      title: '流水记录',
      requiresAuth: true,
    },
  },
  {
    path: '/base/mock',
    name: 'mock',
    component: () => import('../views/Base/Mock.vue'),
    meta: {
      title: '应用模拟',
      requiresAuth: true,
    },
  },
  {
    path: '/403',
    name: 'forbidden',
    component: () => import('../views/Error/Forbidden.vue'),
    meta: {
      title: '无权限',
      requiresAuth: true,
    },
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

// 路由和权限的映射
const pathPermissionMap: Record<string, string> = {}
menuConfig.forEach((group) => {
  group.children.forEach((item) => {
    pathPermissionMap[item.path] = item.permission
  })
})

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 动态编排底座管理后台`
    : '动态编排底座管理后台'

  // 仅在开发环境显示路由守卫日志
  if (import.meta.env.DEV) {
    console.log('路由:', to.path, to.meta.requiresAuth ? '需要认证' : '公开访问')
  }

  // 检查该路由是否需要登录权限
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    const token = localStorage.getItem('token')
    const userInfoStr = localStorage.getItem('userInfo')
    let userInfo = null

    try {
      userInfo = userInfoStr ? JSON.parse(userInfoStr) : null
    } catch (err) {
      console.error('解析userInfo失败:', err)
      localStorage.removeItem('userInfo')
    }

    // 同时检查token和userInfo
    if (!token || !userInfo) {
      if (import.meta.env.DEV) {
        console.log('认证失败，重定向到登录页')
      }
      // 清理不完整的登录状态
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      next({
        path: '/login',
        query: { redirect: to.fullPath }, // 将目标路由的完整路径放到查询参数中
      })
      return
    }
    // 权限校验
    const requiredPermission = pathPermissionMap[to.path]
    if (!requiredPermission) {
      next() // 没有配置权限的路由直接放行
      return
    }
    const permissions = userInfo.permissions || []
    if (permissions.includes(requiredPermission)) {
      next() // 已登录，放行
    } else {
      if (import.meta.env.DEV) {
        console.log('权限不足，访问被拒绝:', to.path)
      }
      next('/403')
    }
  } else {
    // 如果是访问登录页并且已经登录，重定向到首页
    if (to.path === '/login' && localStorage.getItem('token')) {
      if (import.meta.env.DEV) {
        console.log('已登录状态访问登录页，重定向到首页')
      }
      next('/')
    } else {
      next() // 不需要登录权限的页面，直接放行
    }
  }
})

export default router
