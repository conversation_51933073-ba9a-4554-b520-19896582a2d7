import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 引入 Element Plus 中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style.css'
import './utils/request' // 引入请求拦截器

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, _instance, info) => {
  console.error('全局错误:', err)
  console.log('错误信息:', info)

  // 过滤掉FrameDoesNotExistError错误，这些通常与浏览器扩展有关，不影响应用本身
  if (
    err instanceof Error &&
    (err.message.includes('FrameDoesNotExistError') ||
      err.message.includes('message port closed'))
  ) {
    console.log('忽略浏览器扩展相关错误')
    return
  }
}

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  // 过滤掉浏览器扩展相关错误
  if (
    event.reason &&
    typeof event.reason.message === 'string' &&
    (event.reason.message.includes('FrameDoesNotExistError') ||
      event.reason.message.includes('message port closed'))
  ) {
    event.preventDefault() // 阻止默认处理
    console.log('忽略浏览器扩展相关的Promise错误')
    return
  }

  console.error('未捕获的Promise错误:', event.reason)
})

// 使用插件
app.use(ElementPlus, { locale: zhCn })
app.use(router)

// 检查初始登录状态（仅开发环境显示）
if (import.meta.env.DEV) {
  const token = localStorage.getItem('token')
  console.log('应用启动，token状态:', token ? '已登录' : '未登录')
}

// 确保路由准备就绪后再挂载应用
router
  .isReady()
  .then(() => {
    // 挂载应用
    app.mount('#app')
    if (import.meta.env.DEV) {
      console.log('应用已挂载')
    }
  })
  .catch((err) => {
    console.error('路由初始化失败:', err)
  })
