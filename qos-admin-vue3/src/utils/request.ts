import axios, {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios'
import { ElMessage } from 'element-plus'
import { currentConfig } from '../config'

// 退出登录状态标记（与useAuth中的保持同步）
let isLoggingOut = false

// 导出设置退出登录状态的方法
export const setLoggingOut = (status: boolean) => {
  isLoggingOut = status
}

// 错误消息去重机制
let lastErrorMessage = ''
let lastErrorTime = 0
const ERROR_DEBOUNCE_TIME = 1000 // 1秒内相同错误只显示一次

// 显示错误消息（带去重）
function showErrorMessage(message: string) {
  const now = Date.now()
  if (
    message === lastErrorMessage &&
    now - lastErrorTime < ERROR_DEBOUNCE_TIME
  ) {
    return // 跳过重复错误
  }
  lastErrorMessage = message
  lastErrorTime = now
  ElMessage.error(message)
}

// 创建Axios实例
const service: AxiosInstance = axios.create({
  baseURL: currentConfig.apiBaseUrl,
  timeout: 10000,
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 如果正在退出登录，静默取消所有非登录相关的请求
    if (isLoggingOut && !config.url?.includes('/login')) {
      const cancelError = new Error('正在退出登录，请求已取消')
      // 标记为已处理的错误，避免显示给用户
      ;(cancelError as any).isHandled = true
      return Promise.reject(cancelError)
    }

    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    if (res.code !== 0) {
      const errorMessage = res.msg || '请求失败'
      showErrorMessage(errorMessage)

      // 判断是否为认证相关错误
      if (res.code === 100401) {
        // 立即清除登录状态
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')

        // 触发页面完全刷新，确保Vue状态重置
        window.location.reload()
        return Promise.reject(new Error('认证失败，请重新登录'))
      }

      // 创建一个带有特殊标记的错误对象，表示错误已被处理
      const handledError = new Error(errorMessage)
      ;(handledError as any).isHandled = true
      ;(handledError as any).code = res.code // 将错误码直接附加到错误对象上
      return Promise.reject(handledError)
    }

    return res
  },
  (error) => {
    // 如果错误已被标记为已处理，直接返回不显示消息
    if ((error as any).isHandled) {
      return Promise.reject(error)
    }

    // 处理HTTP错误
    let message = '网络异常'

    if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.response?.status) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = `请求失败 (${error.response.status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.code === 'ERR_NETWORK') {
      message = '网络连接失败'
    } else if (error.message) {
      message = error.message
    }

    showErrorMessage(message)

    // 处理401未授权错误
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 触发页面完全刷新，确保Vue状态重置
      window.location.reload()
      return Promise.reject(error)
    }

    // 标记错误已被处理
    ;(error as any).isHandled = true
    return Promise.reject(error)
  }
)

export default service
