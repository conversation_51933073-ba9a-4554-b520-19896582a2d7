// 省份代码枚举
const ProvinceCodeEnum = [
  { code2: 9, desc: '全国' },
  { code2: 11, desc: '北京' },
  { code2: 13, desc: '天津' },
  { code2: 18, desc: '河北' },
  { code2: 19, desc: '山西' },
  { code2: 10, desc: '内蒙' },
  { code2: 91, desc: '辽宁' },
  { code2: 90, desc: '吉林' },
  { code2: 97, desc: '黑龙江' },
  { code2: 31, desc: '上海' },
  { code2: 34, desc: '江苏' },
  { code2: 36, desc: '浙江' },
  { code2: 30, desc: '安徽' },
  { code2: 38, desc: '福建' },
  { code2: 75, desc: '江西' },
  { code2: 17, desc: '山东' },
  { code2: 76, desc: '河南' },
  { code2: 71, desc: '湖北' },
  { code2: 74, desc: '湖南' },
  { code2: 51, desc: '广东' },
  { code2: 59, desc: '广西' },
  { code2: 50, desc: '海南' },
  { code2: 83, desc: '重庆' },
  { code2: 81, desc: '四川' },
  { code2: 85, desc: '贵州' },
  { code2: 86, desc: '云南' },
  { code2: 79, desc: '西藏' },
  { code2: 84, desc: '陕西' },
  { code2: 87, desc: '甘肃' },
  { code2: 70, desc: '青海' },
  { code2: 88, desc: '宁夏' },
  { code2: 89, desc: '新疆' },
]

/**
 * 根据省份代码获取省份名称
 * @param code 省份代码 (code2)
 * @returns 省份名称，未找到则返回"-"
 */
export const getProvinceNameByCode = (
  code: number | null | undefined
): string => {
  if (code === null || code === undefined) {
    return '-'
  }

  const province = ProvinceCodeEnum.find((item) => item.code2 === code)
  return province ? province.desc : '-'
}
