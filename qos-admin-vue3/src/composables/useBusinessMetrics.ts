import { reactive, ref } from 'vue'
import {
  getBusinessMetrics,
  getMetricsByService,
  getProcessStageData,
} from '../api/metrics'
import { listCmServices } from '../api/cmService'
import type { CmServiceListDataDTO } from '../api/cmService'
import {
  ReturnBusinessMetricsDTO,
  ProcessStageDayDataPO,
  BusinessMetricsByServiceDTO,
  BaseMetricsReq,
} from '../types/metrics'

export function useBusinessMetrics() {
  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref<Error | null>(null)

  // 筛选条件
  const queryParams = reactive<BaseMetricsReq>({
    startTime: '',
    endTime: '',
    communicationServices: [],
  })

  // API返回的原始数据
  const metricsData = ref<ReturnBusinessMetricsDTO | null>(null)
  const stageData = ref<{
    ded: ProcessStageDayDataPO[]
    def: ProcessStageDayDataPO[]
  } | null>(null)
  const serviceData = ref<BusinessMetricsByServiceDTO | null>(null)
  const communicationServiceList = ref<CmServiceListDataDTO[]>([])
  const servicesFetched = ref(false)

  // 获取通信服务列表
  const fetchCommunicationServices = async () => {
    if (servicesFetched.value) {
      return
    }
    try {
      const res = await listCmServices({})
      if (res.data) {
        communicationServiceList.value = res.data
        servicesFetched.value = true
      }
    } catch (err) {
      console.error('获取通信服务列表失败:', err)
      communicationServiceList.value = []
    }
  }

  // 获取所有数据的核心方法
  const fetchAllData = async () => {
    if (!queryParams.startTime || !queryParams.endTime) {
      // 在实际应用中可能会用 ElMessage 提示用户
      console.error('请选择起始和结束时间')
      return
    }

    loading.value = true
    error.value = null

    try {
      // 并行获取所有数据
      const [
        metricsRes,
        dedStageRes,
        defStageRes,
        dedServiceRes,
        defServiceRes,
      ] = await Promise.all([
        getBusinessMetrics({ ...queryParams, selectType: 'all' }),
        getProcessStageData({ ...queryParams, selectType: 'dedbearer' }),
        getProcessStageData({ ...queryParams, selectType: 'defbearer' }),
        getMetricsByService({ ...queryParams, selectType: 'dedbearer' }),
        getMetricsByService({ ...queryParams, selectType: 'defbearer' }),
      ])

      metricsData.value = metricsRes.data

      // 分别存储专载和默载的流程分阶段数据
      stageData.value = {
        ded: dedStageRes.data || [],
        def: defStageRes.data || [],
      }

      // 合并专载和默载的服务分类指标
      const dedServiceData = dedServiceRes.data
      const defServiceData = defServiceRes.data
      serviceData.value = {
        dedbearerCmServiceList: dedServiceData?.dedbearerCmServiceList || [],
        defbearerCmServiceList: defServiceData?.defbearerCmServiceList || [],
      }
    } catch (err: any) {
      error.value = err
      console.error('数据加载失败:', err)
      // 可以在这里使用 ElMessage 显示错误
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    queryParams,
    metricsData,
    stageData,
    serviceData,
    communicationServiceList,
    servicesFetched,
    fetchAllData,
    fetchCommunicationServices,
  }
}
