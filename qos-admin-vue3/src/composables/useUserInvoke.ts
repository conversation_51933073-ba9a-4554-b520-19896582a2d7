import { computed, reactive, ref } from 'vue'
import { OrderRecord, OrderRecords, ProductSubscribeInfos } from '../types'
import request from '../utils/request'

export function useUserInvoke() {
  // --- State ---
  const productSubscribeInfoTable: ProductSubscribeInfos = reactive([])
  const dedOrderTable: OrderRecords = reactive([])
  const defOrderTable: OrderRecords = reactive([])
  const allOrderTable: OrderRecords = reactive([])
  const dedHistoryOrderTable: OrderRecords = reactive([])
  const defHistoryOrderTable: OrderRecords = reactive([])
  const allHistoryOrderTable: OrderRecords = reactive([])

  const loading = ref(false)
  const orderLoading = ref(false)
  const historyOrderLoading = ref(false)

  const historyCurrentPage = ref(1)
  const historyPageSize = ref(10)
  const currentPhoneNumber = ref('')

  // --- Computed ---
  const pagedHistoryOrderTable = computed(() => {
    const start = (historyCurrentPage.value - 1) * historyPageSize.value
    const end = start + historyPageSize.value
    return allHistoryOrderTable.slice(start, end)
  })

  // --- Methods ---
  function fetchCurrentCallRecords(msisdn: string) {
    orderLoading.value = true
    Promise.all([
      request
        .post('/manage/user/single/dedbearer/queryOrders/1.0', { msisdn })
        .catch((error) => {
          if (error.code === 309203) return { data: [] }
          throw error
        }),
      request
        .post('/manage/user/single/defbearer/queryOrders/1.0', { msisdn })
        .catch((error) => {
          if (error.code === 201005) return { data: [] }
          throw error
        }),
    ])
      .then(([dedRes, defRes]) => {
        dedOrderTable.length = 0
        if (dedRes.data) {
          const dedOrders = dedRes.data.map((order: OrderRecord) => ({
            ...order,
            orderType: '专载',
          }))
          dedOrderTable.push(...dedOrders)
        }

        defOrderTable.length = 0
        if (defRes.data) {
          const defOrders = defRes.data.map((order: OrderRecord) => ({
            ...order,
            orderType: '默载',
          }))
          defOrderTable.push(...defOrders)
        }

        allOrderTable.length = 0
        allOrderTable.push(...dedOrderTable, ...defOrderTable)
        allOrderTable.sort(
          (a, b) =>
            new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
        )
      })
      .catch((error) => {
        if (error.response?.data?.code === 309203) {
          dedOrderTable.length = 0
          defOrderTable.length = 0
          allOrderTable.length = 0
        } else {
          console.error('获取当前调用记录失败:', error)
        }
      })
      .finally(() => {
        orderLoading.value = false
      })
  }

  function fetchHistoryCallRecords(
    msisdn: string,
    dateRange?: [string, string] | []
  ) {
    historyOrderLoading.value = true
    const dedParams: {
      msisdn: string
      startTimeBegin?: string
      startTimeEnd?: string
    } = { msisdn }
    const defParams: {
      msisdn: string
      startTimeBegin?: string
      startTimeEnd?: string
    } = { msisdn }

    if (dateRange && dateRange.length === 2) {
      const startTimeBegin = `${dateRange[0]} 00:00:00`
      const startTimeEnd = `${dateRange[1]} 23:59:59`
      dedParams.startTimeBegin = startTimeBegin
      dedParams.startTimeEnd = startTimeEnd
      defParams.startTimeBegin = startTimeBegin
      defParams.startTimeEnd = startTimeEnd
    }

    Promise.all([
      request.post(
        '/manage/user/single/dedbearer/queryHistoryOrders/1.0',
        dedParams
      ),
      request.post(
        '/manage/user/single/defbearer/queryHistoryOrders/1.0',
        defParams
      ),
    ])
      .then(([dedRes, defRes]) => {
        dedHistoryOrderTable.length = 0
        if (dedRes.data) {
          const dedOrders = dedRes.data.map((order: OrderRecord) => ({
            ...order,
            orderType: '专载',
          }))
          dedHistoryOrderTable.push(...dedOrders)
        }

        defHistoryOrderTable.length = 0
        if (defRes.data) {
          const defOrders = defRes.data.map((order: OrderRecord) => ({
            ...order,
            orderType: '默载',
          }))
          defHistoryOrderTable.push(...defOrders)
        }

        allHistoryOrderTable.length = 0
        allHistoryOrderTable.push(
          ...dedHistoryOrderTable,
          ...defHistoryOrderTable
        )
        allHistoryOrderTable.sort(
          (a, b) =>
            new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
        )
      })
      .catch((error) => {
        console.error('获取历史调用记录失败:', error)
      })
      .finally(() => {
        historyOrderLoading.value = false
      })
  }

  function onSubmit({
    phoneNumber,
    dateRange,
  }: {
    phoneNumber: number
    dateRange: [string, string] | []
  }) {
    loading.value = true
    currentPhoneNumber.value = String(phoneNumber)
    const params = { userId: phoneNumber }

    request
      .get('/admin/user-subscribe-record/1.0', { params })
      .then((res) => {
        productSubscribeInfoTable.length = 0
        productSubscribeInfoTable.push(...res.data)
      })
      .catch((error) => {
        console.error('获取产品订购记录失败:', error)
      })
      .finally(() => {
        loading.value = false
      })

    fetchCurrentCallRecords(String(phoneNumber))
    fetchHistoryCallRecords(String(phoneNumber), dateRange)
  }

  // --- Handlers ---
  const handleHistoryCurrentChange = (val: number) => {
    historyCurrentPage.value = val
  }

  const handleHistorySizeChange = (val: number) => {
    historyPageSize.value = val
    historyCurrentPage.value = 1
  }

  function goToFlowRecord(orderId: string, startTime: string) {
    const queryParams = new URLSearchParams({
      phoneNumber: currentPhoneNumber.value,
      orderId,
      startTime,
    })
    const baseUrl = window.location.origin + window.location.pathname + '#'
    const url = `${baseUrl}/user/flow?${queryParams.toString()}`
    window.open(url, '_blank', 'noopener')
  }

  // --- Helpers ---
  function getStatusType(status: string | number): string {
    const statusStr = String(status).toLowerCase()
    if (statusStr === '0' || statusStr === 'active' || statusStr === '生效') {
      return 'success'
    } else if (
      statusStr === '1' ||
      statusStr === 'inactive' ||
      statusStr === '失效'
    ) {
      return 'danger'
    } else if (
      statusStr === '2' ||
      statusStr === 'pending' ||
      statusStr === '待生效'
    ) {
      return 'warning'
    }
    return 'info'
  }

  function getStatusText(status: string | number): string {
    const statusStr = String(status)
    if (
      statusStr.includes('效') ||
      statusStr.includes('活') ||
      statusStr.includes('待')
    ) {
      return statusStr
    }
    switch (statusStr) {
      case '1':
        return '不可用'
      case '0':
        return '可用'
      default:
        return statusStr
    }
  }

  function getOrderStatusType(status: number): string {
    switch (status) {
      case 0:
        return 'success'
      case -1:
        return 'danger'
      case -2:
        return 'warning'
      case 1:
        return 'info'
      default:
        return 'info'
    }
  }

  function getOrderStatusText(status: number): string {
    switch (status) {
      case 0:
        return '已生效'
      case -1:
        return '已终止'
      case -2:
        return '已中断'
      case 1:
        return '重建中'
      default:
        return '未知状态'
    }
  }

  function getBearerModeText(bearerMode: number): string {
    switch (bearerMode) {
      case 0:
        return '专载'
      case 1:
        return '默载'
      case 2:
        return 'AM-PCF'
      default:
        return '未知'
    }
  }

  function getSpeedTypeText(speedType: number): string {
    switch (speedType) {
      case 0:
        return '加速'
      case 1:
        return '限速'
      case 2:
        return '其他'
      default:
        return '未知'
    }
  }

  function formatDuration(seconds: number): string {
    if (!seconds && seconds !== 0) return '-'
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    let result = ''
    if (hours > 0) result += `${hours}小时`
    if (minutes > 0 || hours > 0) result += `${minutes}分钟`
    result += `${remainingSeconds}秒`
    return result
  }

  return {
    // State
    productSubscribeInfoTable,
    allOrderTable,
    allHistoryOrderTable,
    pagedHistoryOrderTable,
    dedOrderTable,
    defOrderTable,
    dedHistoryOrderTable,
    defHistoryOrderTable,
    loading,
    orderLoading,
    historyOrderLoading,
    historyCurrentPage,
    historyPageSize,

    // Methods & Handlers
    onSubmit,
    handleHistoryCurrentChange,
    handleHistorySizeChange,
    goToFlowRecord,

    // Helpers
    getStatusType,
    getStatusText,
    getOrderStatusType,
    getOrderStatusText,
    getBearerModeText,
    getSpeedTypeText,
    formatDuration,
  }
}
