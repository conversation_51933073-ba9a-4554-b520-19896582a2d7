import {ElMessage} from 'element-plus'
import {computed, onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {firstStepLogin, secondStepLogin,} from '../api/auth'
import {FirstStepParams, FirstStepResult, LoginResult, LoginUserInfo, SecondStepParams} from '../types/auth'
import { setLoggingOut } from '../utils/request'

const token = ref<string | null>(null)
const userInfo = ref<LoginUserInfo | null>(null)
const loading = ref(false)
const error = ref<Error | null>(null)

let isInitialized = false
let cachedToken: string | null = null
let cachedUserInfo: LoginUserInfo | null = null
let isLoggingOut = false // 添加退出登录状态标记

const initializeAuth = () => {
  if (!isInitialized) {
    cachedToken = localStorage.getItem('token')
    const userInfoStr = localStorage.getItem('userInfo')
    try {
      cachedUserInfo = userInfoStr ? JSON.parse(userInfoStr) : null
    } catch {
      cachedUserInfo = null
      localStorage.removeItem('userInfo')
    }
    token.value = cachedToken
    userInfo.value = cachedUserInfo
    isInitialized = true
  }
}

initializeAuth()

export default function useAuth() {
  const router = useRouter()

  // 组件挂载时检查登录状态完整性
  onMounted(() => {
    // 如果正在退出登录，跳过检查
    if (isLoggingOut) {
      return
    }

    // 避免重复日志，只在第一次初始化时打印
    if (import.meta.env.DEV && !isInitialized) {
      console.log('useAuth初始化，用户:', userInfo.value?.userName || '未登录')
      isInitialized = true
    }

    // 检查登录状态完整性
    const hasToken = !!token.value
    const hasUserInfo = !!userInfo.value

    // 如果token和userInfo任何一个缺失，清理状态并跳转登录
    if (!hasToken || !hasUserInfo) {
      if (import.meta.env.DEV) {
        console.log('登录状态不完整，清理并跳转登录页')
      }

      // 清理不完整的状态
      token.value = null
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 跳转登录页
      router.push('/login')
      return
    }
  })

  // 计算属性：是否已登录（同时检查token和userInfo）
  const isLoggedIn = computed(() => {
    const hasToken = !!token.value
    const hasUserInfo = !!userInfo.value
    return hasToken && hasUserInfo
  })

  // 登录方法（支持两步验证）
  const login = async (params: FirstStepParams | SecondStepParams): Promise<FirstStepResult | LoginResult> => {
    error.value = null
    loading.value = true

    try {
      // 判断是第一步还是第二步
      if ('captcha' in params) {
        // 第一步：用户名密码验证
        return await firstStepLogin(params as FirstStepParams)
      } else {
        // 第二步：短信验证码验证
        const result = await secondStepLogin(params as SecondStepParams)

        // 检查token是否为空
        if (!result.token || result.token.trim() === '') {
          ElMessage.error('返回Token为空，请联系管理员')
          return Promise.reject(new Error('返回Token为空，请联系管理员'))
        }

        // 保存token和用户信息
        token.value = result.token
        userInfo.value = result.userInfo

        cachedToken = result.token
        cachedUserInfo = result.userInfo
        localStorage.setItem('token', result.token)
        localStorage.setItem('userInfo', JSON.stringify(result.userInfo))
        ElMessage.success('登录成功')

        // 重定向
        const redirect = router.currentRoute.value.query.redirect as string
        router.replace(redirect || '/')

        return result
      }
    } catch (err: any) {
      error.value = err
      if (!err.isHandled) {
        ElMessage.error(err.message || '登录失败，请稍后再试')
      }
      return Promise.reject(err)
    } finally {
      loading.value = false
    }
  }

  // 登出方法
  const logout = () => {
    // 设置退出登录状态，避免其他组件重复检查和新请求
    isLoggingOut = true
    setLoggingOut(true)

    // 清除本地状态
    token.value = null
    userInfo.value = null

    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    // 立即显示退出消息
    ElMessage.success('已安全退出系统')

    // 重定向到登录页
    router.push('/login')

    // 延迟重置状态标记，确保路由跳转完成
    setTimeout(() => {
      isLoggingOut = false
      setLoggingOut(false)
    }, 200)
  }







  return {
    token,
    userInfo,
    loading,
    error,
    isLoggedIn,
    login,
    logout,
  }
}
