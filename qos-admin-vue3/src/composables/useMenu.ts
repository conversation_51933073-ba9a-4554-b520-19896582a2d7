import { computed } from 'vue'
import { menuConfig } from '../config/menu'
import useAuth from './useAuth'

export default function useMenu() {
  const { userInfo } = useAuth()
  // 过滤菜单
  const filteredMenu = computed(() => {
    const permissions = userInfo.value?.permissions || []
    return menuConfig
      .map((group) => ({
        ...group,
        children: group.children.filter((item) =>
          permissions.includes(item.permission)
        ),
      }))
      .filter((group) => group.children.length > 0)
  })
  return { filteredMenu }
}
