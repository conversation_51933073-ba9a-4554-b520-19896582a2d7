export interface FirstStepParams {
  userName: string
  password: string
  captcha: string
  sessionId: string
}

export interface FirstStepResult {
  maskedPhone: string
}

export interface SecondStepParams {
  userName: string
  smsCode: string
  remember?: boolean
}

export interface SendSmsParams {
  userName: string
}


export interface LoginUserInfo {
  userName: string
  realName: string
  phone: string
  email: string
  roles: string[]
  permissions: string[]
}

export interface LoginResult {
  token: string
  userInfo: LoginUserInfo
}

export interface AuthState {
  token: string | null
  userInfo: LoginUserInfo | null
  isLoggedIn: boolean
}

export type LoginParams = FirstStepParams | SecondStepParams
