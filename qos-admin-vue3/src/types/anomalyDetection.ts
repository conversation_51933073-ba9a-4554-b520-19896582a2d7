/**
 * 异常检测结果
 */
export interface AnomalyResult {
  /** 异常类型 */
  anomalyType: 'NEW_COMBINATION' | 'THRESHOLD_EXCEEDED' | 'PROPORTION_FLUCTUATION'
  /** 异常类型描述 */
  anomalyTypeDesc: string
  /** 规则ID */
  ruleId: string
  /** 规则名称 */
  ruleName: string
  /** 异常数据的维度组合 */
  dimensions: Record<string, any>
  /** 当前计数值 */
  currentCount: number | string
  /** 当前占比（用于占比波动检测） */
  currentProportion?: number
  /** 历史基线值（用于占比波动检测） */
  historicalProportion?: number
  /** 波动值（用于占比波动检测） */
  fluctuation?: number
  /** 触发阈值 */
  thresholdValue: any
  /** 异常描述 */
  description: string
  /** 检测时间 */
  detectionTime: string
  
  // 前端展示用的字段（兼容旧版本）
  /** 异常ID */
  anomalyId?: string
  /** 业务类型 */
  businessType?: string
  /** 异常严重程度 */
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  /** 匹配条件 */
  matchConditions?: Record<string, any>
  /** 当前值 */
  currentValue?: number
  /** 阈值 */
  threshold?: number
  /** 历史基线值 */
  baselineValue?: number
  /** 建议措施 */
  recommendations?: string[]
}

/**
 * 异常检测统计
 */
export interface AnomalyStats {
  /** 总异常数 */
  totalAnomalies: number
  /** 严重异常数 */
  criticalAnomalies: number
  /** 高严重异常数 */
  highAnomalies: number
  /** 中等严重异常数 */
  mediumAnomalies: number
  /** 低严重异常数 */
  lowAnomalies: number
  /** 按业务类型分组 */
  byBusinessType: Record<string, number>
}

/**
 * 异常检测请求参数
 */
export interface AnomalyDetectionParams {
  /** 业务类型 */
  businessType?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 严重程度过滤 */
  severity?: string[]
} 