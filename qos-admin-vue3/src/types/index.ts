export type UserInfo = {
  msisdn: string
  imsi: string
  homeProvince: number
  visitProvince: number
  signNetworkType: number
  isOpenUser: number
  openTime: string
  qosLevel4g: number
  qosLevel5g: number
  userIpv4: string
  userIpv6: string
}

export type OpenAccountRecord = {
  orderTime: string
  messageId: string
  operType: string
  signNetworkType: number
  respCode: number
  respDesc: string
  operSource: string
}

export type ProductSubscribeInfo = {
  qosProductId: string
  productName: string
  systemCode: string
  status: string
  effectiveTime: string
  expireTime: string
  cmServiceIdList: string[]
}

export type OrderRecord = {
  orderIdStr: string
  orderType?: '专载' | '默载'
  qosProductId: string | number
  cmServiceId: string
  speedType?: number
  bearerMode?: number
  startTime: string
  userEndTime?: string
  endTime?: string
  duration?: number
  status: number
  delSource?: string
}

export type MockRule = {
  id: number
  mockAppName: string
  serviceName: string
  serviceType: string
  methodName: string
  appName: string
  mockFlag: boolean
  mockType: string
  mockContent: string
  faultFlag: boolean
  faultType: string
  faultDetail: string
}

export type DefOrderFlowRecord = {
  orderIdStr: string
  qosProductId: number
  cmServiceId: string
  bearerMode: number
  operType: number
  respCode: number
  respDesc: string
  duration: number
  startTime: string
  createTime: string
  orderTime: string
  operTime: string
  respTime: string
  returnTime: string
  speedType: number
  afid: string
  neId: string
  homeProvince: number
  visitProvince: number
  orderSource: string
  traceId: string
}

export type DedOrderFlowRecord = {
  orderIdStr: string
  qosProductId: number
  cmServiceId: string
  operType: number
  respCode: number
  respDesc: string
  duration: number
  startTime: string
  createTime: string
  orderTime: string
  operTime: string
  respTime: string
  returnTime: string
  publicIp: string
  privateIp: string
  targetIp: string
  correlationId: string
  afid: string
  neId: string
  homeProvince: number
  visitProvince: number
  orderSource: string
  traceId: string
}

export type UserSubscribeFlowRecord = {
  qosProductId: number
  operType: number
  effectiveTime: string
  expireTime: string
  createTime: string
  respCode: number
  respDesc: string
}

export type OpenAccountRecords = OpenAccountRecord[]
export type ProductSubscribeInfos = ProductSubscribeInfo[]
export type OrderRecords = OrderRecord[]
export type MockRules = MockRule[]
export type DefOrderFlowRecords = DefOrderFlowRecord[]
export type DedOrderFlowRecords = DedOrderFlowRecord[]
export type UserSubscribeFlowRecords = UserSubscribeFlowRecord[]
