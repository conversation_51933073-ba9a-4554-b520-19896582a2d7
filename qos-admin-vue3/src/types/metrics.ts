/**
 * API请求的基础参数
 */
export interface BaseMetricsReq {
  startTime: string
  endTime: string
  communicationServices?: string[]
}

/**
 * 业务指标查询请求 (/getMetrics/1.0)
 */
export interface BusinessMetricsReq extends BaseMetricsReq {
  selectType: 'all' | 'defbearer' | 'dedbearer'
}

/**
 * 流程分阶段数据查询请求 (/process/stage-data/1.0)
 */
export interface ProcessStageDataReq extends BaseMetricsReq {
  selectType: 'defbearer' | 'dedbearer'
}

/**
 * 服务分类流程指标查询请求 (/metricsByService/1.0)
 */
export interface BusinessMetricsByServiceReq extends BaseMetricsReq {
  selectType: 'defbearer' | 'dedbearer'
}

// --- 响应类型 ---

/**
 * 通用响应包装器
 */
export interface ApiResponse<T> {
  code: number
  msg: string
  traceId: string
  data: T
}

/**
 * 父指标PO
 */
export interface ParentBusinessMetricsPO {
  dayId: string
  allCount?: number
  noRepeatCount?: number
  southCount?: number
  southSucCount?: number
  southFailCount?: number
}

/**
 * 默载指标PO
 */
export interface DefbearerBusinessMetricsPO extends ParentBusinessMetricsPO {
  southSuccessPercentage?: number
  southFailurePercentageOfAll?: number
  requestSuccessPercentage?: number
}

/**
 * 专载指标PO
 */
export interface DedbearerBusinessMetricsPO extends ParentBusinessMetricsPO {
  getNumSucCount?: number
  getNumFailCount?: number
  getNumCount?: number
  getNumSuccessPercentage?: number
  getNumFailPercentageOfAll?: number
  southFailNo5GCount?: number
  southSuc5GCount?: number
  southSuccessPercentage?: number
  southFailurePercentageOfAll?: number
  requestSuccessPercentage?: number
  businessSuccessPercentage?: number
  interruptCount?: number
  interruptionPercentage?: number
  correlationRebuildCount?: number
  correlationRebuildSucCount?: number
  rebuildSuccessPercentage?: number
  businessSuccessPercentageFitting?: number
  requestSuccessPercentageFitting?: number
  southCountNo5G?: number
  southSuccess5GPercentage?: number
  southFailure5GPercentageOfAll?: number
}

/**
 * 汇总统计DTO
 */
export interface AllBusinessMetricsDTO {
  dayId: string
  dedbearerRequestSuccessPercentage?: number
  defbearerRequestSuccessPercentage?: number
  overallSuccessRate?: number
}

/**
 * /getMetrics/1.0 接口的返回DTO
 */
export interface ReturnBusinessMetricsDTO {
  allMetricsList: AllBusinessMetricsDTO[]
  dedbearerMetricsList: DedbearerBusinessMetricsPO[]
  defbearerMetricsList: DefbearerBusinessMetricsPO[]
}

/**
 * 流程分阶段数据PO
 */
export interface ProcessStageDataPO {
  stageType: string
  type: string
  respCode: number
  respDesc: string
  count: number
}

/**
 * 按天聚合的流程分阶段数据PO
 */
export interface ProcessStageDayDataPO {
  dayId: string
  stageDataList: ProcessStageDataPO[]
}

/**
 * 默载按服务分类的指标PO
 */
export interface DefbearerCmServiceMetricsPO {
  cmServiceId: string
  cmServiceName: string
  defbearerMetricsList: DefbearerBusinessMetricsPO[]
}

/**
 * 专载按服务分类的指标PO
 */
export interface DedbearerCmServiceMetricsPO {
  cmServiceId: string
  cmServiceName: string
  dedbearerMetricsList: DedbearerBusinessMetricsPO[]
}

/**
 * /metricsByService/1.0 接口的返回DTO
 */
export interface BusinessMetricsByServiceDTO {
  dedbearerCmServiceList: DedbearerCmServiceMetricsPO[]
  defbearerCmServiceList: DefbearerCmServiceMetricsPO[]
}
