{"name": "qos-admin-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && vite build --mode test", "build:prod": "vue-tsc -b && vite build --mode production", "build:backup": "vue-tsc -b && vite build --mode backup", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix --ignore-path .gitignore", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.7.6", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.15.19", "@types/vue": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-commonjs": "^0.10.4", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^2.0.21"}}