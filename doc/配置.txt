{"mcpServers": {"shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "E:/mcp-shrimp-task-manager/data", "ENABLE_GUI": "false", "TEMPLATES_USE": "en"}}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"], "env": {}}, "mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Shanghai"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "mcp-feedback-enhanced": {"command": "python", "args": ["-m", "mcp_feedback_enhanced", "server"], "env": {"MCP_DEBUG": "false", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "18765", "autoApprove": "[\"interactive_feedback\"]", "timeout": "600"}}}}