# 默载批量调用详细设计

# 变更内容

2024-07-04

批量默载接口，接收请求新增结束时间，调用新系统默载时传入固定的结束时间

文件解析接口需要增加校验，最后一批执行结束时间是否在固定结束时间之前

# 1、数据表

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/f880922d-240a-42dc-9195-170c79f06d20.png)

## qos\_defbearer\_batch\_task 默载批量调用任务表(已完成)

```mysql
CREATE TABLE qos_defbearer_batch_task (
  plan_id bigint NOT NULL COMMENT '计划任务ID',
  message_id varchar(64) NOT NULL COMMENT '请求ID', 
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  cm_service_id varchar(50) NOT NULL COMMENT '通信服务id',
  duration int NOT NULL COMMENT '订单时长(单位秒)',
  home_province int NOT NULL COMMENT '归属省',
  msisdn_file_path varchar(500) NOT NULL COMMENT '手机号文件路径',
  execute_result_file_path varchar(500) DEFAULT NULL COMMENT '执行结果文件路径',
  terminate_result_file_path varchar(500) DEFAULT NULL COMMENT '终止结果文件路径',
  execute_msisdn_count int DEFAULT NULL COMMENT '执行号码数量',
  req_expect_start_time datetime NOT NULL COMMENT '请求预置开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_start_time datetime DEFAULT NULL COMMENT '预期开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_end_time datetime DEFAULT NULL COMMENT '预期结束调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_result_notice_time datetime DEFAULT NULL COMMENT '执行计划结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_start_time datetime DEFAULT NULL COMMENT '实际开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_end_time datetime DEFAULT NULL COMMENT '实际结束调用时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_result_notice_time datetime DEFAULT NULL COMMENT '执行结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_terminate_finish_time datetime DEFAULT NULL COMMENT '预期终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_terminate_finish_time datetime DEFAULT NULL COMMENT '实际终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  status tinyint NOT NULL DEFAULT 0 COMMENT '状态 0-已接收请求待解析文件 1-待执行 2-执行完成待反馈执行结果 3-已反馈执行结果 4-待终止 5-终止完成待反馈终止结果 6-已反馈终止结果 7-解析失败',
  req_source varchar(100) DEFAULT NULL COMMENT '请求来源',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (plan_id) USING BTREE,
  KEY index_status_expect_time_province (status,expect_start_time,home_province) USING BTREE,
  KEY index_rel_end_time (rel_end_time) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载批量调用任务表';
```

| 字段名称 |  | 字段描述 |
| --- | --- | --- |
| plan\_id | planId | 计划任务ID |
| message\_id | messageId | 请求ID |
| qos\_product\_id | qosProductId | qos产品id |
| cm\_service\_id | cmServiceId | 通信服务id |
| duration | duration | 订单时长(单位秒)(按时长进行调用此字段不为空) |
| order\_end\_time | orderEndTime | 订单结束时间（按订单结束时间进行调用此字段不为空） |
| home\_province | homeProvince | 归属省（英文） |
| msisdn\_file\_path | msisdnFilePath | 手机号文件路径 |
| execute\_result\_file\_path | executeResultFilePath | 执行结果文件路径 |
| terminate\_result\_file\_path | terminateResultFilePath | 终止结果文件路径 |
| execute\_msisdn\_count | executeMsisdnCount | 执行号码数量 |
| req\_expect\_start\_time | reqExpectStartTime | 请求预置开始调用时间 |
| expect\_start\_time | expectStartTime | 预期开始调用时间 |
| expect\_end\_time | expectEndTime | 预期结束调用时间 |
| rel\_start\_time | relStartTime | 实际开始调用时间 |
| rel\_end\_time | relEndTime | 实际结束调用时间 |
| expect\_terminate\_finish\_time | expectTerminateFinishTime | 预期终止完成时间 |
| rel\_terminate\_finish\_time | relTerminateFinishTime | 实际终止完成时间 |
| status | status | 状态 0-已接收请求待解析文件 1-待执行 2-执行完成待反馈执行结果 3-已反馈执行结果 4-待终止 5-终止完成待反馈终止结果 6-已反馈终止结果 7-解析失败 |
| req\_source | reqSource | 请求来源（英文） |

## qos\_defbearer\_batch\_time\_slot 默载批量调用时段表(已完成)

```mysql
CREATE TABLE qos_defbearer_batch_time_slot (
  plan_id bigint NOT NULL COMMENT '计划任务ID',
  slot_id int NOT NULL COMMENT '任务执行时段序号',
  home_province int NOT NULL COMMENT '归属省',
  config_start_time datetime NOT NULL COMMENT '配置开始时间，格式为yyyy-MM-dd HH:mm:ss',
  config_end_time datetime NOT NULL COMMENT '配置结束时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_msisdn_count int NOT NULL COMMENT '执行号码数量',
  status tinyint NOT NULL DEFAULT 0 COMMENT '状态 0-待执行 1-执行中 2-已完成 3-终止执行',
  execute_time datetime DEFAULT NULL COMMENT '执行时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (plan_id,slot_id) USING BTREE,
  KEY index_status_time_province(config_start_time,home_province,status) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载批量调用时段表';
```

## qos\_defbearer\_batch\_time\_slot\_shard\_relation 默载批量调用时段分片序号关联表(已完成)

```mysql
CREATE TABLE qos_defbearer_batch_time_slot_shard_relation (
  plan_id bigint NOT NULL COMMENT '计划任务ID',
  slot_id int NOT NULL COMMENT '任务执行时段序号',
  shard_index int NOT NULL COMMENT '分片序号',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (plan_id,slot_id,shard_index) USING BTREE,
  KEY index_create_time(create_time) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载批量调用时段分片序号关联表';
```

## qos\_defbearer\_batch\_expect\_order 默载批量调用执行订单表(已完成)

```mysql
CREATE TABLE qos_defbearer_batch_expect_order (
  id int NOT NULL COMMENT '序号，用作分片查询使用',
  plan_id bigint NOT NULL COMMENT '计划任务ID',
  msisdn varchar(20) NOT NULL COMMENT '手机号码',
  order_id bigint DEFAULT NULL COMMENT '订单号',
  slot_id int DEFAULT NULL COMMENT '任务执行时段序号',
  status tinyint NOT NULL DEFAULT 0 COMMENT '状态 0-待执行 1-申请成功 2-申请失败 3-终止订单成功 4-终止订单失败',
  resp_code int DEFAULT NULL  COMMENT '返回码',
  resp_desc varchar(255) DEFAULT NULL  COMMENT '返回描述',
  rel_start_time datetime DEFAULT NULL COMMENT '实际开始时间',
  rel_final_end_time datetime DEFAULT NULL COMMENT '实际最终终止时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (plan_id,msisdn) USING BTREE,
  KEY index_plan_id_status(plan_id,status) USING BTREE,
  KEY index_plan_id_slot_id(plan_id,slot_id) USING BTREE,
  KEY index_plan_id_id(plan_id,id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载批量调用执行订单表';
```

## 归档表-qos\_defbearer\_batch\_task\_history 默载批量调用任务历史表

```mysql
CREATE TABLE pcc_slicepro.qos_defbearer_batch_task_history_local on cluster 'ck-share-pod1'(
  plan_id UInt64 COMMENT '计划任务ID',
  message_id String COMMENT '请求ID',
  qos_product_id Int32 COMMENT 'qos产品id',
  cm_service_id String COMMENT '通信服务id',
  duration Int32 DEFAULT -1 COMMENT '订单时长(单位秒)',
  order_end_time DateTime64 COMMENT '订单结束时间，格式为yyyy-MM-dd HH:mm:ss',
  home_province Int32 COMMENT '归属省',
  msisdn_file_path String COMMENT '手机号文件路径',
  execute_result_file_path String COMMENT '执行结果文件路径',
  terminate_result_file_path String COMMENT '终止结果文件路径',
  execute_msisdn_count Int32 DEFAULT -1 COMMENT '执行号码数量',
  req_expect_start_time DateTime64 COMMENT '请求预置开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_start_time DateTime64 COMMENT '预期开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_end_time DateTime64 COMMENT '预期结束调用时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_result_notice_time DateTime64 COMMENT '执行计划结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_start_time DateTime64 COMMENT '实际开始调用时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_end_time DateTime64 COMMENT '实际结束调用时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_result_notice_time DateTime64 COMMENT '执行结果上报时间，格式为yyyy-MM-dd HH:mm:ss',
  expect_terminate_finish_time DateTime64 COMMENT '预期终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  rel_terminate_finish_time DateTime64 COMMENT '实际终止完成时间，格式为yyyy-MM-dd HH:mm:ss',
  status Int8 COMMENT '状态 0-已接收请求待解析文件 1-待执行 2-执行完成待反馈执行结果 3-已反馈执行结果 4-待终止 5-终止完成待反馈终止结果 6-已反馈终止结果 7-解析失败',
  req_source String COMMENT '请求来源',
  create_time DateTime64(3) COMMENT '创建时间',
  update_time DateTime64(3) COMMENT '修改时间'
) 
ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_defbearer_batch_task_history_local','{replica}')
PARTITION BY toYYYYMM(create_time)
ORDER BY (plan_id)
PRIMARY KEY (plan_id);

CREATE TABLE pcc_slicepro.qos_defbearer_batch_task_history_all on cluster 'ck-share-pod1'  AS  pcc_slicepro.qos_defbearer_batch_task_history_local ENGINE = Distributed('ck-share-pod1',
 'pcc_slicepro',
 'qos_defbearer_batch_task_history_local',
 intHash64(plan_id));
```

## 归档表-qos\_defbearer\_batch\_time\_slot\_history 默载批量调用时段历史表

```mysql
CREATE TABLE pcc_slicepro.qos_defbearer_batch_time_slot_history_local on cluster 'ck-share-pod1'(
  plan_id UInt64 COMMENT '计划任务ID',
  slot_id Int32 COMMENT '任务执行时段序号',
  home_province Int32 COMMENT '归属省',
  config_start_time DateTime64 COMMENT '配置开始时间，格式为yyyy-MM-dd HH:mm:ss',
  config_end_time DateTime64 COMMENT '配置结束时间，格式为yyyy-MM-dd HH:mm:ss',
  execute_msisdn_count Int32 DEFAULT -1 COMMENT '执行号码数量',
  status Int8 COMMENT '状态 0-待执行 1-执行中 2-已完成 3-终止执行',
  execute_time DateTime64 COMMENT '执行时间',
  create_time DateTime64(3) COMMENT '创建时间',
  update_time DateTime64(3) COMMENT '修改时间'
) 
ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_defbearer_batch_time_slot_history_local','{replica}')
PARTITION BY toYYYYMM(create_time)
ORDER BY (plan_id,slot_id)
PRIMARY KEY (plan_id,slot_id);

CREATE TABLE pcc_slicepro.qos_defbearer_batch_time_slot_history_all on cluster 'ck-share-pod1'  AS  pcc_slicepro.qos_defbearer_batch_time_slot_history_local ENGINE = Distributed('ck-share-pod1',
 'pcc_slicepro',
 'qos_defbearer_batch_time_slot_history_local',
 intHash64(plan_id));
```

## 归档表-qos\_defbearer\_batch\_expect\_order\_history 默载批量调用执行订单历史表

```mysql
CREATE TABLE pcc_slicepro.qos_defbearer_batch_expect_order_history_local on cluster 'ck-share-pod1'(
  id Int32  COMMENT '序号，用作分片查询使用',
  plan_id UInt64 COMMENT '计划任务ID',
  msisdn String COMMENT '手机号码',
  order_id Int64 DEFAULT -1 COMMENT '订单号',
  slot_id Int32 DEFAULT -1 COMMENT '任务执行时段序号',
  status Int8  COMMENT '状态 0-待执行 1-申请成功 2-申请失败 3-终止订单成功 4-终止订单失败',
  resp_code Int32 DEFAULT -1 COMMENT '返回码',
  resp_desc String COMMENT '返回描述',
  rel_start_time DateTime64  COMMENT '实际开始时间',
  rel_final_end_time DateTime64 COMMENT '实际最终终止时间',
  create_time DateTime64(3) COMMENT '创建时间',
  update_time DateTime64(3) COMMENT '修改时间'
) 
ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_defbearer_batch_expect_order_history_local','{replica}')
PARTITION BY toYYYYMM(create_time)
ORDER BY (plan_id, msisdn , status)
PRIMARY KEY (plan_id, msisdn);

CREATE TABLE pcc_slicepro.qos_defbearer_batch_expect_order_history_all on cluster 'ck-share-pod1'  AS  pcc_slicepro.qos_defbearer_batch_expect_order_history_local ENGINE = Distributed('ck-share-pod1',
 'pcc_slicepro',
 'qos_defbearer_batch_expect_order_history_local',
 intHash64(plan_id));
```

# 2、功能性设计

## 状态流转

### 任务表状态

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/9d632c3b-498b-4a18-97d0-6da625cf3697.png)

### 时段表状态

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/701aae87-7531-479d-a93a-b650ce7aedf0.png)

### 执行订单表状态

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/317fe7b7-5086-4559-acee-ddfc2ba0748f.png)

## 2.1、功能接口设计

| **编号** | **接口名** | **功能描述** | **外部依赖** |
| --- | --- | --- | --- |
| 1 | 默载批量申请（包括订购） | 接收批量申请请求，校验参数后，反馈计划执行结果，到时执行后反馈最终执行结果<br>规则校验：<br>1.  请求参数校验<br>    <br>2.  文件校验<br>    <br>3.  通信服务状态校验<br>    <br>4.  产品状态校验（直接查表）<br>    <br>5.  通信服务与产品的匹配校验（直接查表）<br>    <br>![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/23467322-80a7-4d3f-8fcf-4b69176af590.png) | 调用快照服务获取通信服务状态信息 |
| 2 | 默载批量终止 | 接收批量终止<br>规则校验：<br>6.  请求参数校验<br>    <br>7.  如终止时间已经超过实际执行结束时间+时长，不允许终止<br>    <br>8.  任务表关联执行订单表无待执行/执行成功的数据，不允许终止<br>    <br>9.  终止系统与申请时传入系统一致<br>    <br>10.  计划任务ID校验（存在且状态不为已接收请求待解析文件、待终止、待反馈终止结果、解析失败）(状态为已接收请求待解析文件需要返回原因，正在文件解析中，请稍后终止)<br>    <br>![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/J9LnWNYWXN0WlvDe/img/b0095bd1-1006-4721-88d4-02f4ad635824.png) |  |
| 3 | 文件上传(网关) | 接收请求方的请求，在sftp生成文件并写入<br>规则校验：<br>1.  请求参数校验<br>    <br>2.  号码数量校验（1-500万）<br>    <br>3.  文件格式校验，需为csv文件 | sftp进行文件存储 |
| 4 | 文件下载（网关） | 接收请求方的请求，捞取sftp文件进行输出<br>规则校验：<br>1.  请求参数校验<br>    <br>2.  根据路径未找到文件 | sftp进行文件下载 |
|  |  |  |  |

## 2.2、文件样例

### 2.2.1 申请上传号码文件

```plaintext
156********
156********
186********
```

### 2.2.2 执行结果文件

```plaintext
156********,0,成功,2024-05-17 10:10:10
156********,202001,策略冲突,2024-05-17 10:10:10
186********,202002,产品互斥,2024-05-17 10:10:10
```

### 2.2.3 终止结果文件

```plaintext
156********,0,取消执行,2024-05-17 10:10:10
156********,0,终止成功,2024-05-17 10:10:10
186********,202005,终止失败,2024-05-17 10:10:10
```

## 2.3、核心功能实现

### 2.3.1 默载批量申请（包括订购）

```plantuml
@startuml

  participant "qos-batch" as batch order 1
  participant "qos-snapshot" as snap order 2
  database mysql order 3
  queue kafka order 4
  participant sftp order 5

  调用方->batch:发起默载申请
  opt 请求参数校验不通过
    调用方<--batch:返回失败
  end
  group 业务校验
  batch->sftp:文件校验（基于文件路径校验文件是否存在）
  group 通信服务状态校验
    batch->snap:获取配置
    snap-->batch:返回通信服务配置数据
    batch->batch:通信服务状态校验(存在且为使用中)
  end
  group 产品及匹配关系校验
  batch->mysql:查询产品表及通信服务与产品匹配表，一次查询
  batch->batch:产品状态校验
  batch->batch:通信服务与产品匹配校验
  end
  end
  opt 业务校验不通过
       调用方<--batch:返回失败
  end
  batch->mysql:生成plan_id，使用13位毫秒时间戳+5位随机数
  batch->kafka:发送文件解析消息进行后续动作触发
  alt 发送成功
  batch->mysql:插入默载批量调用任务表数据，状态为0
  batch-->调用方:返回成功并返回plan_id
  else 发送失败
    batch-->调用方:返回失败
  end
  @enduml
  
```

### 2.3.2 文件解析及计划执行结果上报（7点到9点容量是0 月底前7天 1秒100）

```plantuml
@startuml

  participant "qos-batch-async" as batch order 2
  participant "TimeCalculation method" as time order 3 
participant "qos-gateway" as gateway order 6

  database redis order 4

  database mysql order 4
  queue kafka order 1
  participant sftp order 5
  kafka->batch:消费消息进行处理
  batch->mysql:根据消息体中plan_id查询任务
  opt 状态不等于0
    batch-->kafka:提交offset  
  end
  batch->batch:放入独立线程执行,\n执行异常发送到此kafka中进行重试处理
  batch-->kafka:提交offset

  group 解析文件中的号码入库
    batch->batch:定义总计数变量
    batch->sftp:使用文件路径逐行读取文件
    loop 文件存在下一行数据
    batch->batch:读取下一行数据序号放入号码列表,记录ID号为当前计数
    batch->batch:总计数+1
    opt 号码列表达到批次插入最大数量(预期打算配置2000条一次)
       batch->mysql:进行默载批量调用执行订单表插入
      batch->batch:清空号码列表
    end
end
  opt 号码列表不为空
    batch->mysql:进行默载批量调用执行订单表插入
    batch->batch:清空号码列表
  end
  end
group 文件解析异常处理
  opt 上面解析文件入库出现异常
    batch->mysql:修改任务表状态为解析失败
  batch->gateway:上报失败结果
    opt 上报失败
     batch->kafka:发送结果上报消息，类型传入1，计划执行结果上报,重试次数设置为1
end
end
end

  batch->redis:获取锁，设置锁超时时间为2分钟（用省分编码加锁，同一时间只有一个请求允许计算时间）
  batch->redis:间隔5秒循环获取锁，持续等待最长2分钟后获取到锁
  
    batch->batch:查找当前时间+1分（冗余一下，防止现在离5分特别近，导致任务未扫描到）往后最近的一个5分钟作为开始时间
    batch->time:调用计算实际需要时间段方法，传入开始时间、总计数
 group 并发数计算
  time-> time:使用省分+pcf获取配置数据，\n基于配置数据计算并发数\n并发数=PCF套数*单套并发（150）
  time->time:计算单个时段最大执行数量:并发数*3分钟（可以留出一些冗余，涉及先订购后调用，实际任务间隔为5分钟）
  time->time:计算月底前7天时段最大执行数量:100*3分钟（可以留出一些冗余，涉及先订购后调用，实际任务间隔为5分钟）
  end
    time->time:定义时段map，key:时间,value：容量
    loop 总计数>0
      alt 开始时间>=19点&&开始时间<21点
        time->time:开始时间=21点
      else 开始时间在月底前7天
        time->time:容量=容量+100*3分钟
        alt 总计数=总计数-容量>=0
          time->time:时段map存入key：开始时间，value：容量
        else 总计数=总计数-容量<0
            time->time:时段map存入key：开始时间，value：总计数
        end
        time->time:开始时间+5分钟
      else 常规时段
        time->time:容量=容量+单个时段最大执行数量
alt 总计数=总计数-容量>=0
          time->time:时段map存入key：开始时间，value：容量
        else 总计数=总计数-容量<0
            time->time:时段map存入key：开始时间，value：总计数
        end
        time->time:开始时间+5分钟
end  
end    
    time-->batch:返回时段map

  group 可执行时段判断
    loop
    batch->batch:计算结束时间=map中最大开始时间+5分钟
    batch->mysql:查询时段表，条件开始时间<config_end_time and 结束时间 > config_start_time 
    alt 存在数据(说明有交叉时间段，需要往后继续去找可执行时间段)
    batch-> batch:开始时间=返回数据中最大的结束时间
    batch->time:调用计算实际需要时间段方法，传入开始时间、总计数
    time-->batch:返回时段map和需要时间段个数

    else 不存在数据（说明无交叉时段，可以插入数据）
    batch->batch:终止循环
end
  end
end
  opt 时段map不为空
    batch->batch:循环时段map生成时段对象，补充配置开始时间、配置结束时间等
    batch->mysql:时段map插入默载批量调用时段表
    batch->mysql:更新任务表状态为1待执行、预期开始时间、预期结束时间、执行号码数量
  opt 上述存在异常
    batch->kafka:发送异常重试消息
end
    batch->redis:解锁
    batch->gateway:上报结果,上报结束时间多+15分钟(考虑根据号码多少加不同的时间)，用于后续执行后生成结果文件等
    opt 上报失败
     batch->kafka:发送结果上报消息，类型传入1，计划执行结果上报,重试次数设置为1
    else 上报成功
      batch->mysql:修改任务表执行计划结果上报时间为当前时间
    end
  end
  @enduml
  
```

### 2.3.3 任务到期执行

```plantuml
@startuml

  participant "qos-batch-task" as batch order 1
  participant "qos-task" as task order 0
  participant "qos-user" as user order 3
  participant "qos-defbearer-service" as def order 3

  queue "executeThreadPool" as thread order 2

  database mysql order 3
  task->batch:发起分片任务扫描 5分钟一次
  batch->batch:记录一下当前时间为任务开始时间
  batch->mysql:以当前时间查询时段表（关联计划表，计划表状态为待执行），条件配置开始时间<=当前时间、\n省分编码、状态为待执行/执行中，按照配置开始时间倒序排列
  opt 时段表数据为空
    batch-->task:结束
  end
  group 并发数计算
    batch-> batch:普通时段：使用省分+pcf获取配置数据，\n基于配置数据计算并发数\n并发数=PCF套数*单套并发（150）\n月底7天：100
    end
  loop 时段表数据不为空
      batch->batch:动态创建线程池，线程池核心数为当前时段所属并发数/总分片数
    opt 配置结束时间>当前时间(本时段必须执行的数据)
      batch->mysql:修改此条数据状态为1
      batch->batch:定义执行数量变量
      loop 执行数量<时段表中的执行数量
      batch->mysql:分批查询执行订单表，按照ID分片查询，单次查询5000条数据,状态为
      opt 用户数据为空
        batch->batch:结束本次循环
      end
      batch->thread:提交线程池执行
      thread->thread:定义请求参数
      thread->user:进行产品订购
      user->thread:返回结果
      alt 失败
        thread->thread:记录号码、响应码、响应描述、时段序号、成功失败标记
      else 成功
      alt 开关标记为老系统
        thread->thread:来源赋值为任务表中req_source+'_batch'
        thread->def:拼装调用老系统pcc服务的请求报文
        thread->def:进行老系统默载申请调用(失败需要重试吗)
      else 其他情况
        thread->def:进行新系统默载申请调用
  thread->thread:来源赋值为任务表中req_source+'_batch'
      end

      def-->thread:返回结果
      thread->thread:记录号码、订单号、响应码、响应描述、时段序号、成功失败标记
      end
      thread->mysql:修改用户表此条数据,条件状态不为执行成功\n（防止被别的线程执行过了）
      batch->batch:等待线程池中任务都执行完毕后进行下次循环

      end
      batch->batch:等待线程池任务全部执行完毕
      batch->mysql:修改时段表状态为已完成
    else 配置结束时间<当前时间（说明此任务属于未被执行或未执行完需要补充执行）
      opt 当前时间-任务开始时间<30s
        batch->batch:结束循环
      end
      batch->mysql:修改此条数据状态为1
      batch->batch:定义执行数量变量
      loop 执行数量<时段表中的执行数量

      opt 当前时间-任务开始时间<30s
        batch->batch:结束循环
      end
      batch->mysql:分批查询执行订单表，按照ID分片查询，单次查询1000条数据,状态为待执行
      opt 用户数据为空
        batch->batch:结束本次循环
      end
      batch->thread:提交线程池执行
      batch->batch:等待线程池中任务都执行完毕后进行下次循环
    end
      batch->batch:等待线程池任务全部执行完毕,此位置最多等待30秒，防止影响后续时段执行
      opt 线程池任务全部执行完毕
        batch->mysql:修改时段表状态为已完成
      end
  end
 end
  batch->batch:关闭线程池

batch-->task:结束
  @enduml
  
```

### 2.3.4 任务执行完成扫描

```plantuml
@startuml

  participant "qos-batch-task" as batch order 1
  participant "qos-task" as task order 0
  database mysql order 3
  queue kafka order 4
  task->batch:1分钟一次执行任务
  batch->mysql:查询任务表，条件：状态为待执行、时段表存在关联数据、时段表中数据均为已完成状态
  opt 不存在数据
    batch-->task:结束
  end
  batch->mysql:修改任务表状态为执行完成待反馈执行结果、\n实际开始时间为时段表最早执行时间、实际结束时间为时段表最晚修改时间
  batch->kafka:发送生成执行结果文件生成消息
batch-->task:结束
  @enduml
  
```

### 2.3.5 执行结果文件生成及执行结果上报

```plantuml
@startuml

  participant "qos-batch-async" as batch order 2
participant "qos-gateway" as gateway order 6

  database mysql order 4
  queue kafka order 1
  participant sftp order 5
  kafka->batch:消费消息进行处理
  batch->mysql:根据消息体中plan_id查询任务表
  opt 任务状态不为 执行完成待反馈执行结果
    batch-->kafka:提交offset  
  end
  batch->batch:放入独立线程执行,\n执行异常发送到结果上报kafka中进行重试处理
  batch-->kafka:提交offset
  batch->mysql:查询执行订单表数据，获取此条planid最大的序号
  batch->batch:定义计数变量
  opt sftp已存在文件executeresult+planid.csv
    batch->sftp:删除文件(防止文件中数据不全)
  end
  batch->sftp:创建结果文件，目录:qos\省分编码拼音\executeresult+planid.csv
  loop 计数<=最大序号
      batch->mysql:查询执行订单表数据，条件序号<计数变量+1000且大于等于计数变量(每次想要获取1000条数据)
      opt 存在数据
      batch-> sftp:写入文件中
      end
      batch->batch:计数变量+1000
  end
    batch->gateway:上报执行结果
    alt 上报失败
     batch->kafka:发送结果上报消息，类型传入2，执行结果上报,重试次数设置为1
    else 上报成功
      batch->mysql:修改任务表状态为已执行，执行结果上报时间修改为当前时间
    end
  @enduml
  
```

### 2.3.6 默载批量终止

```plantuml
@startuml

  participant "qos-batch" as batch order 1
  database mysql order 3
  queue kafka order 4
  调用方->batch:发起默载批量终止
  opt 请求参数校验不通过
    调用方<--batch:返回失败
  end
  batch->mysql:根据计划ID查询任务表数据（条件新增exists 执行订单表存在状态为待执行或执行成功数据）
  group 业务校验
  batch->batch:来源校验终止系统与申请时传入req_source一致
  batch->batch:计划任务ID校验（不存在或状态为已接收请求待解析文件、待终止、待反馈终止结果、已终止、解析失败）
  batch->batch:如当前时间已经超过实际执行结束时间+时长，不允许终止
  end
  opt 业务校验不通过
       调用方<--batch:返回失败
  end
  batch->mysql:修改任务表状态为待终止，条件计划任务ID
  batch->mysql:修改时段表数据状态为终止执行，条件计划任务ID
  batch->kafka:发送终止消息进行后续动作触发
  opt 发送失败
    batch->batch:回滚mysql事务
batch-->调用方:返回失败
  end
  batch-->调用方:返回成功
  @enduml
  
```

### 2.3.7 终止消息处理

```plantuml
@startuml

  participant "qos-batch-async" as batch order 2
  participant "TimeCalculation method" as time order 3 
participant "qos-gateway" as gateway order 6

  database redis order 4

  database mysql order 4
  queue kafka order 1
  kafka->batch:消费消息进行处理
  batch->mysql:根据消息体中plan_id查询任务
  opt 状态不等于待终止
    batch-->kafka:提交offset  
  end
  batch->mysql:根据plan_id查询执行订单表，状态为执行成功的数量
  group 终止预计结束时间
  batch->batch:设定并发为30
  batch->batch:需要执行时间：数量/30 秒，预计结束时间=当前时间+执行时间秒数+30分钟冗余，用来处理结果数据
  end
      batch->mysql:修改任务表预期终止完成时间为终止预计结束时间
    batch->gateway:上报预计结束时间结果
    opt 上报失败
     batch->kafka:发送结果上报消息，类型传入3，计划终止结果通知,重试次数设置为1
    end
  batch-->kafka:提交offset

  @enduml
  
```

### 2.3.8 终止执行任务

```plantuml
@startuml

  participant "qos-batch-task" as batch order 1
  participant "qos-task" as task order 0
  participant "qos-user" as user order 3
  participant "qos-defbearer-service" as def order 3

  queue "executeThreadPool" as thread order 2

  database mysql order 3
  queue kafka order 4

  task->batch:发起任务扫描 2分钟一次
  batch->batch:记录一下当前时间为任务开始时间
  batch->mysql:查询任务表中状态为待终止数据
  opt 数据为空
    batch-->task:结束
  end
  loop 当前时间-任务开始时间<1分55秒
    batch->mysql:查询执行订单表,状态为执行成功，单次查询1000条数据
    opt 用户数据为空
        batch->mysql:查询时段表是否存在执行中、已完成的数据，条件计划ID
        alt 无数据||只存在执行中数据且当前时间-配置结束数据>5
        opt 存在执行中数据且当前时间-配置结束数据>5
           batch->mysql:修改时段表状态为已终止
        end
        batch->mysql:修改任务表状态为待反馈终止结果,实际终止完成时间为当前时间
        batch->kafka:发送生成终止结果文件生成消息
        batch->batch:结束本次循环
        else 存在已完成数据
          batch->mysql:修改时段表状态为已终止
        else 只存在执行中数据且当前时间-配置结束数据<=5(防止有在途执行的)
          batch->batch:结束本次循环
        end
    end
      batch->batch:提交线程池执行
    group 线程池处理 核心线程数30
       alt 开关标记为老系统
  thread->thread:来源赋值为任务表中req_source+'_batch'
        thread->def:拼装调用老系统pcc服务的请求报文
        thread->def:进行老系统默载应急删除调用
      else 其他情况
        thread->thread:来源赋值为任务表中req_source+'_batch'
        thread->def:使用订单号进行新系统默载终止调用
      end
      batch->batch:等待线程池中任务都执行完毕后进行下次循环
end
  end
       batch->batch:等待线程池任务全部执行完毕
batch-->task:结束
  @enduml
  
```

### 2.3.9 终止结果文件生成及终止结果上报

```plantuml
@startuml

  participant "qos-batch-async" as batch order 2
participant "qos-gateway" as gateway order 6

  database mysql order 4
  queue kafka order 1
  participant sftp order 5
  kafka->batch:消费消息进行处理
  batch->mysql:根据消息体中plan_id查询任务表
  opt 任务状态不为 终止完成待反馈终止结果
    batch-->kafka:提交offset  
  end
  batch->batch:放入独立线程执行,\n执行异常发送到此kafka中进行重试处理
  batch-->kafka:提交offset
  batch->mysql:查询执行订单表数据，获取此条planid最大的序号,状态为终止成功或终止失败或待执行
  batch->batch:定义计数变量
  opt sftp已存在文件terminateresult+planid.csv
    batch->sftp:删除文件(防止文件中数据不全)
  end
  batch->sftp:创建结果文件，目录:qos\省分编码拼音\terminateresult+planid.csv
  loop 计数<=最大序号
      batch->mysql:查询执行订单表数据，条件序号<计数变量+1000且大于等于计数变量(每次想要最多获取1000条数据),状态为终止成功或终止失败或待执行
      opt 存在数据
        opt 状态为待执行
          batch->batch:状态变量修改为取消执行，时间为当前时间
        end
      batch-> sftp:写入文件中
      end
      batch->batch:计数变量+1000
  end
    batch->gateway:上报执行结果
    alt 上报失败
     batch->kafka:发送结果上报消息，类型传入4，终止结果上报,重试次数设置为1
    else 上报成功
      batch->mysql:修改任务表状态为已终止
    end
  @enduml
  
```

### 2.3.10 结果上报补偿消息处理

```plantuml
@startuml

  participant "qos-batch-async" as batch order 2
participant "qos-gateway" as gateway order 6

  database mysql order 4
  queue kafka order 1
  kafka->batch:消费消息进行处理
  opt 消息体中重试次数>5
     batch-->kafka:提交offset  
  end
  batch->batch:休眠10秒作为重试间隔

  batch->mysql:根据消息体中plan_id查询任务表
alt 消息体中类型为1 &&任务状态不为 待执行
    batch-->kafka:提交offset  
else 消息体中类型为2 &&任务状态不为 执行完成待反馈执行结果
    batch-->kafka:提交offset  

else 消息体中类型为3 &&任务状态不为 待终止
    batch-->kafka:提交offset  
else 消息体中类型为4 &&任务状态不为 终止完成待反馈终止结果
    batch-->kafka:提交offset  
else 消息体中类型为5 &&任务状态不为 待执行
    batch-->kafka:提交offset  
end
alt 类型为1、2、3、4中一个
    batch->gateway:上报执行结果
    alt 上报失败
     batch->kafka:发送结果上报消息，类型传入对应类型，重试次数设置+1
    batch-->kafka:提交offset  

    else 上报成功
      
alt 消息体中类型为1
    batch-->mysql:修改执行计划结果上报时间为当前时间
else 消息体中类型为2 
    batch-->mysql:修改状态为已执行，执行结果上报时间修改为当前时间
else 消息体中类型为4 
      batch->mysql:修改任务表状态为已终止
end  
end
    batch-->kafka:提交offset  
else 类型为5
  batch-->kafka:发送文件解析及计划执行结果上报消息，重试次数+1
    batch-->kafka:提交offset  

end


  @enduml
  
```

### 2.3.11 文件上传

```plantuml
@startuml

  participant "qos-gateway" as batch order 1
  participant "sftp" as sftp order 2
  调用方->batch:发起文件上传
  opt 请求参数校验不通过
    调用方<--batch:返回失败
  end
  group 业务校验
  batch->batch:号码数量校验（1-500万）
  batch->batch:文件格式校验，需为csv文件
  end
  opt 业务校验不通过
       调用方<--batch:返回失败
  end
  batch->batch:生成文件，目录:qos\省分编码拼音\文件名，msisdns+自定义id(13位毫秒时间戳+5位随机数)
  batch->sftp:将文件写入到sftp中
  alt 写入失败
batch-->调用方:返回失败
else 写入成功
  batch->sftp:基于文件名查询文件是否存在(此步可以视情况做与不做)
  alt 存在
  batch-->调用方:返回成功
else
    batch-->调用方:返回失败

end
  end
  @enduml
  
```

### 2.3.12 文件下载

```plantuml
@startuml

  participant "qos-gateway" as batch order 1
  participant "sftp" as sftp order 2
  调用方->batch:发起文件上传
  opt 请求参数校验不通过
    调用方<--batch:返回失败
  end
  group 业务校验
  batch->sftp:文件校验(根据路径查找文件)
  end
  opt 业务校验不通过
       调用方<--batch:返回失败
  end
  batch->sftp:读取文件内容
  alt 读取成功
      batch->调用方:返回文件内容
  else 读取失败
      batch->调用方:返回失败
  end

  @enduml
  
```

### 2.3.13 监控(后续支持)

1.  状态为 已接收请求待解析文件 且当前时间-创建时间>5分钟
    
2.  状态为 待执行 且当前时间-预期结束调用时间>10分钟
    
3.  状态为 执行完成待反馈执行结果 且当前时间-实际结束时间>20分钟
    
4.  时段表状态为 待执行、执行中 且当前时间-配置结束时间>10分钟 
    
5.  状态为 待终止且当前时间-预期终止完成时间>10分钟
    

```plantuml
@startuml

  participant "qos-batch-task" as batch order 1
  participant "qos-task" as task order 0
  database mysql order 3
  task->batch:5分钟扫描一次
  batch->mysql:查询任务表，判断上面需要监控的内容
  opt 不存在数据
    batch-->task:结束
  end
  batch->batch:处理内容，发短信告警么，还是先打个日志出来
batch-->task:结束
  @enduml
  
```

### 2.3.14 数据清理任务（后续，防止执行用户表数据量过大）

```plantuml
@startuml

  participant "qos-batch-task" as batch order 1
  participant "qos-task" as task order 0
  database mysql order 3
  database clickhouse as ck order 4

  task->batch:每日凌晨3点扫描一次
  batch->mysql:查询任务表，条件：\nrel_end_time< 当前时间- duration -冗余（3天） and 状态为3 \n或 状态为6 and rel_terminate_finish_time < 当前时间-冗余（3天） \n或状态为7

  opt 不存在数据
    batch-->task:结束
  end
  loop 上面获取的任务数据列表
    loop 根据任务ID获取执行订单表数据,每次读取5000条，存在数据
    batch->ck: 使用读取的数据插入历史执行订单表
    batch->mysql: 删除读取到的数据
    end
    batch->mysql: 删除时段分片关联表数据

    batch->mysql:根据任务ID获取时段表数据
    batch->ck: 插入任务历史表数据和时段历史表数据
    batch->mysql: 删除时段表数据
    batch->mysql:删除任务表数据
  end
batch-->task:结束
  @enduml
  
```

# 3、任务拆分

开发人员：彭博、范兵兵、杨仕祥

| 周一 | 周二 | 周三 | 周四 | 周五 | 周六 | 周日 |
| --- | --- | --- | --- | --- | --- | --- |
| 5.20（**启动开发**） |  |  |  | 5.24 |  |  |
| 5.27 |  |  |  | 5.31 |  |  |
| 6.3 |  | 6.4（**开发完成**） |  | 6.7（**自测完成**） |  |  |
| 6.10（端午） | 6.11（**可联调**） |  |  | 6.14（**开始走上线流程**） |  |  |
| 6.17 |  |  |  | 6.21（**发版**） |  |  |

| **任务内容** |  | **开始时间** | **结束时间** | **工作量（预估人日）** |
| --- | --- | --- | --- | --- |
| 环境初始化 | sftp服务器搭建 | \- | \- | （找厂商支持搭建，测试+生产都搭） |
| 默载批量申请 | 默载批量申请接口开发 | 5.20 | 5.20 | 1 |
| 文件解析及计划执行结果上报 | 验证springboot连接sftp | 5.21 | 5.21 | 1 |
|  | 实现解析文件号码入库功能 | 5.22 | 5.22 | 1 |
|  | 实现时间资源管理功能 | 5.23 | 5.24 | 2 |
| 任务到期执行 | 实现动态线程池创建及主体时间处理逻辑 | 5.27 | 5.28 | 2 |
|  | 实现调用逻辑功能，包括调用产品订购接口、调用新老系统默载接口 | 5.29 | 5.31 | 3 |
| 任务执行完成扫描 | 实现任务执行完成扫描task功能 | 6.3 | 6.3 | 1 |
| 执行结果文件生成及执行结果上报 | 实现执行结果文件生成及执行结果上报功能 | 5.27 | 5.28 | 2（兵兵支持） |
| 文件上传下载 | 实现文件上传下载 | \- | \- | 2（仕祥支持） |
| 批量调用接口网关对接 | 6个接口 | \- | \- | 1（仕祥支持） |
| 默载批量终止 | 实现默载批量终止功能 | 5.29 | 5.30 | 2（兵兵支持） |
| 终止消息处理 | 实现终止消息处理并上报预期终止时间功能 | 5.31 | 5.31 | 1（兵兵支持） |
| 终止执行任务 | 实现终止执行任务功能 | 6.4 | 6.4 | 1 |
| 终止结果文件生成及终止结果上报 | 实现终止结果文件生成及终止结果上报功能 | 6.3 | 6.3 | 1（兵兵支持） |
| 结果上报补偿消息处理 | 实现结果上报补偿消息处理 | 6.4 | 6.4 | 1（兵兵支持） |
| 测试 | 各功能自测 | 6.5 | 6.5 | 2（彭博+兵兵） |
|  | 完成单元测试 | 6.6 | 6.6 | 2（彭博+兵兵） |
| 效能平台 | 完成CI/CD配置 | 6.7 | 6.7 | \- |
|  | 通过效能平台上线到测试环境 | 6.7 | 6.7 | 1 |
|  | sonar问题解决 | 6.11 | 6.11 | 1 |
| 联调测试 | 配合外部接入方进行联调测试 | 6.12 | 6.13 | 2 |
| 上线流程 | 准备上线所需要的各项材料 | 6.14 | 6.14 | 1 |
|  | 提交发版材料 | 6.17 | 6.17 | \- |
|  | 配合质量室验证 | 6.17 | 6.19 | 3 |
| 上线 | 上线前在生产环境创建相关库表、CI/CD流程 | 6.20 | 6.20 | 1 |
|  | 完成版本发布 | 6.21 | 6.21 | \- |