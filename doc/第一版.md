# 通用批量任务框架设计草案（v0.1 提案）

作者: pengb7  日期: 2025-09-01

## 1. 背景与目标
- 现有“默载批量调用”方案已较为完整，包含任务表、时段表、执行订单表、归档与完整的流程编排。
- 诉求：沉淀为“通用批量任务框架”，做到“数据结构通用 + 处理逻辑通用 + 末端执行差异化（插件化）”。
- 目标：
  - 支持多任务类型（例如：默载申请/终止、其它通信/计费/账务类任务等）。
  - 统一的任务/明细/时段/分片建模；统一状态机；统一补偿与幂等保障。
  - 末端执行采用“执行器SPI + 模板渲染”的方式差异化，可热插拔扩展。
  - 与现有表结构/流程兼容，可平滑迁移。

## 2. 业内主流方案对比（简表）
- Spring Batch（Chunk + Step + Job + Partitioning）：
  - 优点：成熟的批处理抽象、事务/重试/跳过/分区并行、与Spring生态契合；易与XXL-Job/Quartz触发结合。
  - 场景：海量明细处理、稳定性和幂等要求高。
- XXL-Job（或Quartz）+ 自研引擎：
  - 优点：触发与调度简单、对运维友好；业务引擎可高度定制。
  - 场景：已有XXL-Job体系，任务类型多、差异化强。
- 数据工作流编排（DolphinScheduler/Airflow/Argo Workflows）：
  - 优点：跨任务/跨系统依赖强、可视化编排强。
  - 场景：数据平台/离线数仓/跨系统工作流。
- Kafka 驱动的批处理（Topic分片 + 消费者集群）：
  - 优点：天然水平扩展、背压、DLQ、至少一次语义下的幂等落地。
  - 场景：强实时或大规模分发执行。
- K8s Job + Operator：
  - 优点：容器化弹性扩缩、极致隔离与资源治理。
  - 场景：云原生、任务耗时长且需要独立容器环境。

推荐：在现有技术栈（Spring Boot + XXL-Job + Kafka + MySQL/Redis）下，选“XXL-Job 触发 + 自研通用批处理引擎”，必要处借鉴 Spring Batch 的抽象与容错模型；若未来要更强实时/弹性，可逐步引入“Kafka 驱动分发执行”。

## 3. 统一抽象模型（领域）
- Task（任务主表）：一次批量任务的元数据与统一参数（通用维度）。
- TaskItem（任务明细）：每一个执行单元，承载个性化参数与执行状态。
- TimeSlot（时段）：资源并发的容量切片（窗口 + 配额）。
- SlotShard（分片）：方便并行/水平扩展与幂等扫描。
- Template（模板）：执行报文/参数模板，支持变量占位（FreeMarker/Mustache）。
- Executor（执行器SPI）：不同任务类型的“申请/执行/查询/终止/组装”策略。
- ResourceCalendar（资源日历）：描述时段容量策略（常规、月底前7天、夜间停等）。
- Notification（通知）：计划完成/执行完成/终止完成等回调，建议出站Outbox + Kafka补偿。
- StateMachine（有限状态机）：统一管理任务与明细的状态转移与校验。

## 4. 存储设计（通用）
在不破坏既有表的基础上做“泛化扩展”，优先兼容现有表：
- task_master（可沿用 qos_defbearer_batch_task，新增或复用字段）：
  - task_type（任务类型枚举/字符串）、task_name、template_id、default_params（JSON）、req_source、期望/实际时间、status …
- task_item（可沿用 qos_defbearer_batch_expect_order，新增/复用）：
  - item_key（如 msisdn）、item_params（JSON）、slot_id、status、resp_code/desc、rel_start_time/rel_end_time、ext_id（外部相关单号）…
- time_slot（沿用 qos_defbearer_batch_time_slot）：
  - 配置开始/结束、容量（execute_count）、状态、execute_time …
- time_slot_shard_relation（沿用）：
  - 维护slot与分片index的映射。
- template_def（新增）：
  - template_id、engine（freemarker/mustache）、content（模板内容）、schema（参数校验schema）。
- executor_registry（可选）：
  - type、bean_name、version、capability元信息（支持申请/终止/查询等）。

说明：参数采用“分层覆盖”——默认模板参数 < 任务级default_params < 明细级item_params。

## 5. 执行架构与组件
- Scheduler（XXL-Job/Quartz）：
  - 固定少量Job：任务入站/解析与计划、时段执行、执行完成扫描、终止计划与执行、结果文件生成与通知、补偿处理、清理归档。
- Planner（计划器）：
  - 解析文件 -> 计算TimeSlot（基于ResourceCalendar）-> 落库 -> 回报计划完成时间。
- Dispatcher（派发器）：
  - 按时段+分片扫描明细，组成批次，派发到线程池或Kafka Topic（可演进）。
- Executor SPI（策略）：
  - 接口示例：
    - execute(itemCtx): 执行业务调用
    - terminate(itemCtx): 终止调用
    - canTerminate(policy, item/slot/taskCtx): 终止可行性判定
    - enrichTemplateParams(task,item): 参数装配
- Aggregator（汇聚器）：
  - 汇总明细、生成结果文件（SFTP/OSS）与回调；补偿（Outbox + Kafka + 幂等）
- Guardrail（护栏）：
  - 幂等（唯一键/幂等键）、限流/熔断、重试（指数退避）、分布式锁（Redis/DB）、超时与补偿。
- Observability：
  - 统一打点（QPS、耗时、成功率、在途、积压、每省并发）、日志追踪（traceId/planId/itemKey）。

## 6. 与现有思路的对齐与改进
1) “主表通用 + 明细个性 + 模板渲染”的思路是好主意：
- 建议：
  - 明确模板引擎与参数校验Schema（JSON Schema）
  - 参数合并顺序与可见性策略固化到公共库
  - 扩展字段以JSON为主，避免频繁改表

2) “多个XXL-Job扫描处理不同类型任务 + 启动时扫描注解注册执行器”：
- 建议：
  - 控制Job种类到“功能职责”维度（计划、执行、终止、汇总、补偿…），而非“任务类型”维度，减少Job膨胀。
  - 任务类型差异用“执行器SPI + 注解注册”解决：@BatchTaskType("defbearer.apply")；运行期通过task_type动态分发。

3) “接收->解析->计算时段（个性/通用并发）->计划完成通知->到时按时段执行->结果文件->结束通知->终止流程”：
- 建议：
  - 引入ResourceCalendar与CapacityProvider，支持“月底前7天特例/夜间容量为0”等策略可配置。
  - 终止策略：在任务类型层定义TermPolicy：
    - not_allowed_after_done / allowed_if_running / allowed_always（按类型配置）
  - 计划与执行分离，计划结果不可回滚；执行按slot/分片“租约”模型避免并发冲突。

## 7. 关键设计要点
- 幂等：
  - 任务入站：message_id + req_source 唯一；明细：plan_id + item_key 唯一。
  - 外部调用：传入幂等键（planId + itemKey），重放安全。
- 扫描与租约：
  - slot与item扫描更新采用“乐观锁 + 领取标记 + 租约超时回收”。
- 重试/补偿：
  - 结果通知、外部调用失败走Outbox + Kafka补偿；DLQ预警。
- 速率/并发控制：
  - 省分/任务/全局维度限流；时段容量由CapacityProvider决定，窗口固定（如5分钟）。
- 文件：
  - SFTP沿用；建议抽象为FileStore接口，后续可支持OSS/MinIO。
- 归档与数据保留：
  - 继续ClickHouse归档策略；清理任务按状态和时间窗口。

## 8. PlantUML 流程

### 8.1 任务接收与计划
```plantuml
@startuml
participant API as api
participant Planner as plan
database MySQL as db
queue Kafka as mq
participant File as file

api->plan: 接收请求(含模板/通用参数/文件路径)
plan->file: 文件校验与读取样本
plan->db: 幂等校验/创建Task(status=RECEIVED)
plan->plan: 解析文件计数
plan->plan: 计算TimeSlot(基于ResourceCalendar)
plan->db: 写入TimeSlot/更新Task为PENDING
plan->mq: 发送"计划完成"通知(Outbox)
api<--plan: 返回planId
@enduml
```

### 8.2 按时段执行
```plantuml
@startuml
participant SlotScanner as scan
queue ExecPool as pool
database MySQL as db
participant Executor as exe

scan->db: 拉取当前应执行slot(可租约)
scan->db: 领取slot并标记执行中
loop 直到达到slot配额
  scan->db: 分片扫描TaskItem(status=PENDING)
  scan->pool: 提交执行任务
  pool->exe: execute(itemCtx)
  exe->db: 落明细结果(含幂等键)
end
scan->db: 完成slot(若超时则部分完成)
@enduml
```

### 8.3 终止流程
```plantuml
@startuml
participant TermAPI as api
participant TermPlan as tplan
participant TermExec as texe
database MySQL as db
queue Kafka as mq

api->tplan: 发起终止(带策略校验)
tplan->db: 校验task状态与TermPolicy
alt 可终止
  tplan->db: 标记Task为TERMINATING/slot为TERMINATE
  tplan->mq: 发送预计完成时间通知
  texe->db: 扫描成功明细并逐个terminate
  texe->db: 汇总并生成终止结果文件
  texe->mq: 上报终止结果
else 不可终止
  api<-tplan: 返回不可终止原因
end
@enduml
```

## 9. 与现有方案的兼容
- 表：沿用当前表并增补泛化字段（task_type、default_params、item_params、template_id等）。
- 流程：现有状态机/清理/归档/通知流程可直接映射到通用引擎的“计划/执行/汇聚/终止/补偿”。
- 调度：保持XXL-Job为触发入口，逐步将“按类型的Job”收敛为“按职能的Job”。

## 10. 开放问题（请确认）
1) 任务类型清单与差异点：当前除“默载申请/终止”外，还有哪些类型？对外系统与协议形态？
2) 规模与SLA：单任务最大明细量（当前提到最高500万），可接受的计划时长/执行窗口？
3) 终止策略：是否按任务类型配置TermPolicy？哪些类型已知“执行完毕后不可终止”？
4) 模板：是否统一FreeMarker？模板与参数的Schema是否需要平台化（在线校验/预览）？
5) 幂等键：外部系统是否支持传入业务幂等键？若不支持，是否接受我们在边界做去重以抵抗重放？
6) 执行分发：短期是否继续线程池直连方式；是否考虑逐步引入Kafka分发以提升弹性与解耦？
7) 文件存储：SFTP是否固定？未来是否需要兼容对象存储（生成大文件更友好/断点续传）？
8) 监控：是否需要对接现有监控平台（Prometheus/ELK/企业短信告警）？
9) 资源日历来源：PCF/省份并发规则的权威来源与变更流程？是否需要热更新？
10) 兼容约束：是否必须完全不改现有表结构；可以增加字段吗？是否可接受新增2~3张“模板/注册/配置”表？

—— 以上为v0.1提案，请审阅后我再据此细化为“接口定义 + 状态机 + 表结构变更DDL + 任务/执行器SPI规范 + 样例模板/Schema + 运维SOP”。

