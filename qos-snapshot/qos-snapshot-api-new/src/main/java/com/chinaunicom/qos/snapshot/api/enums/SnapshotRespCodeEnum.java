package com.chinaunicom.qos.snapshot.api.enums;

import lombok.Getter;

@Getter
public enum SnapshotRespCodeEnum {
    R_SUCCESS(0, "成功"),
    R_SYSTEM_ERROR(129500, "系统异常，请稍后再试"),
    R_PARAMETER_ERROR(129400, "请求参数错误"),
    R_CM_SERVICE_NOT_EXIST(129404, "通信服务不存在"),
    R_SNAPSHOT_VERSION_NOT_EXIST(129460, "快照版本不存在");

    final Integer code;
    final String message;

    SnapshotRespCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
