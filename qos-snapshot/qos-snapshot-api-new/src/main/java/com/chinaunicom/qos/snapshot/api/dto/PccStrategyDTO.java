package com.chinaunicom.qos.snapshot.api.dto;

import lombok.Data;

@Data
public class PccStrategyDTO {

    // 专载AFID 默载ServiceID
    private String afid;

    // 5QI/QCI
    private String qosLevel;

    // PCC策略名称
    private String pccStrategyName;

    // 网络承载类型，0-专载、1-默载、2-AMPCF
    private Integer bearerMode;

    // 核心网子类型，0-人网、1-物网
    private Integer coreNetworkSubtype;

    // 加限速类型，0-加速、1-限速、2-其它
    private Integer speedType;

    // 支持业务网络类型，0-4G、1-5G
    private Integer networkMode;

}
