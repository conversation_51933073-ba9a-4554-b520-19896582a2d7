package com.chinaunicom.qos.snapshot.api.dto;

import com.chinaunicom.qos.common.enums.NetworkModeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CmsSnapshotDTO {
    // 快照版本号
    private String snapshotVersion;

    // 通信服务ID
    private String cmServiceId;

    // 通信服务名称
    private String cmServiceName;

    // 通信服务状态，0启用, -1关闭
    private Integer cmServiceStatus;

    // 核心网子类型，0-人网, 1-物网
    private Integer coreNetworkSubtype;

    // 支持业务网络类型，0-4G，1-5G，2-4&5G
    private Integer networkMode;

    // 承载类型，0-专载，1-默载，2-AMPCF
    private Integer bearerMode;

    // 加限速类型，0-加速、1-限速、2-其它
    private Integer speedType;

    // 归属省列表
    private List<Integer> homeProvinceList;

    // PCC策略信息
    private List<PccStrategyDTO> pccStrategyList;

    // 通信服务规则配置列表
    private List<CmServiceCheckConfigDTO> checkConfigList;

    // 专载通信服务速率配置
    private Map<NetworkModeEnum, BitrateConfigDTO> bitrateConfigMap;

    // 专载通信服务分省配置网元信息
    private List<CmServiceNeConfigDTO> neConfigList;

    // 专载通信服务会话重建配置信息
    private RebuildConfigDTO rebuildConfig;

    public String getAfid(Integer networkMode) {
        for (PccStrategyDTO pccStrategy : this.pccStrategyList) {
            if (networkMode.equals(pccStrategy.getNetworkMode())) {
                return pccStrategy.getAfid();
            }
        }
        return null;
    }

}
