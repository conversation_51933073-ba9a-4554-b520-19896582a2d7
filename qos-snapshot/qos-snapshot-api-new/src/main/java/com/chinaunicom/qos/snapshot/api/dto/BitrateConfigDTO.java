package com.chinaunicom.qos.snapshot.api.dto;

import com.chinaunicom.qos.common.annotations.FieldDesc;
import lombok.Data;

@Data
public class BitrateConfigDTO {

    @FieldDesc(desc = "上行峰值速率，单位Kbps")
    private Integer uplinkMBR;

    @FieldDesc(desc = "下行峰值速率，单位Kbps")
    private Integer downlinkMBR;

    @FieldDesc(desc = "上行最小保障速率，单位Kbps")
    private Integer uplinkGBR;

    @FieldDesc(desc = "下行最小保障速率，单位Kbps")
    private Integer downlinkGBR;

}
