#!/bin/bash

# 子脚本：执行实际的部署逻辑
set -o pipefail

# 接收参数
shell_env=$1
file_name=$2
resource_name=$3
resource_type=$4

echo "开始进行 $resource_name 服务，$shell_env 环境部署"

if [[ "$shell_env" == "gray" ]]; then
  read -p "是否仅删除灰度资源? (y/n): " only_delete
  if [[ "$only_delete" == "y" || "$only_delete" == "Y" ]]; then
    if [[ "$resource_name" != *"gray"* ]]; then
      echo "错误: 只能删除带有'gray'关键字的资源!"
      exit 1
    fi
    # 交互式输入参数
    read -p "请输入K8s环境名称(dev|test|gray|prod|backup):" env
    if [ -z "$env" ]; then
      echo "错误: K8s环境名称不能为空!"
      exit 1
    fi
    echo "切换到K8s环境: $shell_env"
    kubectl config use-context "$shell_env"
    # 获取当前 kubectl 配置的环境
    current_env=$(kubectl config current-context)
    if [ "$current_env" != "$env" ]; then
      echo "错误: 当前K8s环境 ($current_env)与期望环境($env)不匹配!"
      exit 1
    fi
    echo "删除灰度资源: $resource_name"
    kubectl delete "$resource_type"  "$resource_name"
    if [ $? -eq 0 ]; then
      echo "灰度资源已删除"
      exit 0
    else
      echo "灰度资源删除失败，请检查日志"
      exit 1
    fi
  fi
fi

# 交互式输入参数
read -p "请输入K8s环境名称(dev|test|gray|prod|backup):" env
if [ -z "$env" ]; then
  echo "错误: K8s环境名称不能为空!"
  exit 1
fi

read -p "请输入Build内容(格式为服务名:镜像tagId): " build_number
if [ -z "$build_number" ]; then
  echo "错误: Build内容不能为空!"
  exit 1
fi
build_number=$(echo "$build_number" | xargs)
regex="^[a-zA-Z0-9_-]+:[0-9]+$"
if [[ ! $build_number =~ $regex ]]; then
  echo "错误: Build内容格式不正确! 应为 '服务名:镜像tagId',从流水线中获取"
  exit 1
fi

IFS=':' read -r service_name build_number <<< "$build_number"
if [[ "$service_name" != "$resource_name" && "${service_name}-gray" != "$resource_name" ]]; then
  echo "错误: 需要部署的服务名 '$service_name' 与实际部署资源名 '$resource_name' 不匹配!"
  exit 1
fi

echo "切换到K8s环境: $shell_env"
kubectl config use-context "$shell_env"

# 获取当前 kubectl 配置的环境
current_env=$(kubectl config current-context)
if [ "$current_env" != "$env" ]; then
  echo "错误: 当前K8s环境 ($current_env)与期望环境($env)不匹配!"
  exit 1
fi

if [[ "$shell_env" == "backup" || "$shell_env" == "gray" || "$shell_env" == "prod" ]]; then
    read -p "正在进行 $shell_env 环境 $resource_name 部署，请慎重确认是否部署? (y/n): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
      echo "部署取消"
      exit 0
    fi
fi

if [[ "$shell_env" != "gray" ]]; then
  # 获取当前正在运行的镜像（忽略错误）
  if kubectl get $resource_type "$resource_name" >/dev/null 2>&1; then
    current_image=$(kubectl get $resource_type "$resource_name" -o jsonpath='{.spec.template.spec.containers[0].image}')
    echo "当前运行的镜像: $current_image"
  else
    echo "提示: 资源($resource_name)不存在，可能尚未部署。"
  fi
fi

echo "环境匹配成功，开始部署($file_name)"

# 部署到 Kubernetes
sed "s/#{Build.BuildNumber}#/$build_number/g" "$file_name" | kubectl apply -f - 2>&1 | grep -v "Warning: resource .* is missing the kubectl.kubernetes.io/last-applied-configuration annotation"

if [ $? -eq 0 ]; then
  echo "部署完成"

  # If deploying to prod, also deploy to backup environment
  if [[ "$shell_env" == "prod" && "$resource_name" != "qos-admin-vue" ]]; then

    read -p "正在进行备份环境 $resource_name 部署，请慎重确认是否部署? (y/n): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
      echo "部署取消"
      exit 0
    fi

    backup_file="${file_name/-prod.yml/-backup.yml}"
    echo "开始同步部署备份环境($backup_file)"

    # Switch to backup context
    kubectl config use-context "backup"
    current_env=$(kubectl config current-context)
    if [ "$current_env" != "backup" ]; then
      echo "错误: 当前K8s环境 ($current_env)与期望环境(backup)不匹配!"
      exit 1
    fi

    # Deploy to backup environment
    sed "s/#{Build.BuildNumber}#/$build_number/g" "$backup_file" | kubectl apply -f - 2>&1 | grep -v "Warning: resource .* is missing the kubectl.kubernetes.io/last-applied-configuration annotation"
    if [ $? -eq 0 ]; then
      echo "备份环境部署完成"
    else
      echo "备份环境部署失败，请检查日志"
      exit 1
    fi

    # Switch back to original context
    kubectl config use-context "$shell_env"
  fi
else
  echo "部署失败，请检查日志"
  exit 1
fi

if [[ "$shell_env" == "gray" ]]; then
  # 询问是否删除灰度资源
  read -p "是否删除灰度资源? (y/n): " delete_confirm
  if [[ "$delete_confirm" == "y" || "$delete_confirm" == "Y" ]]; then
    if [[ "$resource_name" != *"gray"* ]]; then
      echo "错误: 只能删除带有'gray'关键字的资源!"
      exit 1
    fi
    echo "删除灰度资源: $resource_name"
    kubectl delete $resource_type  "$resource_name"
    if [ $? -eq 0 ]; then
      echo "灰度资源已删除"
      exit 0
    else
      echo "灰度资源删除失败，请检查日志"
      exit 1
    fi
  fi
fi