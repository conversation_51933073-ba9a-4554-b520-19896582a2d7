package com.chinaunicom.qos.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-2-28 14:08
 */

@Getter
public enum CmServiceCheckKeyEnum {
    TARGET_IP_WHITE("targetIpWhite", "目标IP白名单校验"),
    TARGET_IP_BLACK("targetIpBlack", "目标IP黑名单校验"),
    VISIT_PROVINCE("visitProvinceCheck", "拜访省校验"),
    DURATION("durationCheck", "时长校验"),
    DEL_TIME("delTimeCheck", "未达删除时限校验"),
    ALLOW_5G("allow5GConnect", "是否允许5G接入"),
    TARGET_IP_MAX_COUNT("targetIpMaxCount", "目标IP个数限制"),
    QCI_CHECK("qciCheck", "是否校验5QI/QCI等级");


    final String key;
    final String desc;

    CmServiceCheckKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static CmServiceCheckKeyEnum findByKey(String key) {
        if (StringUtils.isBlank(key)){
            return null;
        }

        for (CmServiceCheckKeyEnum e : CmServiceCheckKeyEnum.values()) {
            if (e.key.equals(key)){
                return e;
            }
        }

        return null;
    }
}
