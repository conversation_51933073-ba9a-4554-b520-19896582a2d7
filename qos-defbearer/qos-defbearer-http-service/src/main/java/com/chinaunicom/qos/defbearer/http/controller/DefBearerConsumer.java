package com.chinaunicom.qos.defbearer.http.controller;

import com.chinaunicom.qos.defbearer.api.dto.AddDefBearerDTO;
import com.chinaunicom.qos.defbearer.api.dto.DefBearerOrderDTO;
import com.chinaunicom.qos.defbearer.api.request.*;
import com.chinaunicom.qos.defbearer.api.service.DefBearerService;
import com.chinaunicom.qos.common.response.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@SuppressWarnings({"java:S3740","java:S6212"})
@RequestMapping("api/defbearer")
public class DefBearerConsumer {
    @DubboReference(timeout = 10000)
    private DefBearerService defService;

    private final String ORDER_SOURCE = "consumerTest"; //NOSONAR

    @RequestMapping("add")
    public void add(@RequestParam(value = "msisdn", required = true) String msisdn,
                    @RequestParam(value = "cmServiceId", required = true) String cmServiceId,
                    @RequestParam(value = "userEndTime", required = false) String userEndTime) {
        AddDefBearerReq req = new AddDefBearerReq();
        req.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        req.setMsisdn(msisdn);
        req.setCmServiceId(cmServiceId);
        req.setUserEndTime(userEndTime);
        req.setOrderSource(ORDER_SOURCE);
        req.setQosProductId(880082);
        Resp<AddDefBearerDTO> resp = defService.add(req);
        log.info("调用默载申请，req={}, resp={}", req, resp);
    }

    @RequestMapping("modify")
    public void modify(@RequestParam(value = "msisdn", required = true) String msisdn,
                    @RequestParam(value = "orderId", required = true) Long orderId,
                    @RequestParam(value = "userEndTime", required = false) String userEndTime) {
        ModifyDefBearReq req = new ModifyDefBearReq();
        req.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        req.setMsisdn(msisdn);
        req.setOrderId(orderId);
        req.setUserEndTime(userEndTime);
        req.setOrderSource(ORDER_SOURCE);
        Resp<Void> resp = defService.modify(req);
        log.info("调用默载修改，req={}, resp={}", req, resp);
    }

    @RequestMapping("delete")
    public void delete(@RequestParam(value = "msisdn", required = true) String msisdn,
                       @RequestParam(value = "orderId", required = true) Long orderId) {
        TerminateDefBearerReq req = new TerminateDefBearerReq();

        req.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        req.setMsisdn(msisdn);
        req.setOrderId(orderId);
        req.setOrderSource(ORDER_SOURCE);
        Resp<Void> resp = defService.terminate(req);
        log.info("调用默载按订单号终止，req={}, resp={}", req, resp);
    }

    @RequestMapping("ugdel")
    public void delete(@RequestParam(value = "msisdn", required = true) String msisdn,
                       @RequestParam(value = "cmServiceId", required = true) String cmServiceId) {
        TerminateDefBearerByMsisdnReq req = new TerminateDefBearerByMsisdnReq();

        req.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        req.setMsisdn(msisdn);
        req.setOrderSource(ORDER_SOURCE);
        req.setCmServiceId(cmServiceId);
        Resp<Void> resp = defService.terminateByMsisdn(req);
        log.info("调用默载按用户终止，req={}, resp={}", req, resp);
    }

    @RequestMapping("fastDelete")
    public void fastDelete(@RequestParam(value = "msisdn", required = true) String msisdn,
                       @RequestParam(value = "orderId", required = true) Long orderId) {
        TerminateDefBearerReq req = new TerminateDefBearerReq();

        req.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        req.setMsisdn(msisdn);
        req.setOrderId(orderId);
        req.setOrderSource(ORDER_SOURCE);
        Resp<Void> resp = defService.terminateBySystem(req);
        log.info("调用默载系统终止，req={}, resp={}", req, resp);
    }

    @RequestMapping("query")
    public void queryOrders(@RequestParam(value = "msisdn", required = true) String msisdn,
                           @RequestParam(value = "cmServiceIds", required = false) String cmServiceIds) {
        QueryDefBearerOrdersReq req = new QueryDefBearerOrdersReq();
        req.setMsisdn(msisdn);
        if(cmServiceIds != null && !cmServiceIds.isEmpty()) {
            List<String> cmServiceIdList = Arrays.asList(cmServiceIds.split(","));
            req.setCmServiceIdList(cmServiceIdList);
        }
        Resp<List<DefBearerOrderDTO>> resp = defService.queryValidOrders(req);
        log.info("调用默载订单查询，req={}, resp={}", req, resp);
    }
}
