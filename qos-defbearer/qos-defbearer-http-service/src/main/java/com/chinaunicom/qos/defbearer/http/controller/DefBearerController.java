package com.chinaunicom.qos.defbearer.http.controller;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.api.dto.AddDefBearerDTO;
import com.chinaunicom.qos.defbearer.api.dto.DefBearerOrderDTO;
import com.chinaunicom.qos.defbearer.api.request.*;
import com.chinaunicom.qos.defbearer.api.service.DefBearerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/qos/defbearer/")
public class DefBearerController {
    @DubboReference(timeout = 25000)
    private DefBearerService defService;

    @PostMapping("/queryOrders")
    public Resp<List<DefBearerOrderDTO>> queryOrders(@RequestBody QueryDefBearerOrdersReq req) {
        log.info("queryOrders req:{}", req);
        return defService.queryValidOrders(req);
    }

    @PostMapping(value = "/terminateBySystem")
    public Resp<Void> terminateBySystem(@RequestBody TerminateDefBearerReq req) {
        log.info("terminateBySystem req:{}", req);
        return defService.terminateBySystem(req);
    }

    @PostMapping(value = "/add")
    public Resp<AddDefBearerDTO> add(@RequestBody AddDefBearerReq req) {
        log.info("add req:{}", req);
        return defService.add(req);
    }

    @PostMapping(value = "/modify")
    public Resp<Void> modify(@RequestBody ModifyDefBearReq req) {
        log.info("modify req:{}", req);
        return defService.modify(req);
    }

    @PostMapping(value = "/terminate")
    public Resp<Void> terminate(@RequestBody TerminateDefBearerReq req) {
        log.info("terminate req:{}", req);
        return defService.terminate(req);
    }

    @PostMapping(value = "/terminateByMsisdn")
    public Resp<Void> terminateByMsisdn(@RequestBody TerminateDefBearerByMsisdnReq req) {
        log.info("terminateByMsisdn req:{}", req);
        return defService.terminateByMsisdn(req);
    }

}
