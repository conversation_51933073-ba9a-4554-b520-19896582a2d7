server:
  port: 37101

spring:
  profiles:
    active: @profile.env@
  application:
    name: qos-defbearer-http-service

dubbo:
  application:
    name: qos-defbearer-http-service
    logger: slf4j
    qos-enable: false
    check-serializable: false
    serialize-check-status: DISABLE
  metrics:
    protocol: prometheus


management:
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${dubbo.application.name}
    distribution:
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
      percentiles-histogram:
        http.server.requests: true
