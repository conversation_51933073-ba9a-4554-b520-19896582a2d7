FROM zhongyuan-registry.cucloud.cn/qos/openjdk:21-jdk-ubuntu

MAINTAINER yuxing <<EMAIL>>

# Remember to change the port according to the RPC protocol you select
EXPOSE 37101

# copy the JAR file into the root and rename
ADD qos-defbearer/qos-defbearer-http-service/target/qos-defbearer-http-service-1.0.0-SNAPSHOT.jar qos-defbearer-http-service.jar


# Run java with the jar file when the container starts up
ENTRYPOINT exec java $JAVA_OPTS -jar qos-defbearer-http-service.jar