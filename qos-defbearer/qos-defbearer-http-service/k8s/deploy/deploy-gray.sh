#!/bin/bash

# 主脚本：定义变量并调用子脚本
set -o pipefail

# 定义变量
shell_env="gray"
script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
file_name="$script_dir/../deployment-gray.yml"
resource_name="qos-defbearer-http-service-gray"
resource_type="deployment"

#查找根目录
find_slice_qos_dir() {
    local current_dir="$1"
    while [[ "$current_dir" != "/" ]]; do
        if [[ -d "$current_dir/slice-qos" ]]; then
            echo "$current_dir/slice-qos"
            return 0
        fi
        current_dir="$(dirname "$current_dir")"
    done
    return 1
}

#获取子脚本路径
slice_qos_dir=$(find_slice_qos_dir "$script_dir")
if [[ -z "$slice_qos_dir" ]]; then
    echo "未找到 slice-qos 目录"
    exit 1
fi

# 构建子脚本的路径
sub_script_path="$slice_qos_dir/deploy/deploy_sub.sh"

# 检查子脚本是否存在
if [ -f "$sub_script_path" ]; then
    echo "子脚本文件存在: $sub_script_path"
    # shellcheck disable=SC1090
    source "$sub_script_path" "$shell_env" "$file_name" "$resource_name" "$resource_type"
else
    echo "子脚本文件不存在: $sub_script_path"
    exit 1
fi