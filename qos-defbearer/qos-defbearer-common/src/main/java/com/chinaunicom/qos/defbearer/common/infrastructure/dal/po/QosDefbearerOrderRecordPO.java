package com.chinaunicom.qos.defbearer.common.infrastructure.dal.po;

import com.chinaunicom.qos.common.infrastructure.po.OrderRecordPO;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class QosDefbearerOrderRecordPO extends OrderRecordPO {
    /**
     * 用户期望的结束时间
     */
    private String userEndTime;

    /**
     * 速率类型
     */
    private Integer speedType;

    /**
     * 承载类型 1-默载 2-AM-PCF
     */
    private Integer bearerMode;

    /**
     * 产品调用优先级
     */
    private BigDecimal priority;
}
