package com.chinaunicom.qos.defbearer.common.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonConstants {

    //人网默载网元ID
    public static final String NE_ID_PEOPLE = "iom";

    //物网默载网元ID
    public static final String NE_ID_IOT = "iot";

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class KafkaTopics {
        //默载流水消息
        public static final String TOPIC_DEFBEARER_RECORD = "topic_qos_defbearer_record";
        //默载订单归档消息
        public static final String TOPIC_DEFBEARER_ORDER = "topic_qos_defbearer_order";
        //默载策略冲突消息
        public static final String TOPIC_DEFBEARER_CONFLICT = "topic_qos_defbearer_conflict";
        //AM-PCF策略冲突消息
        public static final String TOPIC_AMPCF_CONFLICT = "topic_qos_ampcf_conflict";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class KafkaConfig {
        public static final String SECURITY_PROTOCOL = "security.protocol";
        public static final String SASL_MECHANISM = "sasl.mechanism";
        public static final String SASL_JAAS_CONFIG = "sasl.jaas.config";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class DelayTask {
        public static final int TASK_CREATED = 0;
        public static final int TASK_NOT_CREATED = -1;
        //加速的延时任务配置
        public static final String SPEED_UP_EXECUTOR_HANDLER = "defbearer_speed_up";
        public static final int SPEED_UP_CORE_POOL_SIZE = 5;
        public static final int SPEED_UP_MAX_POOL_SIZE = 5;
        public static final int SPEED_UP_QUEUE_SIZE = 200;
        //物网加速的延时任务配置
        public static final String IOT_SPEED_UP_EXECUTOR_HANDLER = "iot_defbearer_speed_up";
        public static final int IOT_SPEED_UP_CORE_POOL_SIZE = 20;
        public static final int IOT_SPEED_UP_MAX_POOL_SIZE = 20;
        public static final int IOT_SPEED_UP_QUEUE_SIZE = 200;
        //限速的延时任务配置
        public static final String SPEED_DOWN_EXECUTOR_HANDLER = "defbearer_speed_down";
        public static final int SPEED_DOWN_CORE_POOL_SIZE = 100;
        public static final int SPEED_DOWN_MAX_POOL_SIZE = 100;
        public static final int SPEED_DOWN_QUEUE_SIZE = 1000;
        //物网限速的延时任务配置
        public static final String IOT_SPEED_DOWN_EXECUTOR_HANDLER = "iot_defbearer_speed_down";
        public static final int IOT_SPEED_DOWN_CORE_POOL_SIZE = 100;
        public static final int IOT_SPEED_DOWN_MAX_POOL_SIZE = 100;
        public static final int IOT_SPEED_DOWN_QUEUE_SIZE = 1000;
        //AM-PCF的延时任务配置
        public static final String AM_PCF_EXECUTOR_HANDLER = "defbearer_am_pcf";
        public static final int AM_PCF_CORE_POOL_SIZE = 100;
        public static final int AM_PCF_MAX_POOL_SIZE = 100;
        public static final int AM_PCF_QUEUE_SIZE = 1000;

        public static final String USER_ID = "userId";
        public static final String ORDER_ID = "orderId";
        public static final String AFID = "afid";
        public static final String EXPECTED_EXEC_TIME = "expectedExecTime";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OrderStatus {
        //生效中
        public static final int ORDER_VALID = 0;
        //已终止
        public static final int ORDER_TERMINATED = -1;

    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OperSource {
        //用于默载到时删除
        public static final String TIMED_TERMINATION = "timedTermination";
        //用于策略覆盖
        public static final String CONFLICT_HANDLER = "ConflictHandler";
        //用于同通信服务删除
        public static final String SAME_CS_DEL = "SameCsDel";
        //用于超时未归档
        public static final String OVERDUE_HANDLER = "OverdueHandler";
        //用于调老系统的请求来源以及开户
        public static final String DEFBEARER = "DefBearer";
        //用于重复申请时终止原订单
        public static final String ORDER_REPLACEMENT = "orderReplacement";
        //用于恢复订单
        public static final String RESUME_ORDER = "ResumeOrder";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class RedisKey {
        public static final String REJECT_DEF_BEARER_USER_FAIL_ADD = "reject:defbearer:userfail:add:";
        public static final String ZSET_DEF_BEARER_USER_FAIL_ADD = "zset:defbearer:userfail:add:";
    }

}
