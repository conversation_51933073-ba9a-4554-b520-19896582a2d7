package com.chinaunicom.qos.defbearer.common.model;

import lombok.Data;

@Data
public class QosOrderBO {
    private boolean isPccOrder; //是否老系统的订单，true代表是老系统的订单
    private String id;  //新系统中的orderId，老系统中的correlationId
    private String cmServiceId; //调老系统删除接口需要
    private String msisdn;
    private String imsi;
    private String neId;
    private Integer userType;
    private Integer signNetworkType;
    private Integer bearerMode;
}
