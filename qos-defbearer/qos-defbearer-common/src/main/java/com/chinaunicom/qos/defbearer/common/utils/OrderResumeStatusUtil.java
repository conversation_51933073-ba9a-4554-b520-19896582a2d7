package com.chinaunicom.qos.defbearer.common.utils;

import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-5-15 10:32
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderResumeStatusUtil {

    @SuppressWarnings("java:S1067")
    public static boolean resumeFailed(Integer respCode) {
        return !(DefBearerRespCodeEnum.R_SUCCESS.getCode().equals(respCode)
                || DefBearerRespCodeEnum.USER_ORDER_EXIST.getCode().equals(respCode)
                || DefBearerRespCodeEnum.CMSERVICE_INVALID.getCode().equals(respCode)
                || DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH.getCode().equals(respCode)
                || DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT.getCode().equals(respCode));
    }

    public static boolean stopResumeProcess(Integer respCode, BigDecimal taskOrderPriority) {
        return DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT.getCode().equals(respCode) ||
                (DefBearerRespCodeEnum.R_SUCCESS.getCode().equals(respCode) && taskOrderPriority!=null);
    }

    @SuppressWarnings("java:S1067")
    public static boolean processNextResumeTask(Integer respCode, BigDecimal taskOrderPriority) {
        return (DefBearerRespCodeEnum.R_SUCCESS.getCode().equals(respCode) && taskOrderPriority==null)
                || DefBearerRespCodeEnum.USER_ORDER_EXIST.getCode().equals(respCode)
                || DefBearerRespCodeEnum.CMSERVICE_INVALID.getCode().equals(respCode)
                || DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH.getCode().equals(respCode);
    }

    public static boolean resumeTaskInvalid(Integer resumeRespCode) {
        return DefBearerRespCodeEnum.USER_ORDER_EXIST.getCode().equals(resumeRespCode)
                || DefBearerRespCodeEnum.CMSERVICE_INVALID.getCode().equals(resumeRespCode)
                || DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH.getCode().equals(resumeRespCode);
    }

    /**
     * 除了业务冲突、重复订单、通信服务无效、订购失效以外的其它失败，认为是需要重试的
     */
    public static boolean isRetryableFailure(Integer respCode) {
        return !(DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT.getCode().equals(respCode)
                || DefBearerRespCodeEnum.USER_ORDER_EXIST.getCode().equals(respCode)
                || DefBearerRespCodeEnum.CMSERVICE_INVALID.getCode().equals(respCode)
                || DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH.getCode().equals(respCode));
    }

}
