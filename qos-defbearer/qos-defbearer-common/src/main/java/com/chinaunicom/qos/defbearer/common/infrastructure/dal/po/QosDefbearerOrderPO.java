package com.chinaunicom.qos.defbearer.common.infrastructure.dal.po;

import lombok.Data;

import java.util.Date;

@Data
public class QosDefbearerOrderPO {
    private Long orderId;
    private String userId;
    private Integer qosProductId;
    private String cmServiceId;
    private Integer status;
    private Date startTime;
    private Date endTime;
    private Date userEndTime;
    private Long duration;
    private String msisdn;
    private String imsi;
    private Integer homeProvince;
    private Integer visitProvince;
    private Integer userType;
    private Integer signNetworkType;
    private String afid;
    private Integer bearerMode;
    private Integer speedType;
    private String neId;
    private Integer delayTaskFlag;
    private String snapshotVersion;
    private Date createTime;
    private Date updateTime;
    private String delSource;
}
