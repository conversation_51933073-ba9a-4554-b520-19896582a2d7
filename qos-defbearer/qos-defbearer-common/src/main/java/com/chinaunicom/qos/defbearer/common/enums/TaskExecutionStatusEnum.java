package com.chinaunicom.qos.defbearer.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-4-24 10:14
 */

@Getter
public enum TaskExecutionStatusEnum {
    TO_BE_EXECUTED(0, "待执行"),
    FAIL(1, "失败"),
    REQUIRE_MANUAL_INTERVENTION(2, "失败次数过多需要人工介入");

    final Integer status;
    final String desc;

    TaskExecutionStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
