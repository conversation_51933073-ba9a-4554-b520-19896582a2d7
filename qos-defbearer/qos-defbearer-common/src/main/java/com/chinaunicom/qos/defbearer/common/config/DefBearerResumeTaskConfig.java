package com.chinaunicom.qos.defbearer.common.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-4-24 17:13
 */

@Component
@NacosConfigurationProperties(prefix = "defbearer", dataId = "resume-task-config", type = ConfigType.YAML,
        autoRefreshed = true)
@Data
public class DefBearerResumeTaskConfig {

    /***
     * 失败任务的最大重试次数
     */
    private Integer maxRetryTimes;

    /***
     * 失败任务重试的初始延迟时间，单位：分钟
     */
    private Integer initialDelayMinutes;

    /***
     * 失败任务的最大重试时间间隔，单位：分钟
     */
    private Integer maxRetryIntervalMinutes;

    /***
     * 任务超期的时间（理论上恢复处理都应该在订单终止的时候触发，防止终止的时候触发异常，增加超期的定时任务），单位：分钟
     */
    private Integer taskOverdueMinutes;

    /***
     * 失败重试任务、超期任务，每次批量查询的数量
     */
    private Integer batchSize;

}
