package com.chinaunicom.qos.defbearer.common.infrastructure.dal.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-4-23 16:30
 */

@Data
public class QosDefBearerResumeTaskPO {

    private Long originOrderId;
    private String userId;
    private Integer qosProductId;
    private String cmServiceId;
    private Date resumeTime;
    private Date userEndTime;
    private String msisdn;
    private String imsi;
    private Integer homeProvince;
    private Integer visitProvince;
    private Integer userType;
    private Integer signNetworkType;
    private String afid;
    private Integer bearerMode;
    private Integer speedType;
    private String snapshotVersion;
    private Date nextExecuteTime;
    private Integer executeStatus;
    private Integer executeFailRetryCount;
    private Integer executeCode;
    private String executeDesc;
    private Date createTime;
    private Date updateTime;

}
