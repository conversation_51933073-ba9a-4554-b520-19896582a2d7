package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.dedbearer.api.dto.DedBearerOrderDTO;
import com.chinaunicom.qos.dedbearer.api.enums.DedBearerRespCodeEnum;
import com.chinaunicom.qos.dedbearer.api.request.QueryDedBearerReq;
import com.chinaunicom.qos.dedbearer.api.service.DedBearerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-8-23 14:12
 */
@Slf4j
@Service
public class DedBearerProxy {
    @DubboReference(timeout = 1000)
    private DedBearerService dedBearerService;

    public List<DedBearerOrderDTO> queryDedBearerOrders(String userId) {
        var req = new QueryDedBearerReq();
        req.setMsisdn(userId);
        List<DedBearerOrderDTO> orders = null;
        try {
            log.info("开始调用专载服务查询专载订单，req={}", req);
            Resp<List<DedBearerOrderDTO>> resp = dedBearerService.queryValidOrders(req);
            log.info("调用专载服务查询专载订单结束，resp={}", JsonUtil.obj2String(resp));
            if(DedBearerRespCodeEnum.R_SUCCESS.getCode().equals(resp.getCode())) {
                orders = resp.getData();
            }
        } catch (Exception e) {
            log.error("调用专载服务查询生效中订单异常！", e);
        }
        return orders;
    }
}
