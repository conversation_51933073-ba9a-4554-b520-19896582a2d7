package com.chinaunicom.qos.defbearer.infrastructure.dal;

import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-22 17:31
 */
public interface IDefBearerResumeTaskRepo {

    int deleteResumeTask(Long originOrderId, String userId);

    List<QosDefBearerResumeTaskPO> queryResumeTasksByUserId(String userId);

    int updateTaskExecutionParams(QosDefBearerResumeTaskPO taskPO);

}
