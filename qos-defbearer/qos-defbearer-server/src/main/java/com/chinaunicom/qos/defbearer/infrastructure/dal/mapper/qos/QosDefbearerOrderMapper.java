package com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos;

import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface QosDefbearerOrderMapper {

    List<QosDefbearerOrderPO> queryValidOrderByUserIdCmServiceIds(@Param("userId") String userId, @Param("cmServiceIds") List<String> cmServiceIds, @Param("qosProductId") Integer qosProductId);

    List<QosDefbearerOrderPO> queryOrderByUserIdCmServiceIds(@Param("userId") String userId, @Param("cmServiceIds") List<String> cmServiceIds, @Param("qosProductId") Integer qosProductId);

    QosDefbearerOrderPO queryOrderByUserIdCmServiceId(@Param("userId") String userId, @Param("cmServiceId") String cmServiceId);

    QosDefbearerOrderPO queryOrderByUserIdOrderId(@Param("userId") String userId, @Param("orderId") Long orderId);

    List<QosDefbearerOrderPO> queryValidOrderByUserId(@Param("userId") String userId);

    int insertOrder(QosDefbearerOrderPO qosDefbearerOrder);

    int updateOrder(Map<String,Object> map);
}
