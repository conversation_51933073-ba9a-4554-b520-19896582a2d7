package com.chinaunicom.qos.defbearer.infrastructure.dal.repo;

import com.chinaunicom.qos.defbearer.infrastructure.dal.IPccCurBearRepo;
import com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.pcc.UserUserBearCurinfoMapper;
import com.chinaunicom.qos.defbearer.infrastructure.dal.po.UserUserBearCurinfoPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class PccCurBearRepo implements IPccCurBearRepo {
    @Resource
    private UserUserBearCurinfoMapper userUserBearCurinfoMapper;

    @Override
    public List<UserUserBearCurinfoPO> getPccCurBears(String userId) {
        String userId13 = userId;
        if (!userId.startsWith("86")) {
            userId13 = "86" + userId;
        }
        return userUserBearCurinfoMapper.queryValidOrderByMsisdn(userId13);
    }

    @Override
    public List<UserUserBearCurinfoPO> getPccCurBearsByStatus(String userId, String cmServiceId) {
        String userId13 = userId;
        if (!userId.startsWith("86")) {
            userId13 = "86" + userId;
        }
        return userUserBearCurinfoMapper.queryOrderByMsisdnAndCmServiceId(userId13, cmServiceId);
    }
}
