package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import com.chinaunicom.qos.user.api.enums.QueryFieldEnum;
import com.chinaunicom.qos.user.api.enums.UserResponseCodeEnum;
import com.chinaunicom.qos.user.api.request.OpenAccountReq;
import com.chinaunicom.qos.user.api.request.UserInfoReq;
import com.chinaunicom.qos.user.api.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class UserInfoProxy {
    @DubboReference(timeout = 5000, retries = 0)
    private UserInfoService userInfoService;

    @DubboReference(timeout = 25000, retries = 0)
    private UserInfoService openAccountService;


    public UserInfoDTO getUserInfo(String messageId, String msisdn) {
        UserInfoReq userInfoReq = new UserInfoReq(); //NOSONAR
        List<QueryFieldEnum> queryFields = new ArrayList<>();
        queryFields.add(QueryFieldEnum.BASE_INFO);
        queryFields.add(QueryFieldEnum.VISIT_PROVINCE);
        userInfoReq.setMessageId(messageId);
        userInfoReq.setMsisdn(msisdn);
        userInfoReq.setQueryFields(queryFields);
        Resp<UserInfoDTO> userInfoResp;
        UserInfoDTO userInfo;
        try {
            log.info("开始调用用户服务查询基础信息，req={}", userInfoReq);
            userInfoResp = userInfoService.queryUserInfo(userInfoReq);
            log.info("查询用户基础信息结束，resp={}", JsonUtil.obj2String(userInfoResp));
        } catch (Exception e) {
            log.error("调用用户服务查询用户基础信息异常！req={}", userInfoReq, e);
            throw new DefBearerException(DefBearerRespCodeEnum.QUERY_USER_EXCEPTION);
        }
        if(userInfoResp.getCode() != UserResponseCodeEnum.R_SUCCESS.getCode()) {
            log.info("用户服务查询用户基础信息返回失败！");
            throw new ProxyException(userInfoResp);
        }
        userInfo = userInfoResp.getData();
        return userInfo;
    }

    public void openAccountForUser(String messageId, String msisdn) {
        OpenAccountReq openAccountReq = new OpenAccountReq(); //NOSONAR
        openAccountReq.setMessageId(messageId);
        openAccountReq.setMsisdn(msisdn);
        openAccountReq.setForceOpen(Boolean.TRUE);
        openAccountReq.setOperSource(CommonConstants.OperSource.DEFBEARER);
        Resp<Void> openAccountResp;
        try {
            openAccountResp = openAccountService.openAccount(openAccountReq);
        } catch (Exception e) {
            log.error("调用用户服务开户异常！req={}", openAccountReq, e);
            throw new DefBearerException(DefBearerRespCodeEnum.USER_CALL_EXCEPTION);
        }
        if(openAccountResp.getCode() != UserResponseCodeEnum.R_SUCCESS.getCode()) {
            log.info("用户服务开户返回失败！req={}, resp={}", openAccountReq, openAccountResp);
            throw new ProxyException(openAccountResp);
        }
    }
}
