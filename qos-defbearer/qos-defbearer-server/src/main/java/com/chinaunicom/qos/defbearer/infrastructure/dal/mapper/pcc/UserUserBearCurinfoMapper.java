package com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.pcc;

import com.chinaunicom.qos.defbearer.infrastructure.dal.po.UserUserBearCurinfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserUserBearCurinfoMapper {

    List<UserUserBearCurinfoPO> queryValidOrderByMsisdn(@Param("msisdn") String msisdn);
    List<UserUserBearCurinfoPO> queryOrderByMsisdnAndCmServiceId(@Param("msisdn") String msisdn,@Param("cmServiceId")String cmServiceId);

}
