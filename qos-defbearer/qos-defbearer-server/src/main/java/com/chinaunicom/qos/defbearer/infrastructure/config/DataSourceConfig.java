package com.chinaunicom.qos.defbearer.infrastructure.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2024-12-02 14:41
 */
@Configuration
@Slf4j
public class DataSourceConfig {
    @Resource
    QosDataSourceProperties qosDataSourceProperties;

    @Resource
    PccDataSourceProperties pccDataSourceProperties;

    @Primary
    @Bean(name = "qosDatasource")
    public DataSource qosDatasource() {
        var qosHikariConfig = createHikariConfig(qosDataSourceProperties);
        var qosDataSource = new HikariDataSource(qosHikariConfig);
        log.info("Sharding Datasource Created: {}", qosHikariConfig.getJdbcUrl());
        return qosDataSource;
    }

    @Bean(name = "pccDatasource")
    public DataSource pccDatasource() {
        var pccHikariConfig = createHikariConfig(pccDataSourceProperties);
        return new HikariDataSource(pccHikariConfig);
    }

    private HikariConfig createHikariConfig(DataSourceProperties properties) {
        var defHikariConfig = new HikariConfig();
        defHikariConfig.setDriverClassName(properties.getDriverClassName());
        defHikariConfig.setJdbcUrl(properties.getUrl());
        defHikariConfig.setUsername(properties.getUsername());
        defHikariConfig.setPassword(properties.getPassword());
        defHikariConfig.setMaximumPoolSize(properties.getMaximumPoolSize());
        defHikariConfig.setMinimumIdle(properties.getMinimumIdle());
        defHikariConfig.setKeepaliveTime(properties.getKeepaliveTime());
        defHikariConfig.setPoolName(properties.getPoolName());
        return defHikariConfig;
    }
}
