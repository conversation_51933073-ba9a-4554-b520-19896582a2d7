package com.chinaunicom.qos.defbearer.infrastructure.dal.repo;

import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerResumeTaskRepo;
import com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos.QosDefbearerResumeTaskMapper;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-27 16:04
 */

@Repository
public class DefBearerResumeTaskRepo implements IDefBearerResumeTaskRepo {
    @Resource
    private QosDefbearerResumeTaskMapper resumeTaskMapper;

    @Override
    @Retryable(backoff = @Backoff(value = 30))
    public int deleteResumeTask(Long originOrderId, String userId) {
        return resumeTaskMapper.deleteByOrderIdUserId(originOrderId, userId);
    }

    @Override
    @Retryable(backoff = @Backoff(value = 30))
    public List<QosDefBearerResumeTaskPO> queryResumeTasksByUserId(String userId) {
        return resumeTaskMapper.queryByUserId(userId);
    }

    @Override
    @Retryable(backoff = @Backoff(value = 30))
    public int updateTaskExecutionParams(QosDefBearerResumeTaskPO taskPO) {
        return resumeTaskMapper.updateTaskExecutionParams(taskPO);
    }
}
