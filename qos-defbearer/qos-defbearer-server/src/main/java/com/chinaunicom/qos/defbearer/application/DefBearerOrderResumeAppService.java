package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.StatusEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.ResumeDefBearerOrderReq;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.enums.TaskExecutionStatusEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerResumeTaskRepo;
import com.chinaunicom.qos.defbearer.infrastructure.producer.KafkaProducer;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.*;
import com.chinaunicom.qos.defbearer.utils.OrderRecordMsgUtil;
import com.chinaunicom.qos.defbearer.utils.ValidateUtil;
import com.chinaunicom.qos.idgen.api.enums.OrderTypeEnum;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.redisson.api.RedissonClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.chinaunicom.qos.defbearer.common.utils.OrderResumeStatusUtil.isRetryableFailure;
import static com.chinaunicom.qos.defbearer.common.utils.OrderResumeStatusUtil.resumeTaskInvalid;

/**
 * <AUTHOR>
 * @date 2025-4-22 18:42
 */

@Slf4j
@Service
public class DefBearerOrderResumeAppService {
    @Resource
    private DefBearerOrderAppService orderAppService;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private IDefBearerResumeTaskRepo resumeTaskRepo;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisLockAppService redisLockAppService;

    @Resource
    private KafkaProducer kafkaProducer;

    @Resource
    private CmSnapShotProxy snapShotProxy;

    @Resource
    private UserProductProxy userProductProxy;

    @Resource
    private DefBearerAdapterProxy defBearerAdapterProxy;

    @Resource
    private IdGenProxy idGenProxy;

    @Resource
    private UserInfoProxy userInfoProxy;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Retryable(maxAttempts = 2, backoff = @Backoff(value = 20000))
    public Resp<Void> resumeOrder(ResumeDefBearerOrderReq req) {
        var orderTime = new Date();
        Integer resumeRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
        String resumeRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        List<QosDefBearerResumeTaskPO> conflictAmPcfOrders;
        var resumeOrderRecordPO = initQosDefbearerOrderRecord(req, orderTime);

        //根据手机号获取锁，获取不到则说明该用户已有正在进行中的QoS操作，返回失败
        var lock = new RedisDistributedRedLock(redissonClient, req.getUserId(), 60 * 1000L,
                10 * 1000L); //NOSONAR
        boolean isAcquire = redisLockAppService.acquireUserLock(req.getUserId(), lock, UserTypeEnum.PEOPLE);

        try {
            //查询用户是否已存在生效中的订单
            List<QosDefbearerOrderPO> curOrders = orderAppService.getValidOrdersByUserId(req.getMsisdn());
            checkOrderExist(req, curOrders);

            checkCmServiceStatus(req.getOriginOrderId(), req.getCmServiceId());

            var now = new Date();
            var userEndTime = req.getUserEndTime();
            //调用用户产品订购服务获取用户订购信息
            SubscribeInfoDTO subscribeInfo = userProductProxy.getUserProductSubscribeInfo(req.getUserId(), req.getQosProductId()
                    , UserTypeEnum.PEOPLE);
            if (!subscribeInfo.getExpireTime().after(now)) {
                log.info("用户订购失效时间{}早于等于当前时间{}，无需恢复订单", DateUtils.formatDate(subscribeInfo.getExpireTime()),
                        DateUtils.formatDate(now));
                throw new DefBearerException(DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH);
            }
            else if (subscribeInfo.getExpireTime().before(req.getUserEndTime())) {
                log.info("用户订购失效时间{}早于订单预期结束时间{}，调整预期结束时间为用户订购失效时间",
                        DateUtils.formatDate(subscribeInfo.getExpireTime()), DateUtils.formatDate(req.getUserEndTime()));
                userEndTime = subscribeInfo.getExpireTime();
                resumeOrderRecordPO.setUserEndTime(DateUtils.formatDate(subscribeInfo.getExpireTime()));
            }
            var startTime = DateUtils.removeMillis(new Date());
            Long duration =  DateUtils.computeDuration(startTime, userEndTime);
            log.info("开始时间为{}，用户期望结束时间为{}，计算出时长为{}", DateUtils.formatDateWithMillis(startTime),
                    DateUtils.formatDateWithMillis(userEndTime), duration);
            resumeOrderRecordPO.setStartTime(startTime);
            resumeOrderRecordPO.setDuration(duration);

            //AM-PCF策略冲突校验
            conflictAmPcfOrders = ValidateUtil.checkAmPcfConflict(curOrders, req.getQosProductId(), req.getUserEndTime(),
                    resumeOrderRecordPO, userProductProxy, req);

            //调用ID服务生成orderId
            Long orderId = idGenProxy.genOrderId(OrderTypeEnum.DEF_BEARER, req.getMsisdn(), UserTypeEnum.PEOPLE);

            //调用默载适配服务
            var userInfo = new UserInfoDTO();
            userInfo.setMsisdn(req.getMsisdn());
            userInfo.setImsi(req.getImsi());
            userInfo.setHomeProvince(req.getHomeProvince());
            Resp<OperResultDataDTO> operResp = defBearerAdapterProxy.add(resumeOrderRecordPO.getMessageId(), userEndTime, userInfo,
                    req.getAfid());
            OrderRecordMsgUtil.setAdapterResultForOrderRecordPO(resumeOrderRecordPO, operResp.getData());

            if(DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(operResp.getCode())
                    || DefBearerAdapterRespCodeEnum.ORDER_ALLREADY.getCode().equals(operResp.getCode())) {
                //默载建立成功，提交延时任务并保存订单
                orderAppService.createOrder(req, startTime, userEndTime, duration, orderId);
                resumeOrderRecordPO.setOrderId(orderId);
                if (CollectionUtils.isNotEmpty(conflictAmPcfOrders)) {
                    log.info("开始发送AM-PCF策略覆盖消息，需要覆盖的订单信息为{}", conflictAmPcfOrders);
                    CompletableFuture.runAsync(RunnableWrapper.of(() -> kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_AMPCF_CONFLICT, conflictAmPcfOrders)), msgSendExecutor);
                }
            } else if(DefBearerAdapterRespCodeEnum.USER_NOT_EXIST.getCode().equals(operResp.getCode())) {
                //IOM返回3001-用户不存在，需要调用用户服务去做开户
                userInfoProxy.openAccountForUser(String.valueOf(req.getOriginOrderId()), req.getMsisdn());
                throw new DefBearerException(DefBearerRespCodeEnum.USER_OPEN_SUCCESS);
            } else {
                throw new ProxyException(operResp);
            }

        } catch (DefBearerException de) {
            resumeRespCode = de.getCode();
            resumeRespDesc = de.getDesc();
            if (isRetryableFailure(resumeRespCode)) {
                throw de;
            }
        } catch (ProxyException pe) {
            resumeRespCode = pe.getResp().getCode();
            resumeRespDesc = pe.getResp().getMsg();
            throw pe;
        } catch (Exception e) {
            log.error("恢复订单异常！req={}", JsonUtil.obj2String(req), e);
            resumeRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            resumeRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            throw e;
        } finally {
            handleResumeTask(resumeRespCode, req);
            //释放用户锁
            redisLockAppService.releaseUserLock(isAcquire, lock);
            //发送调用流水
            resumeOrderRecordPO.setRespCode(resumeRespCode);
            resumeOrderRecordPO.setRespDesc(resumeRespDesc);
            resumeOrderRecordPO.setReturnTime(new Date());
            //异步发送调用流水
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(resumeOrderRecordPO)),
                    msgSendExecutor);
        }
        return Resp.<Void>builder().code(resumeRespCode).msg(resumeRespDesc).build();
    }


    private void checkOrderExist(ResumeDefBearerOrderReq req, List<QosDefbearerOrderPO> curOrders) {
        if(curOrders != null && !curOrders.isEmpty()) {
            for(QosDefbearerOrderPO order : curOrders) {
                if(order.getCmServiceId().equals(req.getCmServiceId())) {
                    log.info("恢复AM-PCF订单，已存在当前通信服务的有效订单，order={}", order);
                    throw new DefBearerException(DefBearerRespCodeEnum.USER_ORDER_EXIST);
                }
            }
        }
    }

    /**
     * 检查通信服务是否已关闭，若已关闭，则删除恢复任务
     */
    private void checkCmServiceStatus(Long originOrderId, String cmServiceId) {
        boolean cmServiceClosed = false;
        try {
            //调用快照服务获取通信服务配置相关数据
            CmsSnapshotDTO cmsSnapshot = snapShotProxy.getCmsConfigInfo(String.valueOf(originOrderId),
                    cmServiceId, null, UserTypeEnum.PEOPLE);
            cmServiceClosed = StatusEnum.INACTIVE.getStatus().equals(cmsSnapshot.getCmServiceStatus());
        } catch (Exception e) {
            log.warn("调用快照服务异常", e);
        }

        if (cmServiceClosed) {
            log.info("通信服务已关闭，无需再恢复订单");
            throw new DefBearerException(DefBearerRespCodeEnum.CMSERVICE_INVALID);
        }
    }

    private void handleResumeTask(Integer resumeRespCode, ResumeDefBearerOrderReq req) {
        if (DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT.getCode().equals(resumeRespCode)) {
            // 当前存在更高优先级的订单，更新恢复任务的下次执行时间、执行状态、清空失败重试次数
            QosDefBearerResumeTaskPO resumeTaskPO = new QosDefBearerResumeTaskPO();
            resumeTaskPO.setOriginOrderId(req.getOriginOrderId());
            resumeTaskPO.setUserId(req.getUserId());
            resumeTaskPO.setNextExecuteTime(req.getNextExecuteTime());
            resumeTaskPO.setExecuteStatus(TaskExecutionStatusEnum.TO_BE_EXECUTED.getStatus());
            resumeTaskPO.setExecuteFailRetryCount(0);
            log.info("策略冲突-当前存在优先级更高的订单，准备修改恢复任务的下次执行时间为订单预期结束时间");
            resumeTaskRepo.updateTaskExecutionParams(resumeTaskPO);
            log.info("任务修改成功");
        }
        else if (DefBearerRespCodeEnum.R_SUCCESS.getCode().equals(resumeRespCode)
                || resumeTaskInvalid(resumeRespCode)) {
            log.info("订单恢复成功或者已无需恢复，respCode={}，准备删除恢复任务", resumeRespCode);
            resumeTaskRepo.deleteResumeTask(req.getOriginOrderId(), req.getUserId());
            log.info("任务删除成功");
        }
    }


    private QosDefbearerOrderRecordPO initQosDefbearerOrderRecord(ResumeDefBearerOrderReq req, Date orderTime) {
        var resumeOrderRecordPO = new QosDefbearerOrderRecordPO();
        resumeOrderRecordPO.setMessageId(String.valueOf(req.getOriginOrderId()));
        resumeOrderRecordPO.setOrderSource(CommonConstants.OperSource.RESUME_ORDER);
        resumeOrderRecordPO.setQosProductId(req.getQosProductId());
        resumeOrderRecordPO.setCmServiceId(req.getCmServiceId());
        resumeOrderRecordPO.setUserId(req.getUserId());
        resumeOrderRecordPO.setMsisdn(req.getMsisdn());
        resumeOrderRecordPO.setImsi(req.getImsi());
        resumeOrderRecordPO.setOrderTime(orderTime);
        resumeOrderRecordPO.setUserEndTime(DateUtils.formatDate(req.getUserEndTime()));
        resumeOrderRecordPO.setSignNetworkType(req.getSignNetworkType());
        resumeOrderRecordPO.setBearerMode(req.getBearerMode());
        resumeOrderRecordPO.setSpeedType(req.getSpeedType());
        resumeOrderRecordPO.setAfid(req.getAfid());
        resumeOrderRecordPO.setHomeProvince(req.getHomeProvince());
        resumeOrderRecordPO.setVisitProvince(req.getVisitProvince());
        resumeOrderRecordPO.setUserType(UserTypeEnum.PEOPLE.getCode());
        resumeOrderRecordPO.setOperType(DefOperTypeEnum.ADD.getCode());
        return resumeOrderRecordPO;
    }


}
