package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.user.api.enums.UserResponseCodeEnum;
import com.chinaunicom.qos.user.api.request.IotOpenAccountReq;
import com.chinaunicom.qos.user.api.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 物网用户代理
 *
 * <AUTHOR>
 * @date 2025/1/24 17:18
 */
@Service
@Slf4j
public class IotUserInfoProxy {
    @DubboReference(timeout = 15000, retries = 0)
    private UserInfoService userInfoService;

    public void open(QosDefbearerOrderRecordPO orderRecordPO) {
        IotOpenAccountReq iotOpenAccountReq = new IotOpenAccountReq();
        iotOpenAccountReq.setImsi(orderRecordPO.getImsi());
        iotOpenAccountReq.setMessageId(orderRecordPO.getMessageId());
        iotOpenAccountReq.setForceOpen(Boolean.TRUE);
        iotOpenAccountReq.setOperSource(CommonConstants.OperSource.DEFBEARER);
        Resp<Void> openAccountResp;
        try {
            log.info("调用物网开户,req:{}", iotOpenAccountReq);
            openAccountResp = userInfoService.openIotAccount(iotOpenAccountReq);
            log.info("调用物网开户,resp:{}", openAccountResp);
        } catch (Exception e) {
            log.error("调用物网用户开户异常:", e);
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_USER_CALL_EXCEPTION);
        }
        if(openAccountResp.getCode() != UserResponseCodeEnum.R_SUCCESS.getCode()) {
            throw new ProxyException(openAccountResp);
        }
    }
}
