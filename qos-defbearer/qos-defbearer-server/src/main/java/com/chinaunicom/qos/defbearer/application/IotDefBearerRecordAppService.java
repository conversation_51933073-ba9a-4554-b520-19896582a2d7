package com.chinaunicom.qos.defbearer.application;

import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import com.chinaunicom.qos.common.enums.NetworkModeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 物网默载记录业务处理
 *
 * <AUTHOR>
 * @date 2025/2/8 17:08
 */
@Slf4j
@Service
public class IotDefBearerRecordAppService {

    public void setAfidAndSpeedType(CmsSnapshotDTO cmsSnapshot, QosDefbearerOrderRecordPO orderRecordPO) {
        String afid = cmsSnapshot.getAfid(NetworkModeEnum.FIVE_GEN.getCode());
        if(StringUtils.isEmpty(afid)) {
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_AFID_MISSING);
        }
        orderRecordPO.setAfid(afid);
        orderRecordPO.setSpeedType(cmsSnapshot.getSpeedType());
    }

}
