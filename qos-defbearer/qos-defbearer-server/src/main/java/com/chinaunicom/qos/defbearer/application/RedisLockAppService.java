package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * redis锁处理
 *
 * <AUTHOR>
 * @date 2025/1/24 09:52
 */
@Service
@Slf4j
@SuppressWarnings("java:S2142")
public class RedisLockAppService {

    public boolean acquireUserLock(String userId, RedisDistributedRedLock lock, UserTypeEnum userType) {
        var isAcquire = false;
        var userConflict = false;
        try {
            isAcquire = lock.acquire();
            if(!isAcquire) {
                log.info("获取用户锁失败，userId={}", userId);
                userConflict = true;
            } else {
                log.info("------成功获取用户锁{}------", userId);
            }
        } catch (Exception e) {
            log.error("获取用户锁异常！userId={}", userId, e);
        }
        if (userConflict) {
            throw new DefBearerException(userType==UserTypeEnum.IOT ? DefBearerRespCodeEnum.IOT_USER_CONFLICT :DefBearerRespCodeEnum.USER_CONFLICT);
        }
        return isAcquire;
    }

    public void releaseUserLock(boolean isAcquire, RedisDistributedRedLock lock) {
        if(isAcquire) {
            try {
                lock.release();
                log.info("------用户锁已释放-----");
            } catch (Exception e) {
                log.error("释放用户锁异常！", e);
            }
        }
    }
}
