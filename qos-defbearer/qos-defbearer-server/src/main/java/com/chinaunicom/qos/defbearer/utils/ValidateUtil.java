package com.chinaunicom.qos.defbearer.utils;

import com.chinaunicom.qos.common.enums.*;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.DurationCheck;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.ResumeDefBearerOrderReq;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.enums.TaskExecutionStatusEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.UserProductProxy;
import com.chinaunicom.qos.snapshot.api.dto.CmServiceCheckConfigDTO;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.ProductWithOrderPriorityDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-4-22 09:53
 */

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidateUtil {

    public static void validateRequestData (DefOperTypeEnum operType, CmsSnapshotDTO cmsSnapshot, UserInfoDTO userInfo,
                                            SubscribeInfoDTO subscribeInfo, Long duration, Date userEndTime) {
        //通信服务状态校验，只支持状态为“使用中”的通信服务
        if (!StatusEnum.ACTIVE.getStatus().equals(cmsSnapshot.getCmServiceStatus())) {
            log.info("通信服务状态校验失败，cmServiceId={}, status={}", cmsSnapshot.getCmServiceId(),
                    cmsSnapshot.getCmServiceStatus());
            throw new DefBearerException(DefBearerRespCodeEnum.CMSERVICE_INVALID);
        }

        //网络模式一致性校验
        if (!cmsSnapshot.getNetworkMode().equals(NetworkModeEnum.FOUR_FIVE_GEN.getCode()) &&
                !userInfo.getSignNetworkType().equals(cmsSnapshot.getNetworkMode())) {
            log.info("网络模式一致性校验失败，通信服务网络模式={}，用户签约的网络类型={}", cmsSnapshot.getNetworkMode(),
                    userInfo.getSignNetworkType());
            throw new DefBearerException(DefBearerRespCodeEnum.NETWORK_TYPE_MISMATCH);
        }

        //产品订购校验
        var now = new Date();
        if (subscribeInfo.getEffectiveTime().after(now) || subscribeInfo.getExpireTime().before(userEndTime)) {
            log.info("产品订购校验失败，订购生效时间={}，订购失效时间={}，默载期望终止时间={}，当前时间={}",
                    DateUtils.formatDate(subscribeInfo.getEffectiveTime()),
                    DateUtils.formatDate(subscribeInfo.getExpireTime()),
                    DateUtils.formatDate(userEndTime), DateUtils.formatDate(now));
            throw new DefBearerException(DefBearerRespCodeEnum.PRODUCT_SUBSCRIBE_PERIOD_MISMATCH);
        }


        //产品和通信服务一致性校验，只有默载申请需要做
        if (DefOperTypeEnum.ADD.equals(operType) && !subscribeInfo.getCmServiceIdList().contains(cmsSnapshot.getCmServiceId())) {
            log.info("产品和通信服务一致性校验失败，产品配置的通信服务列表={}，请求的通信服务={}", subscribeInfo.getCmServiceIdList(),
                    cmsSnapshot.getCmServiceId());
            throw new DefBearerException(DefBearerRespCodeEnum.PRODUCT_CMSERVICE_MISMATCH);
        }

        //归属省校验，只有默载申请且通信服务归属省非全国需要做
        if (DefOperTypeEnum.ADD.equals(operType) && !cmsSnapshot.getHomeProvinceList().contains(ProvinceCodeEnum.CHINA.getCode2())
                && !cmsSnapshot.getHomeProvinceList().contains(userInfo.getHomeProvince())) {
            log.info("归属省校验失败，通信服务配置的归属省={}，用户归属省={}", cmsSnapshot.getHomeProvinceList(), userInfo.getHomeProvince());
            throw new DefBearerException(DefBearerRespCodeEnum.HOME_PROVINCE_NOT_ALLOWED);
        }

        validateConfiguredChecks(cmsSnapshot, userInfo, duration);
    }

    @SuppressWarnings("java:S3776")
    public static void validateConfiguredChecks(CmsSnapshotDTO cmsSnapshot, UserInfoDTO userInfo, Long duration) {
        if(cmsSnapshot.getCheckConfigList() != null && !cmsSnapshot.getCheckConfigList().isEmpty()) {
            for(CmServiceCheckConfigDTO config : cmsSnapshot.getCheckConfigList()) {
                //时长校验
                if (CmServiceCheckKeyEnum.DURATION.getKey().equals(config.getCheckKey()) &&
                        DurationCheck.checkDurationFailed(config.getCheckValue(), duration)) {
                    log.info("时长校验失败，通信服务配置的时长规则={}，duration={}", config.getCheckValue(), duration);
                    throw new DefBearerException(DefBearerRespCodeEnum.DURATION_INVALID);
                }

                //拜访省校验
                if (CmServiceCheckKeyEnum.VISIT_PROVINCE.getKey().equals(config.getCheckKey()) && userInfo.getVisitProvince()!=null
                        && !config.getCheckValue().contains(String.valueOf(userInfo.getVisitProvince()))) {
                    log.info("拜访省校验失败，通信服务配置的拜访省={}，用户拜访省={}", config.getCheckValue(), userInfo.getVisitProvince());
                    throw new DefBearerException(DefBearerRespCodeEnum.VISIT_PROVINCE_NOT_ALLOWED);
                }
            }
        }
    }


    @SuppressWarnings("java:S3776")
    public static List<QosDefBearerResumeTaskPO> checkAmPcfConflict(List<QosDefbearerOrderPO> curOrders,
                                                                    Integer reqQosProductId,
                                                                    Date userEndTime,
                                                                    QosDefbearerOrderRecordPO addOrderRecordPO,
                                                                    UserProductProxy userProductProxy,
                                                                    ResumeDefBearerOrderReq resumeTaskReq) {
        List<QosDefBearerResumeTaskPO> conflictAmPcfOrders = new ArrayList<>();

        List<QosDefbearerOrderPO> curAmPcfOrders = curOrders.stream().filter(o -> BearerModeEnum.AM_PCF.getCode().equals(o.getBearerMode())).toList();

        if (CollectionUtils.isNotEmpty(curAmPcfOrders)) {
            List<Integer> qosProductIds = new ArrayList<>();
            qosProductIds.add(reqQosProductId);
            qosProductIds.addAll(curAmPcfOrders.stream().map(QosDefbearerOrderPO::getQosProductId).toList());
            var productOrderPriorityList = userProductProxy.queryProductOrderPriorities(qosProductIds);
            var productOrderPriorityMap =
                    productOrderPriorityList.stream().filter(o->o.getOrderPriority()!=null).collect(Collectors.toMap(ProductWithOrderPriorityDTO::getQosProductId,
                            ProductWithOrderPriorityDTO::getOrderPriority));

            BigDecimal reqOrderPriority = productOrderPriorityMap.get(reqQosProductId);
            log.info("本次请求的产品调用优先级为{}", reqOrderPriority);

            if (reqOrderPriority != null) {
                addOrderRecordPO.setPriority(reqOrderPriority);

                for (QosDefbearerOrderPO amPcfOrder : curAmPcfOrders) {
                    var curOrderPriority = productOrderPriorityMap.get(amPcfOrder.getQosProductId());
                    if (curOrderPriority == null) {
                        log.info("当前AmPCF订单不参与产品优先级调度，amPcfOrder={}", JsonUtil.obj2String(amPcfOrder));
                        continue;
                    }
                    if (curOrderPriority.compareTo(reqOrderPriority) <= 0) {
                        log.info("当前AmPCF订单产品优先级高于等于本次请求的优先级，amPcfOrder={}", JsonUtil.obj2String(amPcfOrder));
                        if (resumeTaskReq != null && amPcfOrder.getUserEndTime().after(resumeTaskReq.getNextExecuteTime())) {
                            resumeTaskReq.setNextExecuteTime(amPcfOrder.getUserEndTime());
                        }
                        throw new DefBearerException(DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT);
                    }
                    log.info("当前AmPCF订单产品优先级小于本次请求的优先级，加入覆盖列表");
                    conflictAmPcfOrders.add(toQosDefBearerResumeTaskPO(amPcfOrder, userEndTime));
                }
            }
        }

        return conflictAmPcfOrders;
    }

    public static QosDefBearerResumeTaskPO toQosDefBearerResumeTaskPO(QosDefbearerOrderPO orderPO, Date resumeTime) {
        var resumeTaskPO = new QosDefBearerResumeTaskPO();
        resumeTaskPO.setOriginOrderId(orderPO.getOrderId());
        resumeTaskPO.setUserId(orderPO.getUserId());
        resumeTaskPO.setQosProductId(orderPO.getQosProductId());
        resumeTaskPO.setCmServiceId(orderPO.getCmServiceId());
        resumeTaskPO.setResumeTime(resumeTime);
        resumeTaskPO.setUserEndTime(orderPO.getUserEndTime());
        resumeTaskPO.setMsisdn(orderPO.getMsisdn());
        resumeTaskPO.setImsi(orderPO.getImsi());
        resumeTaskPO.setHomeProvince(orderPO.getHomeProvince());
        resumeTaskPO.setVisitProvince(orderPO.getVisitProvince());
        resumeTaskPO.setUserType(orderPO.getUserType());
        resumeTaskPO.setSignNetworkType(orderPO.getSignNetworkType());
        resumeTaskPO.setAfid(orderPO.getAfid());
        resumeTaskPO.setBearerMode(orderPO.getBearerMode());
        resumeTaskPO.setSpeedType(orderPO.getSpeedType());
        resumeTaskPO.setNextExecuteTime(resumeTime);
        resumeTaskPO.setExecuteStatus(TaskExecutionStatusEnum.TO_BE_EXECUTED.getStatus());
        resumeTaskPO.setExecuteFailRetryCount(0);
        resumeTaskPO.setSnapshotVersion(orderPO.getSnapshotVersion());
        return resumeTaskPO;
    }

}
