package com.chinaunicom.qos.defbearer.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {
    @Bean("msgSendExecutor")
    public Executor msgSendExecutor() {
        var executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(100);
        executor.setMaxPoolSize(100);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(2000);
        executor.setThreadNamePrefix("msgSendExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        return executor;
    }

    @Bean("resumeTaskExecutor")
    public Executor resumeTaskExecutor() {
        var resumeTaskExecutor = new ThreadPoolTaskExecutor();
        resumeTaskExecutor.setCorePoolSize(50);
        resumeTaskExecutor.setMaxPoolSize(50);
        resumeTaskExecutor.setKeepAliveSeconds(120);
        resumeTaskExecutor.setQueueCapacity(2000);
        resumeTaskExecutor.setThreadNamePrefix("resumeTaskExecutor-");
        resumeTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        resumeTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        resumeTaskExecutor.setAwaitTerminationSeconds(120);
        return resumeTaskExecutor;
    }
}
