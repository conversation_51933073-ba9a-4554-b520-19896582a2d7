package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.enums.BearerModeEnum;
import com.chinaunicom.qos.common.enums.SpeedTypeEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.task.api.enums.QosTaskRespCodeEnum;
import com.chinaunicom.qos.task.api.request.QosDelayTaskAddReq;
import com.chinaunicom.qos.task.delayexecutor.infrastructure.proxy.QosDelayTaskAddProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.DelayTask.*;

@Slf4j
@Service
public class DelayTaskProxy {
    @Resource
    QosDelayTaskAddProxy qosDelayTaskAddProxy;

    @Value("${task.delay.group}")
    private String appName;

    public int createDelayTask(Date taskExecutionTime, QosDefbearerOrderPO order) {
        var req = new QosDelayTaskAddReq();
        try {
            String execTime = DateUtils.formatDate(taskExecutionTime);
            req.setExecuteTime(execTime);
            req.setExecutorAppName(appName);
            if (BearerModeEnum.AM_PCF.getCode().equals(order.getBearerMode())) {
                req.setExecutorHandler(AM_PCF_EXECUTOR_HANDLER);
            }
            else {
                if (SpeedTypeEnum.UP.getCode().equals(order.getSpeedType())) {
                    req.setExecutorHandler(UserTypeEnum.IOT.getCode().equals(order.getUserType()) ?
                            IOT_SPEED_UP_EXECUTOR_HANDLER : SPEED_UP_EXECUTOR_HANDLER);
                } else {
                    req.setExecutorHandler(UserTypeEnum.IOT.getCode().equals(order.getUserType()) ?
                            IOT_SPEED_DOWN_EXECUTOR_HANDLER : SPEED_DOWN_EXECUTOR_HANDLER);
                }
            }
            Map<String, Object> params = new HashMap<>();
            params.put(USER_ID, order.getUserId());
            params.put(ORDER_ID, order.getOrderId());
            params.put(AFID, order.getAfid());
            params.put(EXPECTED_EXEC_TIME, execTime);
            req.setExecuteParam(JsonUtil.obj2String(params));
            log.info("开始创建延时任务，req={}", JsonUtil.obj2String(req));
            Resp<Void> resp = qosDelayTaskAddProxy.delayTaskAdd(req);
            log.info("延时任务创建完成，resp={}", JsonUtil.obj2String(resp));
            if(QosTaskRespCodeEnum.R_SUCCESS.getCode().equals(resp.getCode())) {
                return TASK_CREATED;
            }
        } catch (Exception e) {
            log.error("创建延时任务异常！req={}", req, e);
        }
        return TASK_NOT_CREATED;
    }
}
