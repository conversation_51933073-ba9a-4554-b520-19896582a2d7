package com.chinaunicom.qos.defbearer.exception;

import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import lombok.Getter;

@Getter
public class DefBearerException extends RuntimeException {
    private final int code;
    private final String desc;

    public DefBearerException(DefBearerRespCodeEnum respCodeEnum) {
        super(respCodeEnum.getDesc());
        this.code = respCodeEnum.getCode();
        this.desc = respCodeEnum.getDesc();
    }

    public DefBearerException(int code, String desc) {
        super(desc);
        this.code = code;
        this.desc = desc;
    }
}
