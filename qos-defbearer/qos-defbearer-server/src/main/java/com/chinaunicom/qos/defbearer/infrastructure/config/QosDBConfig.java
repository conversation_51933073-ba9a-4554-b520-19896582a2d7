package com.chinaunicom.qos.defbearer.infrastructure.config;

import lombok.SneakyThrows;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2024-12-02 15:54
 */
@Configuration
@MapperScan(value = {"com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos"}, sqlSessionFactoryRef =
        "qosSqlSessionFactory")
public class QosDBConfig {
    private static final String QOS_MAPPER_LOCATION = "classpath:mapper/qos/*.xml";

    @Resource(name = "qosDatasource")
    private DataSource qosDatasource;

    @Primary
    @Bean("qosSqlSessionFactory")
    @SneakyThrows
    public SqlSessionFactory qosSqlSessionFactory() {
        var qosSessionFactoryBean = new SqlSessionFactoryBean();
        qosSessionFactoryBean.setDataSource(qosDatasource);
        qosSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(QosDBConfig.QOS_MAPPER_LOCATION));
        qosSessionFactoryBean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return qosSessionFactoryBean.getObject();
    }
}
