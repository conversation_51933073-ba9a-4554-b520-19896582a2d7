package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.defbearer.adapter.api.dto.IotDefBearerAdapterDTO;
import com.chinaunicom.qos.defbearer.adapter.api.request.IotDefBearerAdapterAddReq;
import com.chinaunicom.qos.defbearer.adapter.api.request.IotDefBearerAdapterTerminateReq;
import com.chinaunicom.qos.defbearer.adapter.api.service.IotDefBearerAdapterService;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 物网默载适配接口代理
 *
 * <AUTHOR>
 * @date 2025/1/24 16:21
 */
@Service
@Slf4j
public class IotDefBearerAdapterProxy {

    @DubboReference(timeout = 15000, retries = 0)
    private IotDefBearerAdapterService iotDefBearerAdapterService;

    public Resp<IotDefBearerAdapterDTO> add(QosDefbearerOrderRecordPO orderRecordPO) {
        //组装请求报文
        var iotDefBearerAdapterAddReq = new IotDefBearerAdapterAddReq();
        iotDefBearerAdapterAddReq.setImsi(orderRecordPO.getImsi());
        iotDefBearerAdapterAddReq.setAfid(orderRecordPO.getAfid());
        iotDefBearerAdapterAddReq.setMessageId(orderRecordPO.getMessageId());
        iotDefBearerAdapterAddReq.setUserEndTime(DateUtils.parseLocalDateTime(orderRecordPO.getUserEndTime(),null));
        try {
            log.info("调用默载适配申请,req:{}", iotDefBearerAdapterAddReq);
            Resp<IotDefBearerAdapterDTO> result = iotDefBearerAdapterService.add(iotDefBearerAdapterAddReq);
            log.info("调用默载适配申请,resp:{}", result);
            //赋值网元ID到记录
            orderRecordPO.setNeId(CommonConstants.NE_ID_IOT);
            return result;
        } catch (Exception e) {
            log.error("调用默载适配申请异常:", e);
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_ADAPTER_CALL_EXCEPTION);
        }
    }
    public Resp<IotDefBearerAdapterDTO> terminate(QosDefbearerOrderRecordPO orderRecordPO) {
        //组装请求报文
        var iotDefBearerAdapterTerminateReq = new IotDefBearerAdapterTerminateReq();
        iotDefBearerAdapterTerminateReq.setImsi(orderRecordPO.getImsi());
        iotDefBearerAdapterTerminateReq.setAfid(orderRecordPO.getAfid());
        iotDefBearerAdapterTerminateReq.setMessageId(orderRecordPO.getMessageId());
        try {
            log.info("调用默载适配终止,req:{}", iotDefBearerAdapterTerminateReq);
            Resp<IotDefBearerAdapterDTO> result = iotDefBearerAdapterService.terminate(iotDefBearerAdapterTerminateReq);
            log.info("调用默载适配终止,resp:{}", result);
            //赋值网元ID到记录
            orderRecordPO.setNeId(CommonConstants.NE_ID_IOT);
            return result;
        } catch (Exception e) {
            log.error("调用默载适配终止异常:", e);
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_ADAPTER_CALL_EXCEPTION);
        }
    }
}
