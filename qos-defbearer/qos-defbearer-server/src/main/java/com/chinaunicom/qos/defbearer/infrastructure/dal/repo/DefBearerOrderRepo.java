package com.chinaunicom.qos.defbearer.infrastructure.dal.repo;

import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerOrderRepo;
import com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos.QosDefbearerOrderMapper;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Repository
public class DefBearerOrderRepo implements IDefBearerOrderRepo {

    @Resource
    private QosDefbearerOrderMapper qosDefbearerOrderMapper;

    @Override
    public List<QosDefbearerOrderPO> getValidOrders(String userId, List<String> cmServiceIds, Integer qosProductId) {
        return qosDefbearerOrderMapper.queryValidOrderByUserIdCmServiceIds(userId, cmServiceIds, qosProductId);
    }

    @Override
    public List<QosDefbearerOrderPO> getOrders(String userId, List<String> cmServiceIds, Integer qosProductId) {
        return qosDefbearerOrderMapper.queryOrderByUserIdCmServiceIds(userId, cmServiceIds, qosProductId);
    }

    @Override
    public List<QosDefbearerOrderPO> getValidOrdersByUserId(String userId) {
        return qosDefbearerOrderMapper.queryValidOrderByUserId(userId);
    }

    @Override
    public QosDefbearerOrderPO getOrderByUserIdCmServiceId(String userId, String cmServiceId) {
        return qosDefbearerOrderMapper.queryOrderByUserIdCmServiceId(userId, cmServiceId);
    }

    @Override
    public QosDefbearerOrderPO getOrderByUserIdOrderId(String userId, Long orderId) {
        return qosDefbearerOrderMapper.queryOrderByUserIdOrderId(userId, orderId);
    }

    @Override
    public int updateOrder(Map<String,Object> map) {
        return qosDefbearerOrderMapper.updateOrder(map);
    }

    @Override
    public int saveOrder(QosDefbearerOrderPO order) {
        return qosDefbearerOrderMapper.insertOrder(order);
    }
}
