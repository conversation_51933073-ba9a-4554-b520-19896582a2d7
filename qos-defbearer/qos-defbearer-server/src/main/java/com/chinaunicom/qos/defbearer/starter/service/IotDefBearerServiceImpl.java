package com.chinaunicom.qos.defbearer.starter.service;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.api.dto.DefBearerOrderDTO;
import com.chinaunicom.qos.defbearer.api.dto.IotDefBearerAddDTO;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerAddReq;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerTerminateByImsiReq;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerTerminateByOrderReq;
import com.chinaunicom.qos.defbearer.api.request.QueryDefBearerOrdersReq;
import com.chinaunicom.qos.defbearer.api.service.IotDefBearerService;
import com.chinaunicom.qos.defbearer.application.DefBearerQueryAppService;
import com.chinaunicom.qos.defbearer.application.IotDefBearerAddAppService;
import com.chinaunicom.qos.defbearer.application.IotDefBearerTerminateAppService;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.function.Consumer;

/**
 * 物网默载接口实现
 *
 * <AUTHOR>
 * @date 2025/1/23 11:22
 */
@DubboService(filter = {"constraintViolationExceptionFilter"})
@Slf4j
public class IotDefBearerServiceImpl implements IotDefBearerService {

    @Resource
    private DefBearerQueryAppService defBearerQueryAppService;

    @Resource
    private IotDefBearerAddAppService iotDefBearerAddAppService;

    @Resource
    private IotDefBearerTerminateAppService iotDefBearerTerminateAppService;

    @Override
    public Resp<IotDefBearerAddDTO> add(IotDefBearerAddReq req) {
        log.info("物网默载申请，req={}", req);
        Integer iotAddRespCode;
        String iotAddRespDesc;
        try {
            var dto = iotDefBearerAddAppService.add(req);
            return Resp.<IotDefBearerAddDTO>builder()
                       .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                       .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                       .data(dto)
                       .build();
        } catch (DefBearerException de) {
            iotAddRespCode = de.getCode();
            iotAddRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            iotAddRespCode = pe.getResp().getCode();
            iotAddRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("物网默载申请异常", e);
            iotAddRespCode = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode();
            iotAddRespDesc = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc();
        }
        return Resp.<IotDefBearerAddDTO>builder()
                   .code(iotAddRespCode)
                   .msg(iotAddRespDesc)
                   .build();
    }

    @Override
    public Resp<Void> terminateByOrder(IotDefBearerTerminateByOrderReq req) {
        return terminate(req, "物网默载按订单终止", r -> iotDefBearerTerminateAppService.terminateByOrder(r, false));
    }

    @Override
    public Resp<Void> terminateBySystem(IotDefBearerTerminateByOrderReq req) {
        return terminate(req, "物网默载按系统终止",r -> iotDefBearerTerminateAppService.terminateByOrder(r, true));
    }

    @Override
    public Resp<Void> terminateByImsi(IotDefBearerTerminateByImsiReq req) {
        return terminate(req, "物网默载按IMSI终止", r -> iotDefBearerTerminateAppService.terminateByImsi(r));
    }

    private <T> Resp<Void> terminate(T req, String logMsg, Consumer<T> terminateFunction) {
        log.info("{}, req={}", logMsg, req);
        Integer iotTerminateRespCode;
        String iotTerminateRespDesc;
        try {
            terminateFunction.accept(req);
            return Resp.<Void>builder()
                       .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                       .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                       .build();
        } catch (DefBearerException de) {
            iotTerminateRespCode = de.getCode();
            iotTerminateRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            iotTerminateRespCode = pe.getResp().getCode();
            iotTerminateRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("{}, 异常", logMsg, e);
            iotTerminateRespCode = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode();
            iotTerminateRespDesc = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                   .code(iotTerminateRespCode)
                   .msg(iotTerminateRespDesc)
                   .build();
    }

    @Override
    public Resp<List<DefBearerOrderDTO>> queryValidOrders(QueryDefBearerOrdersReq req) {
        log.info("物网生效订单查询，req={}", req);
        Integer respCode;
        String respDesc;
        try {
            var orders = defBearerQueryAppService.queryValidOrders(req, UserTypeEnum.IOT);
            return Resp.<List<DefBearerOrderDTO>>builder()
                       .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                       .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                       .data(orders)
                       .build();
        } catch (DefBearerException de) {
            respCode = de.getCode();
            respDesc = de.getDesc();
        } catch (Exception e) {
            log.error("查询订单列表异常，req={}", req, e);
            respCode = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode();
            respDesc = DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc();
        }
        return Resp.<List<DefBearerOrderDTO>>builder()
                   .code(respCode)
                   .msg(respDesc)
                   .build();
    }
}
