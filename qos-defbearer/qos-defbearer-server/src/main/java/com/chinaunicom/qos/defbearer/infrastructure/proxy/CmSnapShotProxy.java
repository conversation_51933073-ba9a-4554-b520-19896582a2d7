package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.enums.BearerModeEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.snapshot.api.enums.SnapshotRespCodeEnum;
import com.chinaunicom.qos.snapshot.api.request.QuerySnapshotReq;
import com.chinaunicom.qos.snapshot.api.service.QuerySnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


@Service
@Slf4j
public class CmSnapShotProxy {
    @DubboReference(version = "v2", timeout = 1000)
    private QuerySnapshotService snapShotService;

    public CmsSnapshotDTO getCmsConfigInfo(String messageId, String cmServiceId, String snapshotVersion, UserTypeEnum userType) {
        QuerySnapshotReq snapshotReq = new QuerySnapshotReq(); //NOSONAR
        snapshotReq.setMessageId(messageId);
        snapshotReq.setCmServiceId(cmServiceId);
        if(snapshotVersion != null) snapshotReq.setSnapshotVersion(snapshotVersion);
        Resp<CmsSnapshotDTO> snapshotResp;
        CmsSnapshotDTO cmsSnapshot;
        try {
            log.info("开始调用快照服务获取配置信息，req={}", snapshotReq);
            snapshotResp = snapShotService.query(snapshotReq);
            log.info("调用快照服务获取配置信息结束，resp={}", JsonUtil.obj2String(snapshotResp));
        } catch (Exception e) {
            log.error("调用快照服务异常！req={}", snapshotReq, e);
            throw new DefBearerException(userType == UserTypeEnum.IOT ? DefBearerRespCodeEnum.IOT_SNAPSHOT_CALL_EXCEPTION :DefBearerRespCodeEnum.SNAPSHOT_CALL_EXCEPTION);
        }
        if(snapshotResp.getCode() != SnapshotRespCodeEnum.R_SUCCESS.getCode()) {
            log.info("快照服务返回失败！");
            throw new ProxyException(snapshotResp);
        }
        cmsSnapshot = snapshotResp.getData();
        if(cmsSnapshot==null || CollectionUtils.isEmpty(cmsSnapshot.getPccStrategyList())) {
            log.info("未获取到通信服务策略数据，cmsSnapshot={}", cmsSnapshot);
            throw new DefBearerException(userType == UserTypeEnum.IOT ? DefBearerRespCodeEnum.IOT_AFID_MISSING :DefBearerRespCodeEnum.AFID_MISSING);
        }
        if (!BearerModeEnum.DEF.getCode().equals(cmsSnapshot.getBearerMode()) &&
                !BearerModeEnum.AM_PCF.getCode().equals(cmsSnapshot.getBearerMode())) {
            log.info("非默载通信服务，bearerMode={}", cmsSnapshot.getBearerMode());
            throw new DefBearerException(DefBearerRespCodeEnum.CMSERVICE_INVALID.getCode(),
                    DefBearerRespCodeEnum.CMSERVICE_INVALID.getDesc()+" 非默载通信服务");
        }
        return cmsSnapshot;
    }
}
