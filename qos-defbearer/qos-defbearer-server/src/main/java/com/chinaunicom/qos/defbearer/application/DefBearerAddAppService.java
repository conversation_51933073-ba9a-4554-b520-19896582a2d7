package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.*;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.*;
import com.chinaunicom.qos.dedbearer.api.dto.DedBearerOrderDTO;
import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.dto.AddDefBearerDTO;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.*;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.common.model.QosOrderBO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.config.MigratingCmServiceConfig;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IPccCurBearRepo;
import com.chinaunicom.qos.defbearer.infrastructure.dal.po.UserUserBearCurinfoPO;
import com.chinaunicom.qos.defbearer.infrastructure.producer.KafkaProducer;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.*;
import com.chinaunicom.qos.defbearer.utils.OrderRecordMsgUtil;
import com.chinaunicom.qos.defbearer.utils.ValidateUtil;
import com.chinaunicom.qos.idgen.api.enums.OrderTypeEnum;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants.*;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.OperSource.ORDER_REPLACEMENT;

@Slf4j
@Service
public class DefBearerAddAppService {
    @Resource
    private DefBearerOrderAppService defBearerOrderAppService;

    @Resource
    private DefBearerTerminateAppService defBearerTerminateAppService;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private IPccCurBearRepo pccCurBearRepo;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private KafkaProducer kafkaProducer;

    @Resource
    private CmSnapShotProxy snapShotProxy;

    @Resource
    private UserInfoProxy userInfoProxy;

    @Resource
    private UserProductProxy userProductProxy;

    @Resource
    private DefBearerAdapterProxy defBearerAdapterProxy;

    @Resource
    private IdGenProxy idGenProxy;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Resource
    private OldDefBearerProxy oldDefBearerProxy;

    @Resource
    private DedBearerProxy dedBearerProxy;

    @Resource
    private MigratingCmServiceConfig migratingCmServiceConfig;

    @Resource
    private RedisLockAppService redisLockAppService;

    /**
     * 增加enhancedAdd方法，用以支持订单生效期间的重复申请
     */
    public AddDefBearerDTO enhancedAdd(AddDefBearerReq req) {
        QosDefbearerOrderPO order = null;
        try {
            //根据手机号和通信服务获取状态为生效中的订单（本查询没有用时间参数）
            order = defBearerOrderAppService.getOrderByUserIdCmServiceId(req.getMsisdn(), req.getCmServiceId());
            //跟当前时间比的时候去掉毫秒值
            var now = DateUtils.removeMillis(new Date());
            //由于适配服务下发网络策略的时候加了2分钟，为了避免申请的时候出现业务已订购错误，判断重复订单的时候也要加2分钟
            if (order != null && DateUtils.computeDate(order.getUserEndTime(),NET_ADDITIONAL_SECONDS).after(now)) {
                log.info("重复申请，用户已存在订单{}，准备终止", JsonUtil.obj2String(order));
                var terminateReq = new TerminateDefBearerReq();
                terminateReq.setMsisdn(req.getMsisdn());
                terminateReq.setMessageId(req.getMessageId());
                terminateReq.setOrderId(order.getOrderId());
                terminateReq.setOrderSource(ORDER_REPLACEMENT);
                defBearerTerminateAppService.terminateByOrderId(terminateReq, true);
            }
        } catch (Exception e) {
            log.error("终止订单异常！", e);
        }
        if(order == null && migratingCmServiceConfig.getMigratingCmServiceIds().contains(req.getCmServiceId())){
            //增加查询一下老系统的订单，新系统如果已经有数据了，则不用查询老系统了
            var pccOrders = pccCurBearRepo.getPccCurBearsByStatus(req.getMsisdn(), req.getCmServiceId());
            for (UserUserBearCurinfoPO pccOrder : pccOrders) {
                log.info("老系统存在生效中默载数据，需要进行删除，手机号[{}],会话[{}]",pccOrder.getMsisdn(),pccOrder.getCorrelationId());
                //调用应急删除
                oldDefBearerProxy.terminateDefBearer(pccOrder);
            }
        }
        return add(req);
    }

    @SuppressWarnings("java:S3776")
    public AddDefBearerDTO add(AddDefBearerReq req) {
        Date orderTime = new Date(); //NOSONAR
        AddDefBearerDTO dto = new AddDefBearerDTO(); //NOSONAR
        List<QosOrderBO> conflictOrders = null;
        List<QosDefBearerResumeTaskPO> conflictAmPcfOrders = null;
        QosDefbearerOrderPO order;
        Integer addRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
        String addRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        var addOrderRecordPO = initQosDefbearerOrderRecord(req, orderTime);

        //失败重发拒绝校验：5分钟之内连续失败5次，拒绝请求20分钟
        String rejectKey = CommonConstants.RedisKey.REJECT_DEF_BEARER_USER_FAIL_ADD + req.getMsisdn() + ":" + req.getCmServiceId();
        String zsetKey = CommonConstants.RedisKey.ZSET_DEF_BEARER_USER_FAIL_ADD + req.getMsisdn() + ":" + req.getCmServiceId();
        if(RequestRetryCheckHelper.checkFailureReject(redissonClient, rejectKey)) {
            log.info("失败重发拒绝，rejectKey={}", rejectKey);
            throw new DefBearerException(DefBearerRespCodeEnum.FAIL_TOO_MANY_TIMES);
        }

        //根据手机号获取锁，获取不到则说明该用户已有正在进行中的QoS操作，返回失败
        RedisDistributedRedLock lock = new RedisDistributedRedLock(redissonClient, req.getMsisdn(), 60 * 1000L,
                10 * 1000L); //NOSONAR
        boolean isAcquire = redisLockAppService.acquireUserLock(req.getMsisdn(), lock, UserTypeEnum.PEOPLE);

        try {
            //查询用户是否已存在生效中的订单
            List<QosDefbearerOrderPO> curOrders = defBearerOrderAppService.getValidOrdersByUserId(req.getMsisdn());
            checkOrderExist(req.getCmServiceId(), curOrders);

            //调用快照服务获取通信服务配置相关数据
            CmsSnapshotDTO cmsSnapshot = snapShotProxy.getCmsConfigInfo(req.getMessageId(), req.getCmServiceId(),null
                    , UserTypeEnum.PEOPLE);
            addOrderRecordPO.setBearerMode(cmsSnapshot.getBearerMode());
            addOrderRecordPO.setSpeedType(cmsSnapshot.getSpeedType());

            //调用用户服务获取用户基础信息
            UserInfoDTO userInfo = userInfoProxy.getUserInfo(req.getMessageId(), req.getMsisdn());
            OrderRecordMsgUtil.setUserInfoForOrderRecordPO(addOrderRecordPO, userInfo);


            //调用用户产品订购服务获取用户订购信息
            SubscribeInfoDTO subscribeInfo = userProductProxy.getUserProductSubscribeInfo(req.getMsisdn(), req.getQosProductId(), UserTypeEnum.PEOPLE);

            //计算时长
            //开始时间的毫秒值清零
            var startTime = DateUtils.removeMillis(new Date());
            Date userEndTime = DateUtils.parseDate(req.getUserEndTime(), DateUtils.DATE_TIME_FORMATTER_14); //NOSONAR
            Long duration =  DateUtils.computeDuration(startTime, userEndTime);
            log.info("开始时间为{}，用户期望结束时间为{}，计算出时长为{}", DateUtils.formatDateWithMillis(startTime),
                    DateUtils.formatDateWithMillis(userEndTime), duration);
            addOrderRecordPO.setStartTime(startTime);
            addOrderRecordPO.setDuration(duration);

            //数据有效性校验
            ValidateUtil.validateRequestData(DefOperTypeEnum.ADD, cmsSnapshot, userInfo, subscribeInfo, duration,
                    userEndTime);

            String afid = cmsSnapshot.getAfid(userInfo.getSignNetworkType());
            if(StringUtils.isEmpty(afid)) {
                log.warn("未获取到AFID，userInfo={}, cmsSnapshot={}", JsonUtil.obj2String(userInfo),
                        JsonUtil.obj2String(cmsSnapshot));
                throw new DefBearerException(DefBearerRespCodeEnum.AFID_MISSING);
            }
            addOrderRecordPO.setAfid(afid);

            if (BearerModeEnum.DEF.getCode().equals(cmsSnapshot.getBearerMode())) {
                //默载策略冲突校验
                SpeedTypeEnum requestSpeedType = SpeedTypeEnum.getByCode(cmsSnapshot.getSpeedType()); //NOSONAR
                if(requestSpeedType == null || SpeedTypeEnum.OTHER.equals(requestSpeedType)) {
                    log.error("无法识别的速率类型{}", cmsSnapshot.getSpeedType());
                    throw new DefBearerException(DefBearerRespCodeEnum.SPEED_TYPE_UNKNOWN);
                }
                Map<SpeedTypeEnum, List<QosOrderBO>> curOrdersAll = getDefAndDedOrders(req.getMsisdn(), curOrders);
                conflictOrders = checkPccStrategyConflict(requestSpeedType, curOrdersAll);
            }
            else {
                //AM-PCF策略冲突校验
                conflictAmPcfOrders = ValidateUtil.checkAmPcfConflict(curOrders, req.getQosProductId(), userEndTime,
                        addOrderRecordPO, userProductProxy, null);
            }

            //调用ID服务生成orderId
            Long orderId = idGenProxy.genOrderId(OrderTypeEnum.DEF_BEARER, req.getMsisdn(), UserTypeEnum.PEOPLE);

            //调用默载适配服务
            Resp<OperResultDataDTO> operResp = defBearerAdapterProxy.add(req.getMessageId(), userEndTime, userInfo, afid);
            OrderRecordMsgUtil.setAdapterResultForOrderRecordPO(addOrderRecordPO, operResp.getData());

            if(DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(operResp.getCode())) {
                //默载建立成功，提交延时任务并保存订单
                order = defBearerOrderAppService.createOrder(req, userInfo, startTime, userEndTime, duration, afid,
                        cmsSnapshot, orderId);
                dto.setOrderId(order.getOrderId());
                dto.setHomeProvince(order.getHomeProvince());
                addOrderRecordPO.setOrderId(orderId);
                RequestRetryCheckHelper.delFailKey(redissonClient, zsetKey);
                sendConflictOrderMessages(conflictOrders, conflictAmPcfOrders);
            } else if(DefBearerAdapterRespCodeEnum.USER_NOT_EXIST.getCode().equals(operResp.getCode())) {
                //IOM返回3001-用户不存在，需要调用用户服务去做开户
                userInfoProxy.openAccountForUser(req.getMessageId(), req.getMsisdn());
                throw new DefBearerException(DefBearerRespCodeEnum.USER_OPEN_SUCCESS);
            } else {
                throw new ProxyException(operResp);
            }
        } catch (DefBearerException de) {
            //校验未通过，增加失败次数
            addRespCode = de.getCode();
            addRespDesc = de.getDesc();
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw de;
        } catch (ProxyException pe) {
            addRespCode = pe.getResp().getCode();
            addRespDesc = pe.getResp().getMsg();
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw pe;
        } catch (Exception e) {
            log.error("默载申请异常！req={}", JsonUtil.obj2String(req), e);
            addRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            addRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        }
        finally {
            //释放用户锁
            redisLockAppService.releaseUserLock(isAcquire, lock);
            //发送调用流水
            addOrderRecordPO.setRespCode(addRespCode);
            addOrderRecordPO.setRespDesc(addRespDesc);
            addOrderRecordPO.setReturnTime(new Date());
            //异步发送调用流水
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(addOrderRecordPO)),
                    msgSendExecutor);
        }
        return dto;
    }


    private void checkOrderExist(String cmServiceId, List<QosDefbearerOrderPO> curOrders) {
        if(curOrders != null && !curOrders.isEmpty()) {
            for(QosDefbearerOrderPO order : curOrders) {
                if(order.getCmServiceId().equals(cmServiceId)) {
                    log.info("默载申请，已存在生效中订单，order={}", order);
                    throw new DefBearerException(DefBearerRespCodeEnum.USER_ORDER_EXIST);
                }
            }
        }
    }


    @SuppressWarnings({"java:S3776","java:S1541"})
    private Map<SpeedTypeEnum, List<QosOrderBO>> getDefAndDedOrders(String userId,
                                                                    List<QosDefbearerOrderPO> curOrders) {
        //策略冲突校验，获取新老系统所有专默载订单
        Map<SpeedTypeEnum, List<QosOrderBO>> curOrdersAll = new EnumMap<>(SpeedTypeEnum.class);
        //加速列表
        List<QosOrderBO> upList = new ArrayList<>();
        //限速列表
        List<QosOrderBO> downList = new ArrayList<>();

        if(curOrders != null && !curOrders.isEmpty()) {
            log.info("新系统默载订单：{}", curOrders);
            for (QosDefbearerOrderPO order : curOrders) {
                if (BearerModeEnum.AM_PCF.getCode().equals(order.getBearerMode())) {
                    log.info("默载策略冲突校验，跳过AM-PCF订单，orderId={}", order.getOrderId());
                    continue;
                }
                SpeedTypeEnum orderSpeedType = SpeedTypeEnum.getByCode(order.getSpeedType()); //NOSONAR
                QosOrderBO qosOrder = createQosOrder(order);
                switch (orderSpeedType) {
                    case UP -> upList.add(qosOrder);
                    case DOWN -> downList.add(qosOrder);
                    default -> log.error("不可识别的速率类型，value={}", order.getSpeedType());
                }
            }
        }

        //获取新系统专载订单
        List<DedBearerOrderDTO> dedBearerOrders = dedBearerProxy.queryDedBearerOrders(userId);
        if(dedBearerOrders != null && !dedBearerOrders.isEmpty()) {
            log.info("新系统专载订单：{}", dedBearerOrders);
            for (DedBearerOrderDTO dedBearerOrder : dedBearerOrders) {
                QosOrderBO qosOrder = createQosDedOrder(dedBearerOrder);
                upList.add(qosOrder);
            }
        }

        //获取老系统订单
        try {
            List<UserUserBearCurinfoPO> pccOrders = pccCurBearRepo.getPccCurBears(userId);
            if(pccOrders != null && !pccOrders.isEmpty()) {
                log.info("老系统订单：{}", pccOrders);
                for (UserUserBearCurinfoPO pccOrder : pccOrders) {
                    SpeedTypeEnum pccOrderSpeedType = SpeedTypeEnum.getByCode(pccOrder.getSpeedType()); //NOSONAR
                    QosOrderBO qosOrder = createQosOrderPCC(pccOrder);
                    switch (pccOrderSpeedType) {
                        case UP -> upList.add(qosOrder);
                        case DOWN -> downList.add(qosOrder);
                        default -> log.error("未知速率类型，value={}", pccOrder.getSpeedType());
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询老系统订单异常！userId={}", userId, e);
        }
        curOrdersAll.put(SpeedTypeEnum.UP, upList);
        curOrdersAll.put(SpeedTypeEnum.DOWN, downList);
        return curOrdersAll;
    }

    private List<QosOrderBO> checkPccStrategyConflict(SpeedTypeEnum requestSpeedType, Map<SpeedTypeEnum, List<QosOrderBO>> curOrdersAll) {
        List<QosOrderBO> conflictOrders = new ArrayList<>();
        switch (requestSpeedType) {
            case DOWN -> {
                if (!curOrdersAll.get(SpeedTypeEnum.DOWN).isEmpty()) {
                    log.info("策略冲突校验失败，请求的速率类型为限速，系统已存在限速类订单");
                    throw new DefBearerException(DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT);
                }
                conflictOrders.addAll(curOrdersAll.get(SpeedTypeEnum.UP));
            }
            case UP -> { //加速
                if (!curOrdersAll.get(SpeedTypeEnum.DOWN).isEmpty()) {
                    log.info("策略冲突校验失败，请求的速率类型为加速，系统已存在限速类订单");
                    throw new DefBearerException(DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT);
                }
                for (QosOrderBO order : curOrdersAll.get(SpeedTypeEnum.UP)) {
                    if(order.getBearerMode().equals(BearerModeEnum.DEF.getCode())) {
                        log.info("策略冲突校验失败，请求的速率类型为加速，系统已存在默载加速订单");
                        throw new DefBearerException(DefBearerRespCodeEnum.PCC_STRATEGY_CONFLICT);
                    }
                }
            }
            default -> log.error("未知的请求速率类型：{}", requestSpeedType);
        }
        return conflictOrders;
    }

    private QosOrderBO createQosOrder(QosDefbearerOrderPO defbearerOrder) {
        QosOrderBO order = new QosOrderBO(); //NOSONAR
        order.setPccOrder(false);
        order.setId(String.valueOf(defbearerOrder.getOrderId()));
        order.setCmServiceId(defbearerOrder.getCmServiceId());
        order.setMsisdn(defbearerOrder.getMsisdn());
        order.setImsi(defbearerOrder.getImsi());
        order.setNeId(defbearerOrder.getNeId());
        order.setUserType(defbearerOrder.getUserType());
        order.setSignNetworkType(defbearerOrder.getSignNetworkType());
        order.setBearerMode(BearerModeEnum.DEF.getCode());
        return order;
    }

    private QosOrderBO createQosDedOrder(DedBearerOrderDTO dedbearerOrder) {
        var order = new QosOrderBO();
        order.setPccOrder(false);
        order.setId(String.valueOf(dedbearerOrder.getOrderId()));
        order.setCmServiceId(dedbearerOrder.getCmServiceId());
        order.setMsisdn(dedbearerOrder.getMsisdn());
        order.setImsi(dedbearerOrder.getImsi());
        order.setUserType(dedbearerOrder.getUserType());
        order.setSignNetworkType(dedbearerOrder.getSignNetworkType());
        order.setBearerMode(BearerModeEnum.DED.getCode());
        return order;
    }

    private QosOrderBO createQosOrderPCC(UserUserBearCurinfoPO pccOrder) {
        QosOrderBO order = new QosOrderBO(); //NOSONAR
        order.setPccOrder(true);
        order.setId(pccOrder.getCorrelationId());
        order.setCmServiceId(pccOrder.getCmServiceId());
        order.setMsisdn(pccOrder.getMsisdn());
        order.setImsi(pccOrder.getImsi());
        order.setNeId(pccOrder.getNeId());
        order.setUserType(pccOrder.getUserType());
        order.setSignNetworkType(pccOrder.getSignNetworkType());
        order.setBearerMode(pccOrder.getBearerMode());
        return order;
    }


    private void sendConflictOrderMessages(List<QosOrderBO> conflictOrders,
                                           List<QosDefBearerResumeTaskPO> conflictAmPcfOrders) {
        if (CollectionUtils.isNotEmpty(conflictOrders)){
            log.info("开始发送默载策略覆盖消息，需要覆盖的订单信息为{}", conflictOrders);
            CompletableFuture.runAsync(RunnableWrapper.of(() -> kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_DEFBEARER_CONFLICT, conflictOrders)), msgSendExecutor);
        }
        else if (CollectionUtils.isNotEmpty(conflictAmPcfOrders)) {
            log.info("开始发送AM-PCF策略覆盖消息，需要覆盖的订单信息为{}", conflictAmPcfOrders);
            CompletableFuture.runAsync(RunnableWrapper.of(() -> kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_AMPCF_CONFLICT, conflictAmPcfOrders)), msgSendExecutor);
        }
    }

    private QosDefbearerOrderRecordPO initQosDefbearerOrderRecord(AddDefBearerReq req, Date orderTime) {
        var addOrderRecordPO = new QosDefbearerOrderRecordPO();
        addOrderRecordPO.setMessageId(req.getMessageId());
        addOrderRecordPO.setOrderSource(req.getOrderSource());
        addOrderRecordPO.setUserId(req.getMsisdn());
        addOrderRecordPO.setMsisdn(req.getMsisdn());
        addOrderRecordPO.setOrderTime(orderTime);
        addOrderRecordPO.setQosProductId(req.getQosProductId());
        addOrderRecordPO.setCmServiceId(req.getCmServiceId());
        addOrderRecordPO.setUserEndTime(req.getUserEndTime());
        addOrderRecordPO.setUserType(UserTypeEnum.PEOPLE.getCode());
        addOrderRecordPO.setOperType(DefOperTypeEnum.ADD.getCode());
        return addOrderRecordPO;
    }
}
