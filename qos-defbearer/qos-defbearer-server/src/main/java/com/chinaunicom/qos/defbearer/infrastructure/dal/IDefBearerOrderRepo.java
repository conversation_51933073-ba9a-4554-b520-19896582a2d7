package com.chinaunicom.qos.defbearer.infrastructure.dal;

import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;

import java.util.List;
import java.util.Map;

public interface IDefBearerOrderRepo {
    List<QosDefbearerOrderPO> getValidOrders(String userId, List<String> cmServiceIds, Integer qosProductId);

    List<QosDefbearerOrderPO> getOrders(String userId, List<String> cmServiceIds, Integer qosProductId);

    List<QosDefbearerOrderPO> getValidOrdersByUserId(String userId);

    QosDefbearerOrderPO getOrderByUserIdCmServiceId(String userId, String cmServiceId);

    QosDefbearerOrderPO getOrderByUserIdOrderId(String userId, Long orderId);

    int updateOrder(Map<String,Object> map);

    int saveOrder(QosDefbearerOrderPO order);
}
