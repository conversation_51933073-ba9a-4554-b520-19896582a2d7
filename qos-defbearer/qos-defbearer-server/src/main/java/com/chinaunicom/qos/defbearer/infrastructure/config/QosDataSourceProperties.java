package com.chinaunicom.qos.defbearer.infrastructure.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-12-02 15:29
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.qos")
@ConditionalOnProperty(prefix = "spring.datasource.qos", name = "url")
public class QosDataSourceProperties extends DataSourceProperties {
}
