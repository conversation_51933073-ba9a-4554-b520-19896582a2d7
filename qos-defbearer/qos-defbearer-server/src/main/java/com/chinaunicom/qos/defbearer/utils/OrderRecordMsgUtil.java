package com.chinaunicom.qos.defbearer.utils;

import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.NE_ID_PEOPLE;

/**
 * <AUTHOR>
 * @date 2025-4-22 10:02
 */

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderRecordMsgUtil {

    public static void setUserInfoForOrderRecordPO(QosDefbearerOrderRecordPO orderRecordPO, UserInfoDTO userInfo) {
        orderRecordPO.setSignNetworkType(userInfo.getSignNetworkType());
        orderRecordPO.setHomeProvince(userInfo.getHomeProvince());
        orderRecordPO.setVisitProvince(userInfo.getVisitProvince());
        orderRecordPO.setImsi(userInfo.getImsi());
    }

    public static void setAdapterResultForOrderRecordPO(QosDefbearerOrderRecordPO orderRecordPO,
                                                        OperResultDataDTO respData) {
        if(respData != null) {
            orderRecordPO.setOperTime(respData.getNeOperTime());
            orderRecordPO.setRespTime(respData.getNeRespTime());
            orderRecordPO.setNeId(NE_ID_PEOPLE);
        }
    }

    public static void setOrderRecordPOByQosDefbearerOrderPO(QosDefbearerOrderRecordPO orderRecordPO,
                                                             QosDefbearerOrderPO orderPO) {
        orderRecordPO.setQosProductId(orderPO.getQosProductId());
        orderRecordPO.setCmServiceId(orderPO.getCmServiceId());
        orderRecordPO.setMsisdn(orderPO.getMsisdn());
        orderRecordPO.setImsi(orderPO.getImsi());
        orderRecordPO.setHomeProvince(orderPO.getHomeProvince());
        orderRecordPO.setVisitProvince(orderPO.getVisitProvince());
        orderRecordPO.setSignNetworkType(orderPO.getSignNetworkType());
        orderRecordPO.setOrderId(orderPO.getOrderId());
        orderRecordPO.setAfid(orderPO.getAfid());
        orderRecordPO.setBearerMode(orderPO.getBearerMode());
        orderRecordPO.setSpeedType(orderPO.getSpeedType());
    }

}
