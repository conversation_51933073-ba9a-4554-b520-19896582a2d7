package com.chinaunicom.qos.defbearer.infrastructure.config;

import lombok.SneakyThrows;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2024-12-02 16:01
 */
@Configuration
@MapperScan(value = {"com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.pcc"}, sqlSessionFactoryRef =
        "pccSqlSessionFactory")
public class PccDBConfig {
    private static final String PCC_MAPPER_LOCATION = "classpath:mapper/pcc/*.xml";
    @Resource(name = "pccDatasource")
    private DataSource pccDatasource;

    @Bean("pccSqlSessionFactory")
    @SneakyThrows
    public SqlSessionFactory pccSqlSessionFactory() {
        var pccSqlSessionFactoryBean = new SqlSessionFactoryBean();
        pccSqlSessionFactoryBean.setDataSource(pccDatasource);
        pccSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(PccDBConfig.PCC_MAPPER_LOCATION));
        pccSqlSessionFactoryBean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return pccSqlSessionFactoryBean.getObject();
    }
}
