package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.user.api.dto.ProductWithOrderPriorityDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.enums.UserResponseCodeEnum;
import com.chinaunicom.qos.user.api.request.QueryProductOrderPrioritiesReq;
import com.chinaunicom.qos.user.api.request.SubscribeQueryReq;
import com.chinaunicom.qos.user.api.service.UserProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class UserProductProxy {
    @DubboReference(timeout = 1000)
    private UserProductService userProductService;

    public SubscribeInfoDTO getUserProductSubscribeInfo(String userId, Integer qosProductId, UserTypeEnum userType) {
        SubscribeQueryReq subscribeQueryReq = new SubscribeQueryReq(); //NOSONAR
        if (userType == UserTypeEnum.IOT) {
            subscribeQueryReq.setImsi(userId);
        } else {
            subscribeQueryReq.setMsisdn(userId);
        }
        subscribeQueryReq.setQosProductId(qosProductId);
        subscribeQueryReq.setUserType(userType.getCode());
        Resp<SubscribeInfoDTO> subscribeInfoResp;
        SubscribeInfoDTO subscribeInfo;
        try {
            log.info("开始调用用户服务查询订购信息，req={}", subscribeQueryReq);
            subscribeInfoResp = userProductService.querySubscribeInfo(subscribeQueryReq);
            log.info("调用用户服务查询订购信息结束，resp={}", JsonUtil.obj2String(subscribeInfoResp));
        } catch (Exception e) {
            log.error("调用用户服务查询用户订购信息异常！req={}", subscribeQueryReq, e);
            throw new DefBearerException(userType == UserTypeEnum.IOT ? DefBearerRespCodeEnum.IOT_QUERY_USER_EXCEPTION :DefBearerRespCodeEnum.QUERY_USER_EXCEPTION);
        }
        if(subscribeInfoResp.getCode() != UserResponseCodeEnum.R_SUCCESS.getCode()) {
            log.info("用户服务查询订购信息返回失败！");
            throw new ProxyException(subscribeInfoResp);
        }
        subscribeInfo = subscribeInfoResp.getData();
        return subscribeInfo;
    }

    public List<ProductWithOrderPriorityDTO> queryProductOrderPriorities(List<Integer> qosProductIds) {
        var req = new QueryProductOrderPrioritiesReq();
        req.setQosProductIds(qosProductIds);
        List<ProductWithOrderPriorityDTO> data;
        try {
            log.info("开始调用用户服务批量查询产品优先级信息, req={}", JsonUtil.obj2String(req));
            Resp<List<ProductWithOrderPriorityDTO>> resp = userProductService.queryProductOrderPriorities(req);
            log.info("调用用户服务批量查询产品优先级信息结束，resp={}", JsonUtil.obj2String(resp));
            data = resp.getData();
        } catch (Exception e) {
            log.error("调用用户服务批量查询产品优先级信息异常！req={}", req, e);
            throw new DefBearerException(DefBearerRespCodeEnum.QUERY_USER_EXCEPTION.getCode(),
                    DefBearerRespCodeEnum.QUERY_USER_EXCEPTION.getDesc()+": "+e.getMessage());
        }
        return data;
    }
}
