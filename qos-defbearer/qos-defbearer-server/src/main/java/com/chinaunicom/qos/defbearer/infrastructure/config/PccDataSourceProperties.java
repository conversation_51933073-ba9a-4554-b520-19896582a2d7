package com.chinaunicom.qos.defbearer.infrastructure.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-12-02 15:00
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.pcc")
@ConditionalOnProperty(prefix = "spring.datasource.pcc", name = "url")
public class PccDataSourceProperties extends DataSourceProperties {
}
