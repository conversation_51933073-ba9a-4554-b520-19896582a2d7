package com.chinaunicom.qos.defbearer.infrastructure.producer;

import com.chinaunicom.qos.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class KafkaProducer {
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void send(String topic, Object obj) {
        try {
            var msg = JsonUtil.obj2String(obj);
            kafkaTemplate.send(topic, msg);
        } catch (Exception e) {
            log.error("发送kafka异常，topic={}, obj={}", topic, obj, e);
        }
    }
}
