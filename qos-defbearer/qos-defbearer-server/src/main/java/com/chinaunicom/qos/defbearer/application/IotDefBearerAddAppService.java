package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.*;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.DurationCheck;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.common.utils.RequestRetryCheckHelper;
import com.chinaunicom.qos.defbearer.adapter.api.dto.IotDefBearerAdapterDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.dto.IotDefBearerAddDTO;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerAddReq;
import com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.repo.DefBearerOrderRepo;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.*;
import com.chinaunicom.qos.idgen.api.enums.OrderTypeEnum;
import com.chinaunicom.qos.snapshot.api.dto.CmServiceCheckConfigDTO;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 物网默载申请业务处理
 *
 * <AUTHOR>
 * @date 2025/1/23 16:53
 */
@Service
@Slf4j
public class IotDefBearerAddAppService {
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisLockAppService redisLockAppService;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private DefBearerOrderRepo defBearerOrderRepo;

    @Resource
    private CmSnapShotProxy cmSnapShotProxy;

    @Resource
    private UserProductProxy userProductProxy;

    @Resource
    private IdGenProxy idGenProxy;

    @Resource
    private IotDefBearerAdapterProxy iotDefBearerAdapterProxy;

    @Resource
    private IotUserInfoProxy iotUserInfoProxy;

    @Resource
    DelayTaskProxy delayTaskProxy;

    @Resource
    IotDefBearerRecordAppService iotDefBearerRecordAppService;

    public IotDefBearerAddDTO add(IotDefBearerAddReq req) {
        var orderTime = new Date();
        //失败重发拒绝校验：5分钟之内连续失败5次，拒绝请求20分钟
        var rejectKey = getUserFailKey(CommonConstants.RedisKey.REJECT_DEF_BEARER_USER_FAIL_ADD, req);
        var zsetKey = getUserFailKey(CommonConstants.RedisKey.ZSET_DEF_BEARER_USER_FAIL_ADD, req);
        if (RequestRetryCheckHelper.checkFailureReject(redissonClient, rejectKey)) {
            log.info("失败重发拒绝，rejectKey={}", rejectKey);
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_FAIL_TOO_MANY_TIMES);
        }
        //初始化赋值orderRecordPO对象
        var orderRecordPO = initQosDefbearerOrderRecordPO(req, orderTime);
        //根据imsi加锁
        var lock = new RedisDistributedRedLock(redissonClient, req.getImsi(), 60 * 1000L, 10 * 1000L);
        var isAcquire = redisLockAppService.acquireUserLock(req.getImsi(), lock, UserTypeEnum.IOT);
        try {
            //校验生效订单数据
            List<QosDefbearerOrderPO> curOrders = defBearerOrderRepo.getValidOrdersByUserId(req.getImsi());
            checkOrderExist(req.getCmServiceId(), curOrders);
            //查询快照服务
            var cmsSnapshot = cmSnapShotProxy.getCmsConfigInfo(req.getMessageId(), req.getCmServiceId(),null,UserTypeEnum.IOT);
            //查询用户产品订购数据
            var subscribeInfo = userProductProxy.getUserProductSubscribeInfo(req.getImsi(), req.getQosProductId(),UserTypeEnum.IOT);
            //计算申请时长、开始时间赋值orderRecordPO
            calculateAndSetDurationStart(orderRecordPO);
            //业务校验
            businessVerification(cmsSnapshot, subscribeInfo,orderRecordPO);
            //生成订单号
            var orderId = generateOrderId(orderRecordPO);
            //获取AFID、SpeedType赋值orderRecordPO
            iotDefBearerRecordAppService.setAfidAndSpeedType(cmsSnapshot, orderRecordPO);
            //调用物网默载适配服务
            Resp<IotDefBearerAdapterDTO> iotDefBearerAdapterResp = iotDefBearerAdapterProxy.add(orderRecordPO);
            //处理返回结果
            if(iotDefBearerAdapterResp.getData()!=null){
                var iotDefBearerAdapterDTO = iotDefBearerAdapterResp.getData();
                orderRecordPO.setOperTime(DateUtils.toDate(iotDefBearerAdapterDTO.getNeReqTime()));
                orderRecordPO.setRespTime(DateUtils.toDate(iotDefBearerAdapterDTO.getNeRespTime()));
            }
            if(DefBearerAdapterRespCodeEnum.IOT_USER_NOT_EXIST.getCode().equals(iotDefBearerAdapterResp.getCode())){
                //南向返回未开户调用物网开户
                iotUserInfoProxy.open(orderRecordPO);
                throw new DefBearerException(DefBearerRespCodeEnum.IOT_USER_OPEN_SUCCESS);
            }else if(!DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(iotDefBearerAdapterResp.getCode())){
                throw new ProxyException(iotDefBearerAdapterResp);
            }
            //赋值订单ID
            orderRecordPO.setOrderId(orderId);
            //默载建立成功，提交延时任务并保存订单
            createOrderAndDelayTask(orderRecordPO, cmsSnapshot);
            var iotDefBearerAddDTO = new IotDefBearerAddDTO();
            iotDefBearerAddDTO.setOrderId(orderId);
            //删除失败的key
            RequestRetryCheckHelper.delFailKey(redissonClient, zsetKey);
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.R_SUCCESS.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.R_SUCCESS.getDesc());
            return iotDefBearerAddDTO;
        } catch (DefBearerException de) {
            //校验未通过，增加失败次数
            orderRecordPO.setRespCode(de.getCode());
            orderRecordPO.setRespDesc(de.getDesc());
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw de;
        } catch (ProxyException pe) {
            orderRecordPO.setRespCode(pe.getResp().getCode());
            orderRecordPO.setRespDesc(pe.getResp().getMsg());
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw pe;
        } catch (Exception e) {
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc() + e.getMessage());
            RequestRetryCheckHelper.addFailCount(redissonClient, zsetKey, rejectKey, req.getMessageId());
            throw e;
        } finally {
            redisLockAppService.releaseUserLock(isAcquire, lock);
            //异步发送调用流水
            orderRecordPO.setReturnTime(new Date());
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(orderRecordPO)), msgSendExecutor);
        }
    }

    private void createOrderAndDelayTask(QosDefbearerOrderRecordPO orderRecordPO, CmsSnapshotDTO cmsSnapshot) {
        var order = new QosDefbearerOrderPO();
        order.setOrderId(orderRecordPO.getOrderId());
        order.setUserId(orderRecordPO.getUserId());
        order.setQosProductId(orderRecordPO.getQosProductId());
        order.setCmServiceId(orderRecordPO.getCmServiceId());
        order.setStatus(CommonConstants.OrderStatus.ORDER_VALID);
        order.setStartTime(orderRecordPO.getStartTime());
        order.setUserEndTime(DateUtils.parseDate(orderRecordPO.getUserEndTime(), DateUtils.DATE_TIME_FORMATTER_14));
        order.setDuration(orderRecordPO.getDuration());
        order.setMsisdn(orderRecordPO.getMsisdn());
        order.setImsi(orderRecordPO.getImsi());
        order.setHomeProvince(orderRecordPO.getHomeProvince());
        order.setVisitProvince(orderRecordPO.getVisitProvince());
        order.setUserType(orderRecordPO.getUserType());
        order.setSignNetworkType(orderRecordPO.getSignNetworkType());
        order.setAfid(orderRecordPO.getAfid());
        order.setSpeedType(orderRecordPO.getSpeedType());
        order.setNeId(orderRecordPO.getNeId());
        int taskFlag = CommonConstants.DelayTask.TASK_NOT_CREATED;
        if (orderRecordPO.getDuration() <= DefBearerConstants.ONE_DAY) {
            //请求时长在24小时以内，创建延时任务，否则交给定时任务去创建
            taskFlag = delayTaskProxy.createDelayTask(order.getUserEndTime(), order);
        }
        order.setDelayTaskFlag(taskFlag);
        order.setSnapshotVersion(cmsSnapshot.getSnapshotVersion());
        try {
            defBearerOrderRepo.saveOrder(order);
        } catch (Exception e) {
            log.error("插入订单表失败！order={}", order, e);
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION);
        }
    }

    private Long generateOrderId(QosDefbearerOrderRecordPO orderRecordPO) {
        return idGenProxy.genOrderId(OrderTypeEnum.DEF_BEARER, orderRecordPO.getImsi(),UserTypeEnum.IOT);
    }

    private void businessVerification(CmsSnapshotDTO cmsSnapshot, SubscribeInfoDTO subscribeInfo, QosDefbearerOrderRecordPO orderRecordPO) {
        //通信服务状态校验，只支持状态为“使用中”的通信服务
        if(!StatusEnum.ACTIVE.getStatus().equals(cmsSnapshot.getCmServiceStatus())) {
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_CM_SERVICE_INVALID);
        }
        //产品订购校验
        if(subscribeInfo.getEffectiveTime().after(orderRecordPO.getStartTime()) ||
                subscribeInfo.getExpireTime().before(DateUtils.parseDate(orderRecordPO.getUserEndTime(), DateUtils.DATE_TIME_FORMATTER_14))) {
            log.info("产品订购校验失败，订购生效时间={}，订购失效时间={}，默载期望终止时间={}，当前时间={}",
                    DateUtils.formatDate(subscribeInfo.getEffectiveTime()),
                    DateUtils.formatDate(subscribeInfo.getExpireTime()),
                    orderRecordPO.getUserEndTime(), DateUtils.formatDate(orderRecordPO.getStartTime()));
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_PRODUCT_SUBSCRIBE_PERIOD_MISMATCH);
        }
        //产品和通信服务一致性校验
        if(!subscribeInfo.getCmServiceIdList().contains(cmsSnapshot.getCmServiceId())) {
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_PRODUCT_CM_SERVICE_MISMATCH);
        }
        //进行通信服务配置的规则校验
        serviceConfigVerification(cmsSnapshot , orderRecordPO);
    }

    private void serviceConfigVerification(CmsSnapshotDTO cmsSnapshot, QosDefbearerOrderRecordPO orderRecordPO) {
        if(cmsSnapshot.getCheckConfigList() != null && !cmsSnapshot.getCheckConfigList().isEmpty()) {
            for(CmServiceCheckConfigDTO config : cmsSnapshot.getCheckConfigList()) {
                //时长校验
                if(config.getCheckKey().equals(CmServiceCheckKeyEnum.DURATION.getKey()) &&
                        DurationCheck.checkDurationFailed(config.getCheckValue(), orderRecordPO.getDuration())) {
                    log.info("时长校验失败，通信服务配置的时长规则={}，duration={}", config.getCheckValue(), orderRecordPO.getDuration());
                    throw new DefBearerException(DefBearerRespCodeEnum.IOT_DURATION_INVALID);
                }
            }
        }
    }

    private void calculateAndSetDurationStart(QosDefbearerOrderRecordPO orderRecordPO) {
        var startTime = LocalDateTime.now().withNano(0);
        var userEndTime = DateUtils.parseLocalDateTime(orderRecordPO.getUserEndTime(), null);
        Long duration =  DateUtils.calculateSecondsBetween(startTime, userEndTime);
        log.info("开始时间为{}，用户期望结束时间为{}，计算出时长为{}", startTime, userEndTime, duration);
        orderRecordPO.setStartTime(DateUtils.toDate(startTime));
        orderRecordPO.setDuration(duration);
    }

    private void checkOrderExist(String cmServiceId, List<QosDefbearerOrderPO> curOrders) {
        if(curOrders != null && !curOrders.isEmpty()) {
            for(QosDefbearerOrderPO order : curOrders) {
                if(order.getCmServiceId().equals(cmServiceId)) {
                    log.info("物网默载申请，已存在生效中订单，order={}", order);
                    throw new DefBearerException(DefBearerRespCodeEnum.IOT_USER_ORDER_EXIST);
                }
            }
        }
    }

    private @NotNull String getUserFailKey(String prefix, IotDefBearerAddReq req) {
        return prefix + req.getImsi() + ":" + req.getCmServiceId();
    }

    private @NotNull QosDefbearerOrderRecordPO initQosDefbearerOrderRecordPO(IotDefBearerAddReq req, Date orderTime) {
        var orderRecordPO = new QosDefbearerOrderRecordPO();
        orderRecordPO.setOrderTime(orderTime);
        orderRecordPO.setMessageId(req.getMessageId());
        orderRecordPO.setQosProductId(req.getQosProductId());
        orderRecordPO.setCmServiceId(req.getCmServiceId());
        orderRecordPO.setImsi(req.getImsi());
        orderRecordPO.setUserEndTime(req.getUserEndTime());
        orderRecordPO.setOrderSource(req.getOrderSource());
        orderRecordPO.setOperType(DefOperTypeEnum.ADD.getCode());
        orderRecordPO.setUserId(req.getImsi());
        orderRecordPO.setUserType(UserTypeEnum.IOT.getCode());
        orderRecordPO.setSignNetworkType(SignNetworkTypeEnum.FIVE_GEN_PACKAGE.getCode());
        return orderRecordPO;
    }


}
