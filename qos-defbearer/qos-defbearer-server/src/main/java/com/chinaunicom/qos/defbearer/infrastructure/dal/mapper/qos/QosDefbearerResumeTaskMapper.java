package com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos;

import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-25 15:47
 */

@Mapper
public interface QosDefbearerResumeTaskMapper {

    int deleteByOrderIdUserId(@Param("originOrderId") Long originOrderId, @Param("userId") String userId);

    List<QosDefBearerResumeTaskPO> queryByUserId(@Param("userId") String userId);

    int updateTaskExecutionParams(@Param("task") QosDefBearerResumeTaskPO taskPO);

}
