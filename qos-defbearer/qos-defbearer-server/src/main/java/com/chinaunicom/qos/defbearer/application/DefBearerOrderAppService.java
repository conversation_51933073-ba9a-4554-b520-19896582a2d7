package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.AddDefBearerReq;
import com.chinaunicom.qos.defbearer.api.request.ResumeDefBearerOrderReq;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerOrderRepo;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.DelayTaskProxy;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants.*;
import static com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants.DELAY_TASK_FLAG;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.DelayTask.TASK_NOT_CREATED;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.NE_ID_PEOPLE;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.OrderStatus.ORDER_TERMINATED;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.OrderStatus.ORDER_VALID;

/**
 * <AUTHOR>
 * @date 2025-4-22 09:30
 */
@Slf4j
@Service
public class DefBearerOrderAppService {
    @Resource
    private IDefBearerOrderRepo defBearerOrderRepo;

    @Resource
    private DelayTaskProxy delayTaskProxy;

    public List<QosDefbearerOrderPO> getValidOrdersByUserId(String userId) {
        return defBearerOrderRepo.getValidOrdersByUserId(userId);
    }

    public QosDefbearerOrderPO getOrderByUserIdCmServiceId(String userId, String cmServiceId) {
        QosDefbearerOrderPO order = null;
        try {
            order = defBearerOrderRepo.getOrderByUserIdCmServiceId(userId, cmServiceId);
        } catch (Exception e) {
            log.error("获取用户订单数据异常，userId={}, cmServiceId={}", userId, cmServiceId, e);
        }
        return order;
    }


    //查询status=0的订单
    public QosDefbearerOrderPO getOrderByUserIdOrderId(String userId, Long orderId) {
        QosDefbearerOrderPO order;
        try {
            order = defBearerOrderRepo.getOrderByUserIdOrderId(userId, orderId);
        } catch (Exception e) {
            log.error("获取用户订单数据异常，userId={}, orderId={}", userId, orderId, e);
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        }
        //查不到订单，返回失败
        if(order == null) {
            log.info("查询不到用户订单，userId={}, orderId={}", userId, orderId);
            throw new DefBearerException(DefBearerRespCodeEnum.ORDER_ID_INVALID);
        }
        return order;
    }


    @SuppressWarnings("java:S107")
    public QosDefbearerOrderPO createOrder(AddDefBearerReq req, UserInfoDTO userInfo, Date startTime, Date userEndTime,
                                      Long duration, String afid, CmsSnapshotDTO cmsSnapshot, Long orderId) {
        var order = new QosDefbearerOrderPO();
        order.setOrderId(orderId);
        order.setUserId(req.getMsisdn());
        order.setQosProductId(req.getQosProductId());
        order.setCmServiceId(req.getCmServiceId());
        order.setStatus(ORDER_VALID);
        order.setStartTime(startTime);
        order.setUserEndTime(userEndTime);
        order.setDuration(duration);
        order.setMsisdn(req.getMsisdn());
        order.setImsi(userInfo.getImsi());
        order.setHomeProvince(userInfo.getHomeProvince());
        order.setVisitProvince(userInfo.getVisitProvince());
        order.setUserType(UserTypeEnum.PEOPLE.getCode());
        order.setSignNetworkType(userInfo.getSignNetworkType());
        order.setAfid(afid);
        order.setBearerMode(cmsSnapshot.getBearerMode());
        order.setSpeedType(cmsSnapshot.getSpeedType());
        order.setNeId(NE_ID_PEOPLE);
        int taskFlag = TASK_NOT_CREATED; //NOSONAR
        if (duration <= ONE_DAY) {
            //请求时长在24小时以内，创建延时任务，否则交给定时任务去创建
            taskFlag = delayTaskProxy.createDelayTask(userEndTime, order);
        }
        order.setDelayTaskFlag(taskFlag);
        order.setSnapshotVersion(cmsSnapshot.getSnapshotVersion());
        saveOrder(order);
        return order;
    }

    public void createOrder(ResumeDefBearerOrderReq req, Date startTime, Date userEndTime,
                            Long duration, Long orderId) {
        var order = new QosDefbearerOrderPO();
        order.setOrderId(orderId);
        order.setUserId(req.getUserId());
        order.setQosProductId(req.getQosProductId());
        order.setCmServiceId(req.getCmServiceId());
        order.setStatus(ORDER_VALID);
        order.setStartTime(startTime);
        order.setUserEndTime(userEndTime);
        order.setDuration(duration);
        order.setMsisdn(req.getMsisdn());
        order.setImsi(req.getImsi());
        order.setHomeProvince(req.getHomeProvince());
        order.setVisitProvince(req.getVisitProvince());
        order.setUserType(UserTypeEnum.PEOPLE.getCode());
        order.setSignNetworkType(req.getSignNetworkType());
        order.setAfid(req.getAfid());
        order.setSpeedType(req.getSpeedType());
        order.setBearerMode(req.getBearerMode());
        order.setNeId(NE_ID_PEOPLE);
        int taskFlag = TASK_NOT_CREATED; //NOSONAR
        if (duration <= ONE_DAY) {
            //请求时长在24小时以内，创建延时任务，否则交给定时任务去创建
            taskFlag = delayTaskProxy.createDelayTask(userEndTime, order);
        }
        order.setDelayTaskFlag(taskFlag);
        order.setSnapshotVersion(req.getSnapshotVersion());
        saveOrder(order);
    }

    private void saveOrder(QosDefbearerOrderPO order) {
        try {
            log.info("准备插入订单表");
            defBearerOrderRepo.saveOrder(order);
            log.info("订单表插入完毕");
        } catch (Exception e) {
            log.error("插入订单表失败！order={}", order, e);
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        }
    }


    public void updateOrder(Date userEndTime, Long reqDuration, QosDefbearerOrderPO order) {
        //更新订单数据（时长、用户期望的终止时间）
        //时长：用户期望结束时间-订单原始开始时间
        Long duration = DateUtils.computeDuration(order.getStartTime(), userEndTime);
        Map<String, Object> map = new HashMap<>();
        map.put(USER_ID, order.getUserId());
        map.put(ORDER_ID, order.getOrderId());
        map.put(DURATION, duration);
        map.put(USER_END_TIME, userEndTime);
        int taskFlag = TASK_NOT_CREATED; //NOSONAR
        if(reqDuration <= ONE_DAY) {
            //请求时长在24小时以内，创建延时任务，否则交给定时任务去创建
            taskFlag = delayTaskProxy.createDelayTask(userEndTime, order);
        }
        map.put(DELAY_TASK_FLAG, taskFlag);
        log.info("开始更新订单时长和期望结束时间，参数为{}", JsonUtil.obj2String(map));
        defBearerOrderRepo.updateOrder(map);
        log.info("订单时长和期望结束时间更新完毕");
    }

    public void terminateOrder(String userId, Long orderId, Long duration, Date endTime, boolean userEndTimeFlag) {
        Map<String, Object> map = new HashMap<>();
        map.put(USER_ID, userId);
        map.put(ORDER_ID, orderId);
        map.put(DURATION, duration);
        if(userEndTimeFlag) {
            //去掉毫秒
            var userEndTime = DateUtils.removeMillis(endTime);
            map.put(USER_END_TIME, userEndTime);
        }
        map.put(END_TIME, endTime);
        map.put(STATUS, ORDER_TERMINATED);
        //更新订单状态、时长以及终止时间
        defBearerOrderRepo.updateOrder(map);
    }

}
