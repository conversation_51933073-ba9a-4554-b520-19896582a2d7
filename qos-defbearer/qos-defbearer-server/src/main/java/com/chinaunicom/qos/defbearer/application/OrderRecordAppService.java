package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.infrastructure.producer.KafkaProducer;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.IdGenProxy;
import com.chinaunicom.qos.idgen.api.enums.FlowTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 流水相关业务处理
 *
 * <AUTHOR>
 * @date 2025/1/24 11:37
 */
@Service
@Slf4j
public class OrderRecordAppService {
    @Resource
    private IdGenProxy idGenProxy;

    @Resource
    private KafkaProducer kafkaProducer;

    public void sendOrderRecordMsg(QosDefbearerOrderRecordPO orderRecordPO) {
        var id = getRecordId();
        orderRecordPO.setId(id);
        orderRecordPO.setTraceId(TraceContext.traceId());
        log.info("发送流水消息到kafka，流水号={}", id);
        kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_DEFBEARER_RECORD, orderRecordPO);
    }

    private @NotNull Long getRecordId() {
        var id = idGenProxy.genFlowId(FlowTypeEnum.DEF_BEARER);
        if (id == null) {
            log.warn("生成流水ID失败，使用自己生成的ID");
            id = System.currentTimeMillis() * 100000L + ThreadLocalRandom.current().nextInt(100000); //NOSONAR
        }
        return id;
    }
}
