package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.ResumeDefBearerOrderReq;
import com.chinaunicom.qos.defbearer.common.enums.TaskExecutionStatusEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.common.config.DefBearerResumeTaskConfig;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerResumeTaskRepo;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.UserProductProxy;
import com.chinaunicom.qos.user.api.dto.ProductWithOrderPriorityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.stream.Collectors;

import static com.chinaunicom.qos.defbearer.common.utils.OrderResumeStatusUtil.processNextResumeTask;
import static com.chinaunicom.qos.defbearer.common.utils.OrderResumeStatusUtil.stopResumeProcess;

/**
 * <AUTHOR>
 * @date 2025-4-24 15:37
 */

@Slf4j
@Service
public class DefBearerUserResumeAppService {
    @Resource
    private DefBearerOrderResumeAppService orderResumeAppService;

    @Resource
    private UserProductProxy userProductProxy;

    @Resource
    private IDefBearerResumeTaskRepo resumeTaskRepo;

    @Resource
    private DefBearerResumeTaskConfig taskConfig;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    RedisLockAppService redisLockAppService;

    @SuppressWarnings({"java:S135", "java:S3776"})
    public void resumeOrderByUser(String userId) {

        //尝试获取锁，获取不到则说明该用户正在进行恢复
        String lockKey = genLockKey(userId);
        var lock = new RedisDistributedRedLock(redissonClient, lockKey, 600 * 1000L, 0L); //NOSONAR
        boolean isAcquire = redisLockAppService.acquireUserLock(lockKey, lock, UserTypeEnum.PEOPLE);

        try {
            var resumeTasks = resumeTaskRepo.queryResumeTasksByUserId(userId);
            if (CollectionUtils.isEmpty(resumeTasks)) {
                log.info("用户{}不存在待恢复的订单", userId);
                return;
            }
            log.info("查询到{}个待恢复的订单", resumeTasks.size());

            var qosProductIds = resumeTasks.stream().map(QosDefBearerResumeTaskPO::getQosProductId).toList();
            var productOrderPriorityList = userProductProxy.queryProductOrderPriorities(qosProductIds);
            var productOrderPriorityMap =
                    productOrderPriorityList.stream().filter(o->o.getOrderPriority()!=null).collect(Collectors.toMap(ProductWithOrderPriorityDTO::getQosProductId,
                            ProductWithOrderPriorityDTO::getOrderPriority));
            //按优先级给任务排序，NULL的排在最前面
            resumeTasks.sort(Comparator.comparing(task->productOrderPriorityMap.get(task.getQosProductId()),
                    Comparator.nullsFirst(BigDecimal::compareTo)));

            var now = new Date();
            for (QosDefBearerResumeTaskPO resumeTask : resumeTasks) {
                log.info("开始尝试恢复订单{}", JsonUtil.obj2String(resumeTask));
                BigDecimal taskPriority = productOrderPriorityMap.get(resumeTask.getQosProductId());

                if (!resumeTask.getUserEndTime().after(now)) {
                    log.info("订单预期结束时间早于等于当前时间，恢复任务已无效，准备删除任务");
                    deleteResumeTask(resumeTask.getOriginOrderId(), resumeTask.getUserId());
                    continue;
                }

                Resp<Void> resumeResp = resumeOrder(resumeTask);

                if (stopResumeProcess(resumeResp.getCode(), taskPriority)) {
                    log.info("订单恢复成功或者当前存在更高优先级的订单，结束本次用户恢复");
                    break;
                }
                if (processNextResumeTask(resumeResp.getCode(), taskPriority)) {
                    log.info("订单无需恢复，或者订单恢复成功但它已不参与优先级调度，按顺序处理下一个恢复任务");
                }
                else {
                    resumeTask.setExecuteFailRetryCount(1);
                    resumeTask.setExecuteStatus(TaskExecutionStatusEnum.FAIL.getStatus());
                    resumeTask.setNextExecuteTime(DateUtils.computeDate(new Date(), taskConfig.getInitialDelayMinutes() * 60));
                    resumeTask.setExecuteCode(resumeResp.getCode());
                    resumeTask.setExecuteDesc(resumeResp.getMsg());
                    log.info("任务恢复失败，准备更新状态和下次执行时间，resumeTask={}", JsonUtil.obj2String(resumeTask));
                    updateResumeTask(resumeTask);
                    if (taskPriority != null) {
                        break;
                    }
                }

            }

        } finally {
            //释放锁
            redisLockAppService.releaseUserLock(isAcquire, lock);
        }
    }

    private Resp<Void> resumeOrder(QosDefBearerResumeTaskPO resumeTask) {
        Integer resumeCode;
        String resumeDesc;
        try {
            var resumeReq = getResumeDefBearerOrderReqFromResumeTask(resumeTask);
            Resp<Void> resumeResp = orderResumeAppService.resumeOrder(resumeReq);
            resumeCode = resumeResp.getCode();
            resumeDesc = resumeResp.getMsg();
        } catch (DefBearerException de) {
            resumeCode = de.getCode();
            resumeDesc = de.getDesc();
        } catch (ProxyException pe) {
            resumeCode = pe.getResp().getCode();
            resumeDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            resumeCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            resumeDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            log.warn("订单恢复异常", e);
        }
        return Resp.<Void>builder().code(resumeCode).msg(resumeDesc).build();
    }

    private void deleteResumeTask(Long originOrderId, String userId) {
        try {
            resumeTaskRepo.deleteResumeTask(originOrderId, userId);
            log.info("任务删除成功");
        } catch (Exception e) {
            log.error("删除任务异常，originOrderId={} userId={}", originOrderId, userId, e);
        }
    }

    private void updateResumeTask(QosDefBearerResumeTaskPO resumeTask) {
        try {
            resumeTaskRepo.updateTaskExecutionParams(resumeTask);
            log.info("更新任务成功");
        } catch (Exception e) {
            log.error("更新任务异常，resumeTask{}", JsonUtil.obj2String(resumeTask), e);
        }
    }

    private String genLockKey(String userId) {
        return "userResume_" + userId;
    }

    private ResumeDefBearerOrderReq getResumeDefBearerOrderReqFromResumeTask(QosDefBearerResumeTaskPO resumeTask) {
        var req = new ResumeDefBearerOrderReq();
        req.setOriginOrderId(resumeTask.getOriginOrderId());
        req.setUserId(resumeTask.getUserId());
        req.setQosProductId(resumeTask.getQosProductId());
        req.setCmServiceId(resumeTask.getCmServiceId());
        req.setUserEndTime(resumeTask.getUserEndTime());
        req.setMsisdn(resumeTask.getMsisdn());
        req.setImsi(resumeTask.getImsi());
        req.setHomeProvince(resumeTask.getHomeProvince());
        req.setVisitProvince(resumeTask.getVisitProvince());
        req.setUserType(resumeTask.getUserType());
        req.setSignNetworkType(resumeTask.getSignNetworkType());
        req.setAfid(resumeTask.getAfid());
        req.setBearerMode(resumeTask.getBearerMode());
        req.setSpeedType(resumeTask.getSpeedType());
        req.setSnapshotVersion(resumeTask.getSnapshotVersion());
        req.setNextExecuteTime(resumeTask.getNextExecuteTime());
        return req;
    }

}
