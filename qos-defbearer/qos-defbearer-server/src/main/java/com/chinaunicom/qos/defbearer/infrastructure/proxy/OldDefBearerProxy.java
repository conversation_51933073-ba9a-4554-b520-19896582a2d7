package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.enums.CoreNetworkSubtypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.po.UserUserBearCurinfoPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: pengb7
 * @Description: 老系统默载调用
 * @DateTime: 2024/5/29 下午3:59
 **/
@Service
@Slf4j
public class OldDefBearerProxy {
    @Resource
    RestTemplate restTemplate;

    @Value("${def.old-def-bearer-terminate-url}")
    private String url;

    public void terminateDefBearer(UserUserBearCurinfoPO userUserBearCurinfo) {
        var terminateDefBearerReq = new HashMap<String, String>();
        //message暂定使用13位时间戳+号码，老系统messageId长度限制为36位
        terminateDefBearerReq.put("messageId", Instant.now().toEpochMilli() + userUserBearCurinfo.getMsisdn());
        terminateDefBearerReq.put("msisdn", userUserBearCurinfo.getMsisdn());
        terminateDefBearerReq.put("communicationServiceId", userUserBearCurinfo.getCmServiceId());
        terminateDefBearerReq.put("reqSource", CommonConstants.OperSource.DEFBEARER);
        terminateDefBearerReq.put("delSource", CommonConstants.OperSource.SAME_CS_DEL);
        terminateDefBearerReq.put("timeStamp", String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        terminateDefBearerReq.put("networkType", String.valueOf(CoreNetworkSubtypeEnum.PEOPLE.getCode()));

        var result = new Resp<>();
        var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        var entity = new HttpEntity<>(terminateDefBearerReq, headers);
        try {
            log.info("调用老系统默载接口:{},req:{}", url, terminateDefBearerReq);
            ResponseEntity<Map<String, String>> response = restTemplate.exchange(url, HttpMethod.POST, entity, new ParameterizedTypeReference<>() {
            });
            var responseBody = response.getBody();
            log.info("调用老系统默载接口:{},resp:{}", url, responseBody);
            if (responseBody == null) {
                result.setCode(DefBearerRespCodeEnum.R_EXECUTE_RESPONSE_DATA_NULL_ERROR.getCode());
                result.setMsg(DefBearerRespCodeEnum.R_EXECUTE_RESPONSE_DATA_NULL_ERROR.getDesc());
                throw new ProxyException(result);
            }
            if (!DefBearerRespCodeEnum.R_SUCCESS.getCode().equals(Integer.parseInt(responseBody.get("respCode")))) {
                result.setCode(DefBearerRespCodeEnum.R_CALL_OLD_DEF_BEARER_ERROR.getCode());
                result.setMsg(String.format(DefBearerRespCodeEnum.R_CALL_OLD_DEF_BEARER_ERROR.getDesc(), responseBody.get("respDesc")));
                throw new ProxyException(result);
            }
        } catch (Exception e) {
            if (e instanceof ProxyException){
                throw (ProxyException) e;
            }
            log.error("调用老系统默载接口:{}异常:", url, e);
            result.setCode(DefBearerRespCodeEnum.R_CALL_OLD_DEF_BEARER_ERROR.getCode());
            result.setMsg(String.format(DefBearerRespCodeEnum.R_CALL_OLD_DEF_BEARER_ERROR.getDesc(), e.getMessage()));
            throw new ProxyException(result);
        }
    }
}
