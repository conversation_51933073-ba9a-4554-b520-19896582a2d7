package com.chinaunicom.qos.defbearer.starter.service;


import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.dto.AddDefBearerDTO;
import com.chinaunicom.qos.defbearer.api.dto.DefBearerOrderDTO;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.*;
import com.chinaunicom.qos.defbearer.api.service.DefBearerService;
import com.chinaunicom.qos.defbearer.application.*;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;


@DubboService(filter = {"constraintViolationExceptionFilter"})
@Slf4j
public class DefBearerServiceImpl implements DefBearerService {

    @Resource
    private DefBearerQueryAppService defBearerQueryAppService;

    @Resource
    private DefBearerAddAppService defBearerAddAppService;

    @Resource
    private DefBearerModifyAppService defBearerModifyAppService;

    @Resource
    private DefBearerTerminateAppService defBearerTerminateAppService;

    @Resource
    private DefBearerOrderResumeAppService defBearerOrderResumeAppService;

    @Resource
    private DefBearerUserResumeAppService defBearerUserResumeAppService;

    @Override
    public Resp<AddDefBearerDTO> add(AddDefBearerReq req) {
        log.info("进入add方法，req={}", JsonUtil.obj2String(req));
        Integer addRespCode;
        String addRespDesc;
        try {
            AddDefBearerDTO dto = defBearerAddAppService.enhancedAdd(req);
            return Resp.<AddDefBearerDTO>builder()
                    .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                    .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                    .data(dto)
                    .build();
        } catch (DefBearerException de) {
            addRespCode = de.getCode();
            addRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            addRespCode = pe.getResp().getCode();
            addRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("申请默载订单出错，req={}", JsonUtil.obj2String(req), e);
            addRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            addRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<AddDefBearerDTO>builder()
                .code(addRespCode)
                .msg(addRespDesc)
                .build();
    }

    @Override
    public Resp<Void> modify(ModifyDefBearReq req) {
        log.info("进入modify方法，req={}", JsonUtil.obj2String(req));
        Integer modRespCode;
        String modRespDesc;
        try {
            defBearerModifyAppService.modify(req);
            modRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
            modRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        } catch (DefBearerException de) {
            modRespCode = de.getCode();
            modRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            modRespCode = pe.getResp().getCode();
            modRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("修改默载订单出错，req={}", JsonUtil.obj2String(req), e);
            modRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            modRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(modRespCode)
                .msg(modRespDesc)
                .build();
    }

    @Override
    public Resp<Void> terminate(TerminateDefBearerReq req) {
        log.info("进入terminate方法，req={}", JsonUtil.obj2String(req));
        Integer terRespCode;
        String terRespDesc;
        try {
            defBearerTerminateAppService.terminateByOrderId(req, false);
            terRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
            terRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        } catch (DefBearerException de) {
            terRespCode = de.getCode();
            terRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            terRespCode = pe.getResp().getCode();
            terRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("按订单终止默载订单出错，req={}", JsonUtil.obj2String(req), e);
            terRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            terRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(terRespCode)
                .msg(terRespDesc)
                .build();
    }

    @Override
    public Resp<Void> terminateByMsisdn(TerminateDefBearerByMsisdnReq req) {
        log.info("进入terminateByMsisdn方法，req={}", JsonUtil.obj2String(req));
        Integer tmRespCode;
        String tmRespDesc;
        try {
            defBearerTerminateAppService.terminateByMsisdn(req);
            tmRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
            tmRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        } catch (DefBearerException de) {
            tmRespCode = de.getCode();
            tmRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            tmRespCode = pe.getResp().getCode();
            tmRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("按用户终止默载订单出错，req={}", JsonUtil.obj2String(req), e);
            tmRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            tmRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(tmRespCode)
                .msg(tmRespDesc)
                .build();
    }

    @Override
    public Resp<Void> terminateBySystem(TerminateDefBearerReq req) {
        log.info("进入terminateBySystem方法，req={}", JsonUtil.obj2String(req));
        Integer tsRespCode;
        String tsRespDesc;
        try {
            defBearerTerminateAppService.terminateByOrderId(req, true);
            tsRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
            tsRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        } catch (DefBearerException de) {
            tsRespCode = de.getCode();
            tsRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            tsRespCode = pe.getResp().getCode();
            tsRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("系统终止默载订单出错，req={}", JsonUtil.obj2String(req), e);
            tsRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            tsRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(tsRespCode)
                .msg(tsRespDesc)
                .build();
    }

    @Override
    public Resp<Void> resumeOrder(ResumeDefBearerOrderReq request) {
        log.info("进入resumeOrder方法，req={}", JsonUtil.obj2String(request));
        Integer resumeRespCode;
        String resumeRespDesc;
        try {
            Resp<Void> resp = defBearerOrderResumeAppService.resumeOrder(request);
            resumeRespCode = resp.getCode();
            resumeRespDesc = resp.getMsg();
        } catch (DefBearerException de) {
            resumeRespCode = de.getCode();
            resumeRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            resumeRespCode = pe.getResp().getCode();
            resumeRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("恢复默载订单出错，req={}", JsonUtil.obj2String(request), e);
            resumeRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            resumeRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(resumeRespCode)
                .msg(resumeRespDesc)
                .build();
    }

    @Override
    public Resp<Void> resumeOrderByUser(String userId) {
        log.info("进入resumeOrderByUser方法，userId={}", userId);
        Integer resumeUserRespCode;
        String resumeUserRespDesc;
        try {
            defBearerUserResumeAppService.resumeOrderByUser(userId);
            resumeUserRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
            resumeUserRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();
        } catch (DefBearerException de) {
            resumeUserRespCode = de.getCode();
            resumeUserRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            resumeUserRespCode = pe.getResp().getCode();
            resumeUserRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("按用户恢复订单出错，userId={}", userId, e);
            resumeUserRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            resumeUserRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<Void>builder()
                .code(resumeUserRespCode)
                .msg(resumeUserRespDesc)
                .build();
    }

    @Override
    public Resp<List<DefBearerOrderDTO>> queryValidOrders(QueryDefBearerOrdersReq req) {
        log.info("进入queryValidOrders方法，req={}", JsonUtil.obj2String(req));
        Integer queryRespCode;
        String queryRespDesc;
        try {
            List<DefBearerOrderDTO> orders = defBearerQueryAppService.queryValidOrders(req, UserTypeEnum.PEOPLE);
            return Resp.<List<DefBearerOrderDTO>>builder()
                    .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                    .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                    .data(orders)
                    .build();
        } catch (DefBearerException de) {
            queryRespCode = de.getCode();
            queryRespDesc = de.getDesc();
        } catch (ProxyException pe) {
            queryRespCode = pe.getResp().getCode();
            queryRespDesc = pe.getResp().getMsg();
        } catch (Exception e) {
            log.error("查询订单列表出错，req={}", JsonUtil.obj2String(req), e);
            queryRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            queryRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<List<DefBearerOrderDTO>>builder()
                .code(queryRespCode)
                .msg(queryRespDesc)
                .build();
    }

    @Override
    public Resp<List<DefBearerOrderDTO>> queryOrders(QueryDefBearerOrdersReq req) {
        log.info("进入queryOrders方法，req={}", JsonUtil.obj2String(req));
        Integer respCode;
        String respDesc;
        try {
            List<DefBearerOrderDTO> orders = defBearerQueryAppService.queryOrders(req);
            return Resp.<List<DefBearerOrderDTO>>builder()
                    .code(DefBearerRespCodeEnum.R_SUCCESS.getCode())
                    .msg(DefBearerRespCodeEnum.R_SUCCESS.getDesc())
                    .data(orders)
                    .build();
        } catch (DefBearerException defBearerException) {
            respCode = defBearerException.getCode();
            respDesc = defBearerException.getDesc();
        } catch (ProxyException pe) {
            respCode = pe.getResp().getCode();
            respDesc = pe.getResp().getMsg();
        } catch (Exception exception) {
            log.error("查询未归档订单列表出错，req={}", JsonUtil.obj2String(req), exception);
            respCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            respDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc();
        }
        return Resp.<List<DefBearerOrderDTO>>builder()
                .code(respCode)
                .msg(respDesc)
                .build();
    }

}
