package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.BearerModeEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.BaseDefBearerReq;
import com.chinaunicom.qos.defbearer.api.request.TerminateDefBearerByMsisdnReq;
import com.chinaunicom.qos.defbearer.api.request.TerminateDefBearerReq;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerResumeTaskRepo;
import com.chinaunicom.qos.defbearer.infrastructure.producer.KafkaProducer;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.*;
import com.chinaunicom.qos.defbearer.utils.OrderRecordMsgUtil;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.OrderStatus.ORDER_TERMINATED;

/**
 * <AUTHOR>
 * @date 2025-4-22 09:24
 */

@Slf4j
@Service
public class DefBearerTerminateAppService {
    @Resource
    private DefBearerOrderAppService defBearerOrderAppService;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private DefBearerUserResumeAppService userResumeAppService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisLockAppService redisLockAppService;

    @Resource
    private IDefBearerResumeTaskRepo resumeTaskRepo;

    @Resource
    private KafkaProducer kafkaProducer;

    @Resource
    private CmSnapShotProxy snapShotProxy;

    @Resource
    private UserInfoProxy userInfoProxy;

    @Resource
    private DefBearerAdapterProxy defBearerAdapterProxy;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Resource
    private ThreadPoolTaskExecutor resumeTaskExecutor;

    @SuppressWarnings("java:S1126")
    public void terminateByOrderId(TerminateDefBearerReq req, boolean issuedBySystem) {
        Date orderTime = new Date(); //NOSONAR
        var terminateOrderRecordPO = initQosDefbearerOrderRecord(req, orderTime, issuedBySystem);
        Integer respCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
        String respDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();

        //根据手机号获取锁，获取不到则说明该用户已有正在进行中的QoS操作，返回失败
        RedisDistributedRedLock redisDistributedRedLock = new RedisDistributedRedLock(redissonClient, req.getMsisdn(), 60 * 1000L, 500L); //NOSONAR
        boolean isAcquire = redisLockAppService.acquireUserLock(req.getMsisdn(), redisDistributedRedLock, UserTypeEnum.PEOPLE);

        try {
            QosDefbearerOrderPO order = defBearerOrderAppService.getOrderByUserIdOrderId(req.getMsisdn(),
                    req.getOrderId());
            OrderRecordMsgUtil.setOrderRecordPOByQosDefbearerOrderPO(terminateOrderRecordPO, order);

            //系统发起的终止(比如策略冲突场景)，不更新用户期望的结束时间
            //用户发起的终止，需要更新用户期望的结束时间
            terminate(req, order, !issuedBySystem, terminateOrderRecordPO);
        } catch (DefBearerException de) {
            respCode = de.getCode();
            respDesc = de.getDesc();
            if (DefBearerRespCodeEnum.ORDER_ID_INVALID.getCode().equals(respCode)) {
                deleteResumeTask(req.getOrderId(), req.getMsisdn());
            }
            throw de;
        } catch (ProxyException pe) {
            respCode = pe.getResp().getCode();
            respDesc = pe.getResp().getMsg();
            throw pe;
        } catch (Exception e) {
            log.error("默载终止异常，req={}", JsonUtil.obj2String(req), e);
            respCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            respDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        } finally {
            //释放用户锁
            redisLockAppService.releaseUserLock(isAcquire, redisDistributedRedLock);
            //发送调用流水
            terminateOrderRecordPO.setRespCode(respCode);
            terminateOrderRecordPO.setRespDesc(respDesc);
            terminateOrderRecordPO.setReturnTime(new Date());
            //异步发送调用流水
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(terminateOrderRecordPO)),
                    msgSendExecutor);
        }
    }

    public void terminateByMsisdn(TerminateDefBearerByMsisdnReq req) {
        Date orderTime = new Date(); //NOSONAR
        var tmOrderRecordPO = initQosDefbearerOrderRecord(req, orderTime);
        Integer tmRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
        String tmRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();

        //根据手机号获取锁，获取不到则说明该用户已有正在进行中的QoS操作，返回失败
        RedisDistributedRedLock redisLock = new RedisDistributedRedLock(redissonClient, req.getMsisdn(), 60 * 1000L, 500L); //NOSONAR
        boolean lockAcquired = redisLockAppService.acquireUserLock(req.getMsisdn(), redisLock, UserTypeEnum.PEOPLE);

        try {
            QosDefbearerOrderPO order = defBearerOrderAppService.getOrderByUserIdCmServiceId(req.getMsisdn(),
                    req.getCmServiceId());
            if(order != null) {
                OrderRecordMsgUtil.setOrderRecordPOByQosDefbearerOrderPO(tmOrderRecordPO, order);
                log.info("查询到用户订单数据，订单号为{}", order.getOrderId());
                terminate(req, order, true, tmOrderRecordPO);
            }
            else {
                //调用快照服务获取通信服务配置相关数据
                CmsSnapshotDTO cmsSnapshot = snapShotProxy.getCmsConfigInfo(req.getMessageId(), req.getCmServiceId(),null, UserTypeEnum.PEOPLE);
                tmOrderRecordPO.setSpeedType(cmsSnapshot.getSpeedType());
                tmOrderRecordPO.setBearerMode(cmsSnapshot.getBearerMode());

                //调用用户服务获取用户基础信息
                UserInfoDTO userInfo = userInfoProxy.getUserInfo(req.getMessageId(), req.getMsisdn());
                OrderRecordMsgUtil.setUserInfoForOrderRecordPO(tmOrderRecordPO, userInfo);

                //调适配服务删除策略
                String afid = cmsSnapshot.getAfid(userInfo.getSignNetworkType());
                if(StringUtils.isEmpty(afid)) {
                    log.info("未获取到AFID，用户签约的网络类型={}, 通信服务策略信息={}", userInfo.getSignNetworkType(),
                            JsonUtil.obj2String(cmsSnapshot.getPccStrategyList()));
                    throw new DefBearerException(DefBearerRespCodeEnum.AFID_MISSING);
                }
                tmOrderRecordPO.setAfid(afid);

                Resp<OperResultDataDTO> operResp = defBearerAdapterProxy.terminate(req, userInfo.getImsi(), userInfo.getHomeProvince(), afid);
                OrderRecordMsgUtil.setAdapterResultForOrderRecordPO(tmOrderRecordPO, operResp.getData());

                if(!DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(operResp.getCode()) &&
                        !DefBearerAdapterRespCodeEnum.NO_ORDER.getCode().equals(operResp.getCode()) &&
                        !DefBearerAdapterRespCodeEnum.USER_NOT_EXIST.getCode().equals(operResp.getCode())) {
                    throw new ProxyException(operResp);
                }
            }
        } catch (DefBearerException de) {
            tmRespCode = de.getCode();
            tmRespDesc = de.getDesc();
            throw de;
        } catch (ProxyException pe) {
            tmRespCode = pe.getResp().getCode();
            tmRespDesc = pe.getResp().getMsg();
            throw pe;
        } catch (Exception e) {
            log.error("默载按用户终止异常，req={}", JsonUtil.obj2String(req), e);
            tmRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            tmRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        } finally {
            //释放用户锁
            redisLockAppService.releaseUserLock(lockAcquired, redisLock);
            //发送调用流水
            tmOrderRecordPO.setRespCode(tmRespCode);
            tmOrderRecordPO.setRespDesc(tmRespDesc);
            tmOrderRecordPO.setReturnTime(new Date());
            //异步发送调用流水
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(tmOrderRecordPO)),
                    msgSendExecutor);
        }
    }

    private void terminate(BaseDefBearerReq req, QosDefbearerOrderPO order, boolean userEndTimeFlag,
                           QosDefbearerOrderRecordPO terminateOrderRecordPO) {
        Resp<OperResultDataDTO> operResp = defBearerAdapterProxy.terminate(req, order.getImsi(), order.getHomeProvince(), order.getAfid());
        OrderRecordMsgUtil.setAdapterResultForOrderRecordPO(terminateOrderRecordPO, operResp.getData());

        if(DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(operResp.getCode()) ||
                DefBearerAdapterRespCodeEnum.NO_ORDER.getCode().equals(operResp.getCode()) ||
                DefBearerAdapterRespCodeEnum.USER_NOT_EXIST.getCode().equals(operResp.getCode())) {
            Long duration = DateUtils.computeDuration(order.getStartTime(), operResp.getData().getNeOperTime());
            var endTime = operResp.getData().getNeOperTime();
            defBearerOrderAppService.terminateOrder(order.getUserId(), order.getOrderId(), duration, endTime, userEndTimeFlag);
            //发送订单归档消息
            if(userEndTimeFlag) {
                //去掉毫秒
                var userEndTime = DateUtils.removeMillis(endTime);
                order.setUserEndTime(userEndTime);
            }
            order.setDuration(duration);
            order.setEndTime(endTime);
            order.setStatus(ORDER_TERMINATED);
            order.setDelSource(req.getOrderSource());
            //异步发送订单归档消息
            CompletableFuture.runAsync(RunnableWrapper.of(() -> sendOrderArchiveMsg(order)), msgSendExecutor);
            //异步处理恢复任务
            if (BearerModeEnum.AM_PCF.getCode().equals(order.getBearerMode())
                    && !CommonConstants.OperSource.CONFLICT_HANDLER.equals(req.getOrderSource())
                    && !CommonConstants.OperSource.ORDER_REPLACEMENT.equals(req.getOrderSource()) ){
                CompletableFuture.runAsync(RunnableWrapper.of(() -> userResumeAppService.resumeOrderByUser(order.getUserId())), resumeTaskExecutor);
            }
        } else {
            throw new ProxyException(operResp);
        }
    }

    private void sendOrderArchiveMsg(QosDefbearerOrderPO order){
        log.info("开始发送订单归档消息");
        kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_DEFBEARER_ORDER, order);
        log.info("订单归档消息发送完成");
    }

    private void deleteResumeTask(Long originOrderId, String userId) {
        try {
            resumeTaskRepo.deleteResumeTask(originOrderId, userId);
        } catch (Exception e) {
            log.warn("尝试删除恢复任务异常，originOrderId={} userId={}", originOrderId, userId, e);
        }
    }

    private QosDefbearerOrderRecordPO initQosDefbearerOrderRecord(TerminateDefBearerReq req, Date orderTime,
                                                                  boolean issuedBySystem) {
        var terminateOrderRecordPO = new QosDefbearerOrderRecordPO();
        terminateOrderRecordPO.setMessageId(req.getMessageId());
        terminateOrderRecordPO.setOrderSource(req.getOrderSource());
        terminateOrderRecordPO.setUserId(req.getMsisdn());
        terminateOrderRecordPO.setMsisdn(req.getMsisdn());
        terminateOrderRecordPO.setOrderId(req.getOrderId());
        terminateOrderRecordPO.setOrderTime(orderTime);
        terminateOrderRecordPO.setUserType(UserTypeEnum.PEOPLE.getCode());
        terminateOrderRecordPO.setOperType(issuedBySystem ? DefOperTypeEnum.TERMINATE_BY_SYSTEM.getCode() :
                DefOperTypeEnum.TERMINATE.getCode());
        return terminateOrderRecordPO;
    }

    private QosDefbearerOrderRecordPO initQosDefbearerOrderRecord(TerminateDefBearerByMsisdnReq req, Date orderTime) {
        var terminateOrderRecordPO = new QosDefbearerOrderRecordPO();
        terminateOrderRecordPO.setMessageId(req.getMessageId());
        terminateOrderRecordPO.setOrderSource(req.getOrderSource());
        terminateOrderRecordPO.setUserId(req.getMsisdn());
        terminateOrderRecordPO.setMsisdn(req.getMsisdn());
        terminateOrderRecordPO.setCmServiceId(req.getCmServiceId());
        terminateOrderRecordPO.setOrderTime(orderTime);
        terminateOrderRecordPO.setUserType(UserTypeEnum.PEOPLE.getCode());
        terminateOrderRecordPO.setOperType(DefOperTypeEnum.TERMINATE_BY_USER.getCode());
        return terminateOrderRecordPO;
    }

}
