package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.SignNetworkTypeEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.adapter.api.dto.IotDefBearerAdapterDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerBaseTerminateReq;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerTerminateByImsiReq;
import com.chinaunicom.qos.defbearer.api.request.IotDefBearerTerminateByOrderReq;
import com.chinaunicom.qos.defbearer.common.constant.CommonConstants;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerOrderRepo;
import com.chinaunicom.qos.defbearer.infrastructure.producer.KafkaProducer;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.CmSnapShotProxy;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.IotDefBearerAdapterProxy;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants.*;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.OrderStatus.ORDER_TERMINATED;

/**
 * 物网默载终止业务处理
 *
 * <AUTHOR>
 * @date 2025/2/8 11:15
 */
@Slf4j
@Service
public class IotDefBearerTerminateAppService {

    @Resource
    private IDefBearerOrderRepo defBearerOrderRepo;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisLockAppService redisLockAppService;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Resource
    private IotDefBearerAdapterProxy iotDefBearerAdapterProxy;

    @Resource
    private KafkaProducer kafkaProducer;

    @Resource
    private CmSnapShotProxy cmSnapShotProxy;

    @Resource
    private IotDefBearerRecordAppService iotDefBearerRecordAppService;

    //定义物网终止南向成功响应码集合
    private static final List<Integer> IOT_TERMINATE_SUCCESS_RESP_CODE_LIST = List.of(DefBearerAdapterRespCodeEnum.SUCCESS.getCode(), DefBearerAdapterRespCodeEnum.IOT_USER_NOT_EXIST.getCode());

    public void terminateByOrder(IotDefBearerTerminateByOrderReq req, boolean isSystemTerminate) {
        var orderTime = new Date();
        //查询订单数据是否存在，若不存在则抛出异常
        var qosDefbearerOrderPO = checkAndGetOrder(req);
        //初始化赋值orderRecordPO对象
        var orderRecordPO = initQosDefbearerOrderRecordPO(req, orderTime, isSystemTerminate ? DefOperTypeEnum.TERMINATE_BY_SYSTEM.getCode() : DefOperTypeEnum.TERMINATE.getCode());
        //根据imsi加锁
        var lock = new RedisDistributedRedLock(redissonClient, req.getImsi(), 60 * 1000L, 10 * 500L);
        var isAcquire = redisLockAppService.acquireUserLock(req.getImsi(), lock, UserTypeEnum.IOT);
        try {
            //复制订单信息到orderRecordPO对象
            setOrderRecordPOByQosDefbearerOrderPO(orderRecordPO, qosDefbearerOrderPO);
            terminateOrder(qosDefbearerOrderPO, orderRecordPO, isSystemTerminate);
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.R_SUCCESS.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.R_SUCCESS.getDesc());
        } catch (ProxyException pe) {
            orderRecordPO.setRespCode(pe.getResp().getCode());
            orderRecordPO.setRespDesc(pe.getResp().getMsg());
            throw pe;
        } catch (DefBearerException de) {
            orderRecordPO.setRespCode(de.getCode());
            orderRecordPO.setRespDesc(de.getDesc());
            throw de;
        }  catch (Exception e) {
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc() + e.getMessage());
            throw e;
        } finally {
            releaseLockAndSendRecord(isAcquire, lock, orderRecordPO);
        }
    }

    public void terminateByImsi(IotDefBearerTerminateByImsiReq req) {
        var orderTime = new Date();
        //初始化赋值orderRecordPO对象
        var orderRecordPO = initQosDefbearerOrderRecordPO(req, orderTime, DefOperTypeEnum.TERMINATE_BY_USER.getCode());
        //根据imsi加锁
        var lock = new RedisDistributedRedLock(redissonClient, req.getImsi(), 60 * 1000L, 10 * 500L);
        var isAcquire = redisLockAppService.acquireUserLock(req.getImsi(), lock, UserTypeEnum.IOT);
        try {
            var qosDefbearerOrderPO = getOrderByUserIdCmServiceId(req.getImsi(), req.getCmServiceId());
            if (qosDefbearerOrderPO != null) {
                log.info("查询到用户订单数据，订单号为[{}]", qosDefbearerOrderPO.getOrderId());
                //复制订单信息到orderRecordPO对象
                setOrderRecordPOByQosDefbearerOrderPO(orderRecordPO, qosDefbearerOrderPO);
                terminateOrder(qosDefbearerOrderPO, orderRecordPO, false);
            } else {
                orderRecordPO.setCmServiceId(req.getCmServiceId());
                //调用快照服务获取通信服务配置相关数据
                CmsSnapshotDTO cmsSnapshot = cmSnapShotProxy.getCmsConfigInfo(req.getMessageId(), req.getCmServiceId(), null, UserTypeEnum.IOT);
                //获取AFID、SpeedType赋值orderRecordPO
                iotDefBearerRecordAppService.setAfidAndSpeedType(cmsSnapshot, orderRecordPO);
                //发起物网终止请求
                terminateAndProcessResult(orderRecordPO);
            }
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.R_SUCCESS.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.R_SUCCESS.getDesc());
        } catch (DefBearerException de) {
            orderRecordPO.setRespCode(de.getCode());
            orderRecordPO.setRespDesc(de.getDesc());
            throw de;
        } catch (ProxyException pe) {
            orderRecordPO.setRespCode(pe.getResp().getCode());
            orderRecordPO.setRespDesc(pe.getResp().getMsg());
            throw pe;
        } catch (Exception e) {
            orderRecordPO.setRespCode(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getCode());
            orderRecordPO.setRespDesc(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION.getDesc() + e.getMessage());
            throw e;
        } finally {
            releaseLockAndSendRecord(isAcquire, lock, orderRecordPO);
        }
    }

    private void releaseLockAndSendRecord(boolean isAcquire, RedisDistributedRedLock lock, QosDefbearerOrderRecordPO orderRecordPO) {
        redisLockAppService.releaseUserLock(isAcquire, lock);
        //异步发送调用流水
        orderRecordPO.setReturnTime(new Date());
        CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(orderRecordPO)), msgSendExecutor);
    }

    private void terminateOrder(QosDefbearerOrderPO qosDefbearerOrderPO, QosDefbearerOrderRecordPO orderRecordPO, boolean isSystemTerminate) {
        //发起物网终止请求
        terminateAndProcessResult(orderRecordPO);
        //更新订单并归档
        updateOrderAndArchive(qosDefbearerOrderPO, orderRecordPO, isSystemTerminate);
    }

    private void terminateAndProcessResult(QosDefbearerOrderRecordPO orderRecordPO) {
        Resp<IotDefBearerAdapterDTO> iotDefBearerAdapterResp = iotDefBearerAdapterProxy.terminate(orderRecordPO);
        //处理返回结果
        if (iotDefBearerAdapterResp.getData() != null) {
            var iotDefBearerAdapterDTO = iotDefBearerAdapterResp.getData();
            orderRecordPO.setOperTime(DateUtils.toDate(iotDefBearerAdapterDTO.getNeReqTime()));
            orderRecordPO.setRespTime(DateUtils.toDate(iotDefBearerAdapterDTO.getNeRespTime()));
        }
        //如果返回码不在成功列表内，则抛出异常
        if (!IOT_TERMINATE_SUCCESS_RESP_CODE_LIST.contains(iotDefBearerAdapterResp.getCode())) {
            throw new ProxyException(iotDefBearerAdapterResp);
        }
    }

    private void updateOrderAndArchive(QosDefbearerOrderPO qosDefbearerOrderPO, QosDefbearerOrderRecordPO orderRecordPO, boolean isSystemTerminate) {
        //计算默载最终时长
        var endTime = DateUtils.removeMillis(orderRecordPO.getOperTime());
        var duration = DateUtils.computeDuration(qosDefbearerOrderPO.getStartTime(), endTime);
        //更新订单状态
        Map<String, Object> updateOrderMap = new HashMap<>();
        updateOrderMap.put(USER_ID, qosDefbearerOrderPO.getUserId());
        updateOrderMap.put(ORDER_ID, qosDefbearerOrderPO.getOrderId());
        updateOrderMap.put(DURATION, duration);
        if (!isSystemTerminate) {
            updateOrderMap.put(USER_END_TIME, endTime);
            qosDefbearerOrderPO.setUserEndTime(endTime);
        }
        updateOrderMap.put(END_TIME, endTime);
        updateOrderMap.put(STATUS, ORDER_TERMINATED);
        //更新订单状态、时长以及终止时间
        defBearerOrderRepo.updateOrder(updateOrderMap);
        //发送订单归档消息
        qosDefbearerOrderPO.setDuration(duration);
        qosDefbearerOrderPO.setEndTime(endTime);
        qosDefbearerOrderPO.setStatus(ORDER_TERMINATED);
        qosDefbearerOrderPO.setDelSource(orderRecordPO.getOrderSource());
        //异步发送订单归档消息
        CompletableFuture.runAsync(RunnableWrapper.of(() -> kafkaProducer.send(CommonConstants.KafkaTopics.TOPIC_DEFBEARER_ORDER, qosDefbearerOrderPO)), msgSendExecutor);
    }

    private @NotNull QosDefbearerOrderRecordPO initQosDefbearerOrderRecordPO(IotDefBearerBaseTerminateReq req, Date orderTime,
                                                                             Integer operType) {
        var orderRecordPO = new QosDefbearerOrderRecordPO();
        orderRecordPO.setOrderTime(orderTime);
        orderRecordPO.setMessageId(req.getMessageId());
        orderRecordPO.setImsi(req.getImsi());
        orderRecordPO.setOrderSource(req.getOrderSource());
        orderRecordPO.setOperType(operType);
        orderRecordPO.setUserId(req.getImsi());
        orderRecordPO.setUserType(UserTypeEnum.IOT.getCode());
        orderRecordPO.setSignNetworkType(SignNetworkTypeEnum.FIVE_GEN_PACKAGE.getCode());
        return orderRecordPO;
    }

    private void setOrderRecordPOByQosDefbearerOrderPO(QosDefbearerOrderRecordPO orderRecordPO, QosDefbearerOrderPO qosDefbearerOrderPO) {
        orderRecordPO.setSpeedType(qosDefbearerOrderPO.getSpeedType());
        orderRecordPO.setQosProductId(qosDefbearerOrderPO.getQosProductId());
        orderRecordPO.setCmServiceId(qosDefbearerOrderPO.getCmServiceId());
        orderRecordPO.setMsisdn(qosDefbearerOrderPO.getMsisdn());
        orderRecordPO.setImsi(qosDefbearerOrderPO.getImsi());
        orderRecordPO.setHomeProvince(qosDefbearerOrderPO.getHomeProvince());
        orderRecordPO.setSignNetworkType(qosDefbearerOrderPO.getSignNetworkType());
        orderRecordPO.setOrderId(qosDefbearerOrderPO.getOrderId());
        orderRecordPO.setAfid(qosDefbearerOrderPO.getAfid());
    }

    private @NotNull QosDefbearerOrderPO checkAndGetOrder(IotDefBearerTerminateByOrderReq req) {
        var qosDefbearerOrder = defBearerOrderRepo.getOrderByUserIdOrderId(req.getImsi(), req.getOrderId());
        if (qosDefbearerOrder == null) {
            throw new DefBearerException(DefBearerRespCodeEnum.IOT_ORDER_ID_INVALID);
        }
        return qosDefbearerOrder;
    }

    private QosDefbearerOrderPO getOrderByUserIdCmServiceId(String userId, String cmServiceId) {
        QosDefbearerOrderPO order = null;
        try {
            order = defBearerOrderRepo.getOrderByUserIdCmServiceId(userId, cmServiceId);
        } catch (Exception e) {
            log.error("获取用户订单数据异常，userId={}, cmServiceId={}", userId, cmServiceId, e);
        }
        return order;
    }
}
