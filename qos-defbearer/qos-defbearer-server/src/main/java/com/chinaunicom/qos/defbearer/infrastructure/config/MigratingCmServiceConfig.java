package com.chinaunicom.qos.defbearer.infrastructure.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-3 19:30
 */
@Component
@NacosConfigurationProperties(prefix = "defbearer", dataId = "migrating-cm-service", type = ConfigType.YAML,
        autoRefreshed = true)
@Data
public class MigratingCmServiceConfig {
    /**
     * 处于业务迁移期的通信服务列表
     */
    private List<String> migratingCmServiceIds = Collections.emptyList();

    public List<String> getMigratingCmServiceIds() {
        return migratingCmServiceIds == null ? Collections.emptyList() : migratingCmServiceIds;
    }
}
