package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.idgen.api.enums.FlowTypeEnum;
import com.chinaunicom.qos.idgen.api.enums.IdGenRespCodeEnum;
import com.chinaunicom.qos.idgen.api.enums.IdTypeEnum;
import com.chinaunicom.qos.idgen.api.enums.OrderTypeEnum;
import com.chinaunicom.qos.idgen.api.request.IdGenReqV2;
import com.chinaunicom.qos.idgen.api.service.IdGenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IdGenProxy {
    @DubboReference(timeout = 1000)
    private IdGenService idGenService;

    public Long genOrderId(OrderTypeEnum orderType, String userId, UserTypeEnum userType) {
        var idGenReq = new IdGenReqV2();
        idGenReq.setIdType(IdTypeEnum.ORDER.name());
        idGenReq.setOrderType(orderType.name());
        idGenReq.setUserId(userId);
        Long orderId;
        Resp<Long> idGenResp;
        try {
            log.info("开始调用ID服务获取订单号，req={}", idGenReq);
            idGenResp = idGenService.nextIdV2(idGenReq);
            log.info("调用ID服务获取订单号结束，resp={}", idGenResp);
        } catch (Exception e) {
            throw new DefBearerException(userType==UserTypeEnum.IOT? DefBearerRespCodeEnum.IOT_ID_CALL_EXCEPTION:DefBearerRespCodeEnum.ID_CALL_EXCEPTION);
        }
        if(IdGenRespCodeEnum.R_SUCCESS.getCode().equals(idGenResp.getCode())) {
            orderId = idGenResp.getData();
        }
        else {
            throw new ProxyException(idGenResp);
        }
        return orderId;
    }

    public Long genFlowId(FlowTypeEnum flowType){
        var idGenReq = new IdGenReqV2();
        idGenReq.setIdType(IdTypeEnum.FLOW.name());
        idGenReq.setFlowType(flowType.name());
        Long flowId = null;
        try {
            log.info("开始调用ID服务获取流水号，req={}", idGenReq);
            Resp<Long> idGenResp = idGenService.nextIdV2(idGenReq);
            log.info("调用ID服务获取流水号结束，resp={}", idGenResp);
            if(IdGenRespCodeEnum.R_SUCCESS.getCode().equals(idGenResp.getCode())) {
                flowId = idGenResp.getData();
            }
            else {
                log.error("创建流水ID失败！req={}, idGenResp={}", idGenReq, idGenResp);
            }
        } catch (Exception e) {
            log.error("创建流水ID失败！req={}", idGenReq, e);
        }
        return flowId;
    }
}
