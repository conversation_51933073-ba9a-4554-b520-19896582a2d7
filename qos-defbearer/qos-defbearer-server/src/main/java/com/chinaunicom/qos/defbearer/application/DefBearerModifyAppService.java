package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.common.utils.RedisDistributedRedLock;
import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.adapter.api.enums.DefBearerAdapterRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.ModifyDefBearReq;
import com.chinaunicom.qos.common.enums.DefOperTypeEnum;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderRecordPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.exception.ProxyException;
import com.chinaunicom.qos.defbearer.infrastructure.proxy.*;
import com.chinaunicom.qos.defbearer.utils.OrderRecordMsgUtil;
import com.chinaunicom.qos.defbearer.utils.ValidateUtil;
import com.chinaunicom.qos.snapshot.api.dto.CmsSnapshotDTO;
import com.chinaunicom.qos.user.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.CompletableFuture;


/**
 * <AUTHOR>
 * @date 2025-4-22 09:24
 */

@Slf4j
@Service
public class DefBearerModifyAppService {

    @Resource
    private DefBearerOrderAppService defBearerOrderAppService;

    @Resource
    private OrderRecordAppService orderRecordAppService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private CmSnapShotProxy snapShotProxy;

    @Resource
    private UserInfoProxy userInfoProxy;

    @Resource
    private UserProductProxy userProductProxy;

    @Resource
    private DefBearerAdapterProxy defBearerAdapterProxy;

    @Resource
    private ThreadPoolTaskExecutor msgSendExecutor;

    @Resource
    private RedisLockAppService redisLockAppService;

    public void modify(ModifyDefBearReq req) {
        Date orderTime = new Date(); //NOSONAR
        var modifyOrderRecordPO = initQosDefbearerOrderRecord(req, orderTime);
        Integer modRespCode = DefBearerRespCodeEnum.R_SUCCESS.getCode();
        String modRespDesc = DefBearerRespCodeEnum.R_SUCCESS.getDesc();

        QosDefbearerOrderPO order = defBearerOrderAppService.getOrderByUserIdOrderId(req.getMsisdn(), req.getOrderId());
        OrderRecordMsgUtil.setOrderRecordPOByQosDefbearerOrderPO(modifyOrderRecordPO, order);
        //订单状态校验
        if(!order.getUserEndTime().after(orderTime)) {
            log.info("订单期望结束时间{}早于调用时间{}，不允许修改", DateUtils.formatDate(order.getUserEndTime()), orderTime);
            throw new DefBearerException(DefBearerRespCodeEnum.ORDER_ID_INVALID);
        }

        //根据手机号获取锁，获取不到则说明该用户已有正在进行中的QoS操作，返回失败
        RedisDistributedRedLock rLock = new RedisDistributedRedLock(redissonClient, req.getMsisdn(), 60 * 1000L, 500L); //NOSONAR
        boolean isAcquired = redisLockAppService.acquireUserLock(req.getMsisdn(), rLock, UserTypeEnum.PEOPLE);

        try {
            //调用快照服务获取通信服务配置相关数据
            CmsSnapshotDTO cmsSnapshot = snapShotProxy.getCmsConfigInfo(req.getMessageId(), order.getCmServiceId(), order.getSnapshotVersion(), UserTypeEnum.PEOPLE);

            //调用用户服务获取用户基础信息
            UserInfoDTO userInfo = userInfoProxy.getUserInfo(req.getMessageId(), req.getMsisdn());
            modifyOrderRecordPO.setSignNetworkType(userInfo.getSignNetworkType());
            modifyOrderRecordPO.setVisitProvince(userInfo.getVisitProvince());
            //调用用户产品订购服务获取用户订购信息
            SubscribeInfoDTO subscribeInfo = userProductProxy.getUserProductSubscribeInfo(req.getMsisdn(), order.getQosProductId(), UserTypeEnum.PEOPLE);

            //计算时长
            var startTime = DateUtils.removeMillis(new Date());
            Date userEndTime = DateUtils.parseDate(req.getUserEndTime(), DateUtils.DATE_TIME_FORMATTER_14); //NOSONAR
            Long duration = DateUtils.computeDuration(startTime, userEndTime);
            log.info("根据startTime:{}，userEndTime:{}，计算出时长为{}", DateUtils.formatDate(startTime),
                    DateUtils.formatDate(userEndTime), duration);
            modifyOrderRecordPO.setStartTime(startTime);
            modifyOrderRecordPO.setDuration(duration);


            //数据有效性校验
            ValidateUtil.validateRequestData(DefOperTypeEnum.MODIFY, cmsSnapshot, userInfo, subscribeInfo, duration,
                    userEndTime);

            //调用默载适配服务修改默载
            Resp<OperResultDataDTO> operResp = defBearerAdapterProxy.modify(req, userEndTime, userInfo, order.getAfid());
            OrderRecordMsgUtil.setAdapterResultForOrderRecordPO(modifyOrderRecordPO, operResp.getData());

            if(DefBearerAdapterRespCodeEnum.SUCCESS.getCode().equals(operResp.getCode())) {
                //默载修改成功，提交延时任务并保存订单
                //更新订单的时长和用户期望结束时间
                defBearerOrderAppService.updateOrder(userEndTime, duration, order);
            } else {
                throw new ProxyException(operResp);
            }
        } catch (DefBearerException de) {
            modRespCode = de.getCode();
            modRespDesc = de.getDesc();
            throw de;
        } catch (ProxyException pe) {
            modRespCode = pe.getResp().getCode();
            modRespDesc = pe.getResp().getMsg();
            throw pe;
        } catch (Exception e) {
            log.error("默载修改异常，req={}", JsonUtil.obj2String(req), e);
            modRespCode = DefBearerRespCodeEnum.OTHER_EXCEPTION.getCode();
            modRespDesc = DefBearerRespCodeEnum.OTHER_EXCEPTION.getDesc() + e.getMessage();
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        } finally {
            //释放用户锁
            redisLockAppService.releaseUserLock(isAcquired, rLock);
            //发送调用流水
            modifyOrderRecordPO.setRespCode(modRespCode);
            modifyOrderRecordPO.setRespDesc(modRespDesc);
            modifyOrderRecordPO.setReturnTime(new Date());
            //异步发送调用流水
            CompletableFuture.runAsync(RunnableWrapper.of(() -> orderRecordAppService.sendOrderRecordMsg(modifyOrderRecordPO)),
                    msgSendExecutor);
        }
    }


    private QosDefbearerOrderRecordPO initQosDefbearerOrderRecord(ModifyDefBearReq req, Date orderTime) {
        var modifyOrderRecordPO = new QosDefbearerOrderRecordPO();
        modifyOrderRecordPO.setMessageId(req.getMessageId());
        modifyOrderRecordPO.setOrderSource(req.getOrderSource());
        modifyOrderRecordPO.setOrderId(req.getOrderId());
        modifyOrderRecordPO.setUserId(req.getMsisdn());
        modifyOrderRecordPO.setMsisdn(req.getMsisdn());
        modifyOrderRecordPO.setOrderTime(orderTime);
        modifyOrderRecordPO.setUserEndTime(req.getUserEndTime());
        modifyOrderRecordPO.setUserType(UserTypeEnum.PEOPLE.getCode());
        modifyOrderRecordPO.setOperType(DefOperTypeEnum.MODIFY.getCode());
        return modifyOrderRecordPO;
    }
}
