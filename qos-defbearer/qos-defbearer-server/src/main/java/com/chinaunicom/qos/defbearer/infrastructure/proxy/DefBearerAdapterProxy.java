package com.chinaunicom.qos.defbearer.infrastructure.proxy;

import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.AddDefBearerReq;
import com.chinaunicom.qos.defbearer.api.request.BaseDefBearerReq;
import com.chinaunicom.qos.defbearer.api.request.ModifyDefBearReq;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.defbearer.adapter.api.dto.OperResultDataDTO;
import com.chinaunicom.qos.defbearer.adapter.api.request.DefBearerOperAddReq;
import com.chinaunicom.qos.defbearer.adapter.api.request.DefBearerOperModReq;
import com.chinaunicom.qos.defbearer.adapter.api.request.DefBearerOperTerminateReq;
import com.chinaunicom.qos.defbearer.adapter.api.service.DefBearerOperService;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.user.api.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.chinaunicom.qos.defbearer.application.constant.DefBearerConstants.NET_ADDITIONAL_SECONDS;
import static com.chinaunicom.qos.defbearer.common.constant.CommonConstants.NE_ID_PEOPLE;

@Service
@Slf4j
public class DefBearerAdapterProxy {
    @DubboReference(timeout = 25000, retries = 0)
    private DefBearerOperService defBearerOperService;

    public Resp<OperResultDataDTO> add(String messageId, Date userEndTime, UserInfoDTO userInfo, String afid) {
        var addReq = new DefBearerOperAddReq();
        addReq.setMessageId(messageId);
        addReq.setMsisdn(userInfo.getMsisdn());
        addReq.setImsi(userInfo.getImsi());
        addReq.setHomeProvince(userInfo.getHomeProvince());
        addReq.setAfid(afid);
        //下发网络侧的结束时间增加2分钟，避免网络侧到期删除早于默载到时删除的时间
        addReq.setUserEndTime(DateUtils.computeDate(userEndTime, NET_ADDITIONAL_SECONDS));
        addReq.setConfigNeId(NE_ID_PEOPLE);
        Resp<OperResultDataDTO> addResp;
        log.info("开始调用默载适配服务申请接口，req={}", JsonUtil.obj2String(addReq));
        try {
            addResp = defBearerOperService.add(addReq);
        } catch (Exception e) {
            log.error("调用默载适配服务申请接口异常！req={}", JsonUtil.obj2String(addReq), e);
            throw new DefBearerException(DefBearerRespCodeEnum.ADAPTER_CALL_EXCEPTION);
        }
        log.info("调用默载适配服务申请接口，返回为{}", JsonUtil.obj2String(addResp));
        return addResp;
    }

    public Resp<OperResultDataDTO> modify(ModifyDefBearReq req, Date userEndTime, UserInfoDTO userInfo, String afid) {
        var modReq = new DefBearerOperModReq();
        modReq.setMessageId(req.getMessageId());
        modReq.setMsisdn(req.getMsisdn());
        modReq.setImsi(userInfo.getImsi());
        modReq.setHomeProvince(userInfo.getHomeProvince());
        modReq.setAfid(afid);
        modReq.setUserEndTime(userEndTime);
        modReq.setNeId(NE_ID_PEOPLE);
        Resp<OperResultDataDTO> modResp;
        log.info("开始调用默载适配服务修改接口，req={}", JsonUtil.obj2String(modReq));
        try {
            modResp = defBearerOperService.mod(modReq);
        } catch (Exception e) {
            log.error("调用默载适配服务修改接口异常！req={}", JsonUtil.obj2String(modReq), e);
            throw new DefBearerException(DefBearerRespCodeEnum.ADAPTER_CALL_EXCEPTION);
        }
        log.info("调用默载适配服务修改接口，返回为{}", JsonUtil.obj2String(modResp));
        return modResp;
    }

    public Resp<OperResultDataDTO> terminate(BaseDefBearerReq req, String imsi, Integer homeProvince, String afid) {
        var terminateReq = new DefBearerOperTerminateReq();
        terminateReq.setMessageId(req.getMessageId());
        terminateReq.setMsisdn(req.getMsisdn());
        terminateReq.setImsi(imsi);
        terminateReq.setHomeProvince(homeProvince);
        terminateReq.setAfid(afid);
        terminateReq.setNeId(NE_ID_PEOPLE);
        Resp<OperResultDataDTO> terminateResp;
        log.info("开始调用默载适配服务终止接口，req={}", JsonUtil.obj2String(terminateReq));
        try {
            terminateResp = defBearerOperService.terminate(terminateReq);
        } catch (Exception e) {
            log.error("调用默载适配服务终止接口异常！req={}", JsonUtil.obj2String(terminateReq), e);
            throw new DefBearerException(DefBearerRespCodeEnum.ADAPTER_CALL_EXCEPTION);
        }
        log.info("调用默载适配服务终止接口，返回为{}", JsonUtil.obj2String(terminateResp));
        return terminateResp;
    }

}
