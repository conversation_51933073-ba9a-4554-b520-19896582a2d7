package com.chinaunicom.qos.defbearer.application;

import com.chinaunicom.qos.common.enums.BearerModeEnum;
import com.chinaunicom.qos.common.enums.UserTypeEnum;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.defbearer.api.dto.DefBearerOrderDTO;
import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.defbearer.api.request.QueryDefBearerOrdersReq;
import com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO;
import com.chinaunicom.qos.defbearer.exception.DefBearerException;
import com.chinaunicom.qos.defbearer.infrastructure.dal.IDefBearerOrderRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DefBearerQueryAppService {
    @Resource
    private IDefBearerOrderRepo defBearerOrderRepo;


    public List<DefBearerOrderDTO> queryValidOrders(QueryDefBearerOrdersReq req, UserTypeEnum userType) {
        List<DefBearerOrderDTO> orders = new ArrayList<>();
        List<QosDefbearerOrderPO> result;
        try {
            result = defBearerOrderRepo.getValidOrders(userType == UserTypeEnum.IOT ? req.getImsi() : req.getMsisdn(), req.getCmServiceIdList(), req.getQosProductId());
        }
        catch (Exception e) {
            log.error("查询数据库异常！", e);
            switch (userType) {
                case IOT:
                    throw new DefBearerException(DefBearerRespCodeEnum.IOT_OTHER_EXCEPTION);
                case PEOPLE:
                default:
                    throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
            }
        }
        if(result == null || result.isEmpty()) {
            switch (userType) {
                case IOT:
                    throw new DefBearerException(DefBearerRespCodeEnum.IOT_NO_ORDER_DATA);
                case PEOPLE:
                default:
                    throw new DefBearerException(DefBearerRespCodeEnum.NO_ORDER_DATA);
            }
        }
        for(QosDefbearerOrderPO item : result) {
            orders.add(defBearerOrderPO2DTO(item));
        }
        return orders;

    }

    public List<DefBearerOrderDTO> queryOrders(QueryDefBearerOrdersReq req) {
        List<DefBearerOrderDTO> orders = new ArrayList<>();
        List<QosDefbearerOrderPO> result;
        try {
            result = defBearerOrderRepo.getOrders(req.getMsisdn(), req.getCmServiceIdList(), req.getQosProductId());
        }
        catch (Exception e) {
            log.error("查询数据库异常！", e);
            throw new DefBearerException(DefBearerRespCodeEnum.OTHER_EXCEPTION);
        }
        if(result == null || result.isEmpty()) {
            throw new DefBearerException(DefBearerRespCodeEnum.NO_ORDER_DATA);
        }
        for(QosDefbearerOrderPO item : result) {
            orders.add(defBearerOrderPO2DTO(item));
        }
        return orders;

    }

    private DefBearerOrderDTO defBearerOrderPO2DTO(QosDefbearerOrderPO qosDefbearerOrderPO) {
        var dto = new DefBearerOrderDTO();
        dto.setOrderId(qosDefbearerOrderPO.getOrderId());
        dto.setMsisdn(qosDefbearerOrderPO.getMsisdn());
        dto.setQosProductId(qosDefbearerOrderPO.getQosProductId());
        dto.setCmServiceId(qosDefbearerOrderPO.getCmServiceId());
        dto.setStartTime(DateUtils.formatDate(qosDefbearerOrderPO.getStartTime()));
        dto.setUserEndTime(DateUtils.formatDate(qosDefbearerOrderPO.getUserEndTime()));
        if(qosDefbearerOrderPO.getEndTime() != null){
            dto.setEndTime(DateUtils.formatDate(qosDefbearerOrderPO.getEndTime()));
        }
        dto.setDuration(qosDefbearerOrderPO.getDuration());
        dto.setImsi(qosDefbearerOrderPO.getImsi());
        dto.setHomeProvince(qosDefbearerOrderPO.getHomeProvince());
        dto.setSignNetworkType(qosDefbearerOrderPO.getSignNetworkType());
        dto.setAfid(qosDefbearerOrderPO.getAfid());
        dto.setBearerMode(qosDefbearerOrderPO.getBearerMode()==null? BearerModeEnum.DEF.getCode() :
                qosDefbearerOrderPO.getBearerMode());
        dto.setSpeedType(qosDefbearerOrderPO.getSpeedType());
        dto.setNeId(qosDefbearerOrderPO.getNeId());
        dto.setStatus(qosDefbearerOrderPO.getStatus());
        return dto;
    }
}
