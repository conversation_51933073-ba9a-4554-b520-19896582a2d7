package com.chinaunicom.qos.defbearer.starter.filter;

import com.chinaunicom.qos.defbearer.api.enums.DefBearerRespCodeEnum;
import com.chinaunicom.qos.common.response.Resp;
import org.apache.dubbo.rpc.*;
import javax.validation.ConstraintViolationException;

public class ConstraintViolationExceptionFilter implements Filter {

    private static final String IOT_DEF_BEARER_SERVICE = "IotDefBearerService";

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        var result = invoker.invoke(invocation);
        if(result.hasException() && result.getException() instanceof ConstraintViolationException) {
            Resp<Void> resp = Resp.<Void>builder().build();
            resp.setCode(invocation.getTargetServiceUniqueName().contains(IOT_DEF_BEARER_SERVICE)?DefBearerRespCodeEnum.IOT_PARAM_INVALID.getCode():DefBearerRespCodeEnum.PARAM_INVALID.getCode());
            var msg = new StringBuilder();
            msg.append(invocation.getTargetServiceUniqueName().contains(IOT_DEF_BEARER_SERVICE)?DefBearerRespCodeEnum.IOT_PARAM_INVALID.getDesc():DefBearerRespCodeEnum.PARAM_INVALID.getDesc()).append(": ");
            ((ConstraintViolationException) result.getException()).getConstraintViolations().forEach(item -> msg.append(item.getMessageTemplate()).append(","));
            msg.deleteCharAt(msg.length() - 1);
            resp.setMsg(msg.toString());
            result.setValue(resp);
            result.setException(null);
        }
        return result;
    }
}
