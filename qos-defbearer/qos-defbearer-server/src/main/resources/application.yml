server:
  port: 37102

dubbo:
  application:
    name: qos-defbearer-server
    logger: slf4j
    qos-enable: false
    check-serializable: false
    serialize-check-status: DISABLE
  protocol:
    name: dubbo
    port: -1
    threads: ${DUBBO_THREADS:200}
    queues: ${DUBBO_QUEUES:1000}
  provider:
    validation: true
  metrics:
    protocol: prometheus

spring:
  profiles:
    active: @profile.env@
  application:
    name: qos-defbearer-server
  datasource:
    qos:
      url: ${mysql.qos.url}
      driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
      username: ${mysql.qos.username}
      password: ${mysql.qos.password}
      maximum-pool-size: ${QOS_DB_MAX_POOL_SIZE:20}
      minimum-idle: ${QOS_DB_MIN_POOL_SIZE:10}
      keepalive-time: 60000
      pool-name: QosHikariPool
    pcc:
      url: ${mysql.pcc.url}
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: ${mysql.pcc.username}
      password: ${mysql.pcc.password}
      maximum-pool-size: ${PCC_DB_MAX_POOL_SIZE:20}
      minimum-idle: ${PCC_DB_MIN_POOL_SIZE:10}
      keepalive-time: 60000
      pool-name: PccHikariPool
  kafka:
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        connections.max.idle.ms: -1
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: SCRAM-SHA-256
        jaas:
          config: org.apache.kafka.common.security.scram.ScramLoginModule required username="${kafka.username}" password="${kafka.password}";


qosredisson:
  enable: true

mybatis-plus:
  mapper-locations: classpath:mapper/qos/*.xml, classpath:mapper/pcc/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

task:
  delay:
    enable: false #是否开启延时任务执行器
    group: defbearer #dubbo的group,此配置需要与延时任务配置表delay_task_info.executor_app_name值一致
    add:
      enable: true #是否开启延时任务新增消费方法注册

qoshttp:
  enable: true
  configs:
    primaryRestTemplate:
      max-idle: 50
      keep-alive-duration: 1
      protocol: http
      connect-timeout: 10
      read-timeout: 25
      write-timeout: 25


def:
  old-def-bearer-terminate-url: ${old-url}/api/pcc/45gpeopdefbear/urgentdelete


management:
  endpoints:
    web:
      exposure:
        include: health,prometheus,loggers
  metrics:
    tags:
      application: ${dubbo.application.name}
    distribution:
      percentiles:
        http.client.requests: 0.5, 0.9, 0.95, 0.99
      percentiles-histogram:
        http.client.requests: true



