mock-local:
  enable: true

dubbo:
  registry:
    address: nacos://*************:38848
    username: ${nacos.username}
    password: ${nacos.password}
  consumer:
    check: false
    mock: false
nacos:
  username: '{qoscipher}43cdacf67640feeefc13cbeca08691ab0f090fe6a690e59339a19c7cc63ddf3f'
  password: '{qoscipher}f29bfcfb2a884e50dc1665ddf50f6d2977cfa8124c8d21a64f949436e839d88b'
  config:
    server-addr: *************:38848


qosredisson:
  nodeAddresses:
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
  password: '{qoscipher}444f65095897f9fa8562f8edfe6820a0f332f0a3e3f436913cd4f97c3f0a3474'


spring:
  kafka:
    bootstrap-servers: *************:32059,*************:32059,*************:32059


kafka:
  username: '{qoscipher}dd85e10ae7931804172fc6c4b19d4485426db2f4a42ac5b22c8b9a56055618c8'
  password: '{qoscipher}079ad8786395decbbbccc7cf93a9d9a67555eeb69f48fce1855e2ad78d988538689f6df3008a78c6d28d1aa72c3dba12'


mysql:
  qos:
    url: jdbc:shardingsphere:classpath:sharding-dev.yaml?placeholder-type=system_props
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}d2c64796ec04e3738baeef14aa4517330a2f2988df01840109fe98e091231a9f4451c17eac9ac6b57c4a34f56597119f'
  pcc:
    url: *****************************************************************************************************************************************************************************
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}f778d7430ccd5d904a41221fa9dd988fdac3e91598109aa13dd4d58d7451fa29270670ccec38618011cbdfa6137b7ae4'

old-url: http://**************:31301