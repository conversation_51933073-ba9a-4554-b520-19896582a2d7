mock-local:
  enable: true

dubbo:
  registry:
    address: nacos://nacos-server-headless.slice-qos:8848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}
  consumer:
    check: false
dubbo-nacos:
  username: '{qoscipher}43cdacf67640feeefc13cbeca08691ab0f090fe6a690e59339a19c7cc63ddf3f'
  password: '{qoscipher}f29bfcfb2a884e50dc1665ddf50f6d2977cfa8124c8d21a64f949436e839d88b'

nacos:
  config:
    server-addr: nacos-server-headless.slice-qos:8848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}


qosredisson:
  nodeAddresses:
    - redis://**************:32516
    - redis://**************:32518
    - redis://**************:32518
    - redis://**************:32517
    - redis://**************:32515
    - redis://**************:32517
  password: '{qoscipher}444f65095897f9fa8562f8edfe6820a0f332f0a3e3f436913cd4f97c3f0a3474'


spring:
  kafka:
    bootstrap-servers: *************:32017,*************:32015,*************:32014


kafka:
  username: '{qoscipher}a01629d1abb0f5e7c28a92d81d8032aa5f50c836cf45dc782f7e5fb3500fbfae'
  password: '{qoscipher}1014b52d76ed8415fe817c843153e5bff3d249b0ba81841df4113b4cf3f17d5c'


mysql:
  qos:
    url: jdbc:shardingsphere:classpath:sharding-test.yaml?placeholder-type=system_props
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}0b441e82588a86469f10badc0b9ec98d8456a857f9417ed8823a901d550df2a5fffe89932b0582d1df3020d5beb9f841'
  pcc:
    url: *****************************************************************************************************************************************************************************
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}4a9c72a9a9f85bb30a5dc600f42eb02d0b2d40083235bb3ed4b63e689acf0b17f5685354ced1136a18df1d7555f350d2'

old-url: http://pcc-service-45gpeopdefbear.slice:31300
