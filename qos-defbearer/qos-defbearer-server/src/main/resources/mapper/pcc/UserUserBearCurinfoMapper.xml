<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.pcc.UserUserBearCurinfoMapper">
    <resultMap id="userBearCurinfo" type="com.chinaunicom.qos.defbearer.infrastructure.dal.po.UserUserBearCurinfoPO">
        <result column="cm_service_id" property="cmServiceId"/>
        <result column="correlation_id" property="correlationId"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="prov_home" property="homeProvince"/>
        <result column="network_type" property="userType"/>
        <result column="net_type" property="signNetworkType"/>
        <result column="bear_type" property="bearerMode"/>
        <result column="speed_type" property="speedType"/>
        <result column="bear_source" property="neId"/>
    </resultMap>
    <select id="queryValidOrderByMsisdn" resultMap="userBearCurinfo">
        SELECT
        cm_service_id,
        correlation_id,
        msisdn,
        imsi,
        prov_home,
        network_type,
        net_type,
        bear_type,
        speed_type,
        bear_source
        FROM
        user_user_bear_curinfo
        WHERE
        msisdn = #{msisdn}
        AND
        status = 0
        AND
        end_time > current_timestamp
    </select>

    <select id="queryOrderByMsisdnAndCmServiceId" resultMap="userBearCurinfo">
        SELECT
        cm_service_id,
        correlation_id,
        msisdn,
        imsi
        FROM
        user_user_bear_curinfo
        WHERE
        msisdn = #{msisdn}
        and cm_service_id = #{cmServiceId}
        and status = 0
    </select>
</mapper>
