<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos.QosDefbearerResumeTaskMapper">
    <resultMap id="resumeTask" type="com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefBearerResumeTaskPO">
        <result column="origin_order_id" property="originOrderId"/>
        <result column="user_id" property="userId"/>
        <result column="qos_product_id" property="qosProductId"/>
        <result column="cm_service_id" property="cmServiceId"/>
        <result column="resume_time" property="resumeTime"/>
        <result column="user_end_time" property="userEndTime"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="home_province" property="homeProvince"/>
        <result column="visit_province" property="visitProvince"/>
        <result column="user_type" property="userType"/>
        <result column="sign_network_type" property="signNetworkType"/>
        <result column="afid" property="afid"/>
        <result column="bearer_mode" property="bearerMode"/>
        <result column="speed_type" property="speedType"/>
        <result column="snapshot_version" property="snapshotVersion"/>
        <result column="next_execute_time" property="nextExecuteTime"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="execute_fail_retry_count" property="executeFailRetryCount"/>
        <result column="execute_code" property="executeCode"/>
        <result column="execute_desc" property="executeDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <delete id="deleteByOrderIdUserId">
        DELETE FROM
            qos_defbearer_resume_task
        WHERE
            origin_order_id = #{originOrderId}
        AND
            user_id = #{userId}
    </delete>

    <select id="queryByUserId" resultMap="resumeTask">
        SELECT
            origin_order_id,
            user_id,
            qos_product_id,
            cm_service_id,
            resume_time,
            user_end_time,
            msisdn,
            imsi,
            home_province,
            visit_province,
            user_type,
            sign_network_type,
            afid,
            bearer_mode,
            speed_type,
            snapshot_version,
            next_execute_time
        FROM
            qos_defbearer_resume_task
        WHERE
            user_id = #{userId}
    </select>

    <update id="updateTaskExecutionParams">
        UPDATE qos_defbearer_resume_task
        <set>
            <if test="task.nextExecuteTime != null">
                next_execute_time = #{task.nextExecuteTime},
            </if>
            <if test="task.executeStatus != null">
                execute_status = #{task.executeStatus},
            </if>
            <if test="task.executeFailRetryCount != null">
                execute_fail_retry_count = #{task.executeFailRetryCount},
            </if>
            <if test="task.executeCode != null">
                execute_code = #{task.executeCode},
            </if>
            <if test="task.executeDesc != null and task.executeDesc != ''">
                execute_desc = #{task.executeDesc}
            </if>
        </set>
        WHERE
            origin_order_id = #{task.originOrderId}
        AND
            user_id = #{task.userId}
    </update>
</mapper>
