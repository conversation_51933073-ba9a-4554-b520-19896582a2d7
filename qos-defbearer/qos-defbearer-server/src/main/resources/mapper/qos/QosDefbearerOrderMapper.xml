<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.defbearer.infrastructure.dal.mapper.qos.QosDefbearerOrderMapper">
    <resultMap id="defbearerOrder" type="com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO">
        <result column="order_id" property="orderId"/>
        <result column="user_id" property="userId"/>
        <result column="qos_product_id" property="qosProductId"/>
        <result column="cm_service_id" property="cmServiceId"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="user_end_time" property="userEndTime"/>
        <result column="duration" property="duration"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="home_province" property="homeProvince"/>
        <result column="visit_province" property="visitProvince"/>
        <result column="user_type" property="userType"/>
        <result column="sign_network_type" property="signNetworkType"/>
        <result column="afid" property="afid"/>
        <result column="bearer_mode" property="bearerMode" />
        <result column="speed_type" property="speedType"/>
        <result column="ne_id" property="neId"/>
        <result column="delay_task_flag" property="delayTaskFlag"/>
        <result column="snapshot_version" property="snapshotVersion"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="queryValidOrderByUserIdCmServiceIds" resultMap="defbearerOrder">
        SELECT
        order_id,
        user_id,
        qos_product_id,
        cm_service_id,
        status,
        start_time,
        end_time,
        user_end_time,
        duration,
        msisdn,
        imsi,
        home_province,
        visit_province,
        user_type,
        sign_network_type,
        afid,
        bearer_mode,
        speed_type,
        ne_id,
        delay_task_flag,
        snapshot_version,
        create_time,
        update_time
        FROM
            qos_defbearer_order
        WHERE
            user_id = #{userId}
        AND
            status = 0
        AND
            user_end_time >= current_timestamp
        <if test="cmServiceIds != null and cmServiceIds.size() != 0">
            AND
            cm_service_id IN
            <foreach collection="cmServiceIds" open="(" close=")" item="cmServiceId" separator=",">
                #{cmServiceId}
            </foreach>
        </if>
        <if test="qosProductId != null">
            AND
            qos_product_id = #{qosProductId}
        </if>
    </select>

    <select id="queryOrderByUserIdCmServiceIds" resultMap="defbearerOrder">
        SELECT
        order_id,
        user_id,
        qos_product_id,
        cm_service_id,
        status,
        start_time,
        end_time,
        user_end_time,
        duration,
        msisdn,
        imsi,
        home_province,
        visit_province,
        user_type,
        sign_network_type,
        afid,
        bearer_mode,
        speed_type,
        ne_id,
        delay_task_flag,
        snapshot_version,
        create_time,
        update_time
        FROM
        qos_defbearer_order
        WHERE
        user_id = #{userId}
        <if test="cmServiceIds != null and cmServiceIds.size() != 0">
            AND
            cm_service_id IN
            <foreach collection="cmServiceIds" open="(" close=")" item="cmServiceId" separator=",">
                #{cmServiceId}
            </foreach>
        </if>
        <if test="qosProductId != null">
            AND
            qos_product_id = #{qosProductId}
        </if>
    </select>

    <select id="queryOrderByUserIdCmServiceId" resultMap="defbearerOrder">
        SELECT
        order_id,
        user_id,
        qos_product_id,
        cm_service_id,
        status,
        start_time,
        end_time,
        user_end_time,
        duration,
        msisdn,
        imsi,
        home_province,
        visit_province,
        user_type,
        sign_network_type,
        afid,
        bearer_mode,
        speed_type,
        ne_id,
        delay_task_flag,
        snapshot_version,
        create_time,
        update_time
        FROM
        qos_defbearer_order
        WHERE
        user_id = #{userId}
        AND
        cm_service_id = #{cmServiceId}
        AND
        status = 0
        ORDER BY user_end_time DESC LIMIT 1
    </select>

    <select id="queryOrderByUserIdOrderId" resultMap="defbearerOrder">
        SELECT
        order_id,
        user_id,
        qos_product_id,
        cm_service_id,
        status,
        start_time,
        end_time,
        user_end_time,
        duration,
        msisdn,
        imsi,
        home_province,
        visit_province,
        user_type,
        sign_network_type,
        afid,
        bearer_mode,
        speed_type,
        ne_id,
        delay_task_flag,
        snapshot_version,
        create_time,
        update_time
        FROM
        qos_defbearer_order
        WHERE
        user_id = #{userId}
        AND
        order_id = #{orderId}
        AND
        status = 0
    </select>

    <select id="queryValidOrderByUserId" resultMap="defbearerOrder">
        SELECT
        order_id,
        user_id,
        qos_product_id,
        cm_service_id,
        status,
        start_time,
        end_time,
        user_end_time,
        duration,
        msisdn,
        imsi,
        home_province,
        visit_province,
        user_type,
        sign_network_type,
        afid,
        bearer_mode,
        speed_type,
        ne_id,
        delay_task_flag,
        snapshot_version,
        create_time,
        update_time
        FROM
        qos_defbearer_order
        WHERE
        user_id = #{userId}
        AND
        status = 0
        AND
        user_end_time > current_timestamp
    </select>

    <insert id="insertOrder" parameterType="com.chinaunicom.qos.defbearer.common.infrastructure.dal.po.QosDefbearerOrderPO">
        INSERT INTO qos_defbearer_order (
            order_id,
            user_id,
            qos_product_id,
            cm_service_id,
            status,
            start_time,
            user_end_time,
            duration,
            msisdn,
            imsi,
            home_province,
            visit_province,
            user_type,
            sign_network_type,
            afid,
            bearer_mode,
            speed_type,
            ne_id,
            delay_task_flag,
            snapshot_version
        )
        VALUES
            (#{orderId},#{userId},#{qosProductId},#{cmServiceId},#{status},#{startTime},
            #{userEndTime},#{duration},#{msisdn},#{imsi},#{homeProvince},#{visitProvince},#{userType},#{signNetworkType},
            #{afid},#{bearerMode},#{speedType},#{neId},#{delayTaskFlag},#{snapshotVersion})
    </insert>

    <update id="updateOrder">
        UPDATE qos_defbearer_order
        <set>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="userEndTime != null">
                user_end_time = #{userEndTime},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="delayTaskFlag != null">
                delay_task_flag = #{delayTaskFlag},
            </if>
        </set>
        WHERE
            user_id = #{userId}
        AND
            order_id = #{orderId}
    </update>
</mapper>
