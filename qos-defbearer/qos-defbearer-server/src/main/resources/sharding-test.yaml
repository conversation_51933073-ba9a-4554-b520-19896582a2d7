databaseName: sharding

mode:
  type: Standalone
  repository:
    type: JDBC

dataSources:
  qos:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************
    username: $${mysql.qos.username::}
    password: $${mysql.qos.password::}


rules:
- !SHARDING
  tables: # 数据分片规则配置
    qos_defbearer_order:
      actualDataNodes: qos.qos_defbearer_order_${0..15}
      tableStrategy:
        standard:
          shardingColumn: user_id
          shardingAlgorithmName: qos_defbearer_order_inline
    qos_defbearer_resume_task:
      actualDataNodes: qos.qos_defbearer_resume_task
  shardingAlgorithms:
    qos_defbearer_order_inline:
      type: INLINE
      props:
        algorithm-expression: qos_defbearer_order_${Long.valueOf(user_id) % 16}

props:
  sql-show: false