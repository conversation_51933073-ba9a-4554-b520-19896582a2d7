dubbo:
  registry:
    address: nacos://nacos-server-headless.slice-qos:8848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}
  consumer:
    check: false
dubbo-nacos:
  username: '{qoscipher}43cdacf67640feeefc13cbeca08691ab0f090fe6a690e59339a19c7cc63ddf3f'
  password: '{qoscipher}4b309d5f9ba81338a0e764ad97fd489b7cd4b4a32e717ec1f8f4a3fdb50feac1'

nacos:
  config:
    server-addr: nacos-server-headless.slice-qos:8848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}



qosredisson:
  nodeAddresses:
    - redis://**************:32514
    - redis://**************:32536
    - redis://**************:32512
    - redis://**************:32514
    - redis://**************:32579
    - redis://**************:32580
  password: '{qoscipher}ff313dd061f41faea318f79152b6545cffdb2d676ac9bd33765a42fb4cc441b1'


spring:
  kafka:
    bootstrap-servers: *************:32043,*************:32068,*************:32051


kafka:
  username: '{qoscipher}157d899e8789c512ca9049e22c60843f8e34a5ad76101e37e0a73f9121bf8689'
  password: '{qoscipher}5abea5a7eb3663af173cad0ca96d992a5f1c40b112c5abed182c5dc5cdf58dce'


mysql:
  qos:
    url: jdbc:shardingsphere:classpath:sharding-prod.yaml?placeholder-type=system_props
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}ce73f3d5662361c246ce3ae3854f8e284fce15449a413a808519297c8e09c4de3833d266a8419bc103d135858cff9853'
  pcc:
    url: jdbc:mysql://************:3306/user?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}4037b251d3ae794296a5e5dd616d5c47738caa919e05efbc9212f60cc4ff12a0c1578b27cb5264fedc117b8c7120a9f9'

old-url: http://pcc-service-45gpeopdefbear.slice:31300