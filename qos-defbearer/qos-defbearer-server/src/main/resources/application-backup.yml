dubbo:
  registry:
    address: nacos://nacos-server-headless.slice-qos:38848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}
  consumer:
    check: false

dubbo-nacos:
  username: '{qoscipher}43cdacf67640feeefc13cbeca08691ab0f090fe6a690e59339a19c7cc63ddf3f'
  password: '{qoscipher}8613b4aa23d3598e760b430c8073b10b7bf1461b91b74de46e2604b45bf7e102'

nacos:
  config:
    server-addr: nacos-server-headless.slice-qos:38848
    username: ${dubbo-nacos.username}
    password: ${dubbo-nacos.password}



qosredisson:
  nodeAddresses:
    - redis://*************:6379
    - redis://*************:6379
    - redis://*************:6379
    - redis://*************:6379
    - redis://************:6379
    - redis://*************:6379
  password: '{qoscipher}02e5140e0e7a5e8f2be7f12e1ae2d5aa10ae1bc43b74760af096bcbde8f26c73'


spring:
  kafka:
    bootstrap-servers: **************:9092,**************:9092,**************:9092
    properties:
      sasl:
        mechanism: PLAIN


kafka:
  username: slice-qos
  password: '{qoscipher}b3df726cd82e3b4642637c0570c3e38b8f168440ddddff21a782669da127ffa1'


mysql:
  qos:
    url: jdbc:shardingsphere:classpath:sharding-backup.yaml?placeholder-type=system_props
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}463412bba8e6755a2d6f10ec699df78d34713becf6cf49a53d43ba0d3e3270bb3f9f1b884a482c552ea8eb0663c5dac1'
  pcc:
    url: ***************************************************************************************************************************************************************************
    username: '{qoscipher}828eea50b5a46c723a80d6bc7003aaa872db1b16cf1a7067e8be54f521e05352'
    password: '{qoscipher}4037b251d3ae794296a5e5dd616d5c47738caa919e05efbc9212f60cc4ff12a0c1578b27cb5264fedc117b8c7120a9f9'

old-url: http://************:31300