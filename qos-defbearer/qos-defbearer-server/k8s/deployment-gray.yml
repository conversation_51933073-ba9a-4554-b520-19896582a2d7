kind: Service
apiVersion: v1
metadata:
  name: qos-defbearer-server
  namespace: slice-qos
  labels:
    app: qos-defbearer-server
spec:
  ports:
    - name: http
      protocol: TCP
      port: 37102
      targetPort: 37102
  selector:
    app: qos-defbearer-server
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qos-defbearer-server-gray
  namespace: slice-qos
  labels:
    app: qos-defbearer-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qos-defbearer-server
  template:
    metadata:
      labels:
        app: qos-defbearer-server
    spec:
      volumes:
        - name: sw-agent
          emptyDir: {}
        - name: log-volume
          emptyDir: { }
        - name: config-volume
          configMap:
            name: filebeat-config
        - name: data
          emptyDir: {}
      initContainers:
        - name: sw-agent-sidecar
          image: >-
            zhongyuan-registry.cucloud.cn/qos/skywalking-agent-sidecar:9.2.0
          command:
            - sh
          args:
            - '-c'
            - >-
              mkdir -p /skywalking/agent && cp -r /usr/skywalking/agent/*
              /skywalking/agent
          resources: { }
          volumeMounts:
            - name: sw-agent
              mountPath: /skywalking/agent
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      containers:
        - name: qos-defbearer-server
          # noinspection KubernetesUnknownValues
          image: "zhongyuan-registry.cucloud.cn/qos/qos-defbearer-server:#{Build.BuildNumber}#"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 37102
          readinessProbe:
            httpGet: # 指定探测方式
              path: /actuator/prometheus
              port: 37102            # 【注意】需要变更为服务自己开放的监控端口
            initialDelaySeconds: 40  # 容器启动后多久开始探测，【注意】需要根据服务自身启动耗时确定
            timeoutSeconds: 2        # 表示容器必须在2s内做出相应反馈给probe，否则视为探测失败
            periodSeconds: 10        # 探测周期，每10s探测一次
            successThreshold: 1      # 连续探测1次成功表示成功
            failureThreshold: 5      # 连续探测5次失败表示失败
          env:
            - name: JAVA_OPTS
              value: >-
                -javaagent:/usr/skywalking/agent/skywalking-agent.jar
                -Dskywalking.agent.service_name=qos-defbearer-server-gray
                -Dskywalking.collector.backend_service=************:21800       
                -Dskywalking.plugin.jdbc.trace_sql_parameters=true 
                -Dskywalking.plugin.dubbo.collect_consumer_arguments=true
                -Dskywalking.plugin.dubbo.consumer_arguments_length_threshold=1024	
                -Dskywalking.trace.ignore_path=*/actuator/**
                -Dskywalking.licence=QPJPCCNLKFPT
                -Dskywalking.agentid=$HOSTNAME
                -server -Xms4096M -Xmx4096M -XX:+UseG1GC -Djava.awt.headless=true
            - name: DUBBO_THREADS
              value: '200'
            - name: DUBBO_QUEUES
              value: '1000'
            - name: QOS_DB_MAX_POOL_SIZE
              value: '20'
            - name: QOS_DB_MIN_POOL_SIZE
              value: '10'
            - name: PCC_DB_MAX_POOL_SIZE
              value: '20'
            - name: PCC_DB_MIN_POOL_SIZE
              value: '10'
          volumeMounts:
            - name: sw-agent
              mountPath: /usr/skywalking/agent
            - name: log-volume
              mountPath: /logs
          resources:
            limits:
              cpu: '10'
              memory: 8Gi
            requests:
              cpu: '4'
              memory: 5Gi
        - name: filebeat
          image: zhongyuan-registry.cucloud.cn/qos/filebeat:7.10.2-new
          args: [ "-c", "/opt/filebeat/filebeat.yml", "-e" ]
          env:
            - name: SERVICE_NAME
              value: "qos-defbearer-server"
          volumeMounts:
            - name: log-volume
              mountPath: /logs
            - name: config-volume
              mountPath: /opt/filebeat/
            - name: data
              mountPath: /usr/share/filebeat/data
          resources:
            limits:
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 200Mi
      imagePullSecrets:
        - name: docker-secret
      restartPolicy: Always
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: team
                    operator: In
                    values:
                      - slice-qos
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 80
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - qos-defbearer-server
                topologyKey: kubernetes.io/hostname
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 30%
      maxSurge: 30%
  minReadySeconds: 10
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600