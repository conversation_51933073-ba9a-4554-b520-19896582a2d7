CREATE TABLE defbearer.qos_defbearer_order_0
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    bearer_mode       tinyint     DEFAULT 1 COMMENT '承载类型，1-默载 2-AMPCF',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表0';

CREATE TABLE defbearer.qos_defbearer_order_1
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表1';


CREATE TABLE defbearer.qos_defbearer_order_2
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表2';


CREATE TABLE defbearer.qos_defbearer_order_3
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表3';

CREATE TABLE defbearer.qos_defbearer_order_4
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表4';

CREATE TABLE defbearer.qos_defbearer_order_5
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表5';

CREATE TABLE defbearer.qos_defbearer_order_6
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表6';

CREATE TABLE defbearer.qos_defbearer_order_7
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表7';

CREATE TABLE defbearer.qos_defbearer_order_8
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表8';

CREATE TABLE defbearer.qos_defbearer_order_9
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表9';

CREATE TABLE defbearer.qos_defbearer_order_10
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表10';

CREATE TABLE defbearer.qos_defbearer_order_11
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表11';

CREATE TABLE defbearer.qos_defbearer_order_12
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表12';

CREATE TABLE defbearer.qos_defbearer_order_13
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表13';

CREATE TABLE defbearer.qos_defbearer_order_14
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表14';

CREATE TABLE defbearer.qos_defbearer_order_15
(
    order_id          bigint      NOT NULL COMMENT '订单ID',
    user_id           varchar(20) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
    qos_product_id    int(11) DEFAULT NULL COMMENT 'qos产品id',
    cm_service_id     varchar(50) NOT NULL COMMENT '通信服务id',
    status            tinyint     NOT NULL COMMENT '订单状态 0 已生效 -1 已终止',
    start_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单生效时间',
    end_time          datetime             DEFAULT NULL COMMENT '实际订单终止时间',
    user_end_time     datetime             DEFAULT NULL COMMENT '用户期望订单终止时间',
    duration          bigint      NOT NULL COMMENT '订单时长(单位秒)',
    msisdn            varchar(20)          DEFAULT NULL COMMENT '手机号码',
    imsi              varchar(20)          DEFAULT NULL COMMENT 'IMSI',
    home_province     int(11) NOT NULL COMMENT '归属省',
    user_type         tinyint     NOT NULL COMMENT '用户类型 0人网 1物网',
    sign_network_type tinyint     NOT NULL COMMENT '用户签约的网络类型 0-4G 1-5G',
    afid              varchar(64)          DEFAULT NULL COMMENT 'PCC网络策略ID',
    speed_type        tinyint     NOT NULL COMMENT '加限速类型 0-加速 1-限速 2-白名单限速 3-黑名单限速',
    ne_id             varchar(24) NOT NULL COMMENT '网元ID',
    delay_task_flag   tinyint     NOT NULL COMMENT '是否提交延时任务 -1 未提交 0 已提交',
    snapshot_version  varchar(80) NOT NULL COMMENT '通信服务快照版本号',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (order_id) USING BTREE,
    KEY               index_user (user_id) USING BTREE,
    KEY               index_time (user_end_time,delay_task_flag) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='默载订单表15';