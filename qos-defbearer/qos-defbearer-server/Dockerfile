FROM zhongyuan-registry.cucloud.cn/qos/openjdk:21-jdk-ubuntu

MAINTAINER yuxing <<EMAIL>>

# Remember to change the port according to the RPC protocol you select
EXPOSE 37102

# copy the JAR file into the root and rename
ADD qos-defbearer/qos-defbearer-server/target/qos-defbearer-server-1.0.0-SNAPSHOT.jar qos-defbearer-server.jar

# Run java with the jar file when the container starts up
ENTRYPOINT exec java $JAVA_OPTS -jar qos-defbearer-server.jar