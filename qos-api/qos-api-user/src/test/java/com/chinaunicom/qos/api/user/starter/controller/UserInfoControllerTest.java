package com.chinaunicom.qos.api.user.starter.controller;

import com.chinaunicom.qos.api.user.starter.controller.request.TakeNumReq;
import com.chinaunicom.qos.user.api.service.UserInfoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UserInfoControllerTest {
    @InjectMocks
    private UserInfoController userInfoController;
    @Mock
    private UserInfoService userInfoService;

    @Test
    public void takeNum(){
        var takeNumReq = new TakeNumReq();
        userInfoController.takeNum(takeNumReq);
        takeNumReq.setPrivateIp("FF:FF:FF:FF");
        userInfoController.takeNum(takeNumReq);
    }
}
