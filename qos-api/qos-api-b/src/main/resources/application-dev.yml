spring:
  datasource:
    batchmysql:
      url: ***********************************************************************************************************************
      username: slice_dynamic
      password: '{qoscipher}ea253c81485e8271e2229693bd029f859a0b23beb78d79fd360fae2c58d243d63d3442f6b1d2835bef76f8e531468d35'
    mysql:
      url: *************************************************************************************************************************
      username: slice_dynamic
      password: '{qoscipher}ea253c81485e8271e2229693bd029f859a0b23beb78d79fd360fae2c58d243d63d3442f6b1d2835bef76f8e531468d35'
    qos:
      url: ************************************************************************************************************************************************************************
      username: slice_dynamic
      password: '{qoscipher}a35dc127af732809ce1866916ecce72a1aadc9dedf0501bba8743649e5758810bf22e4ed2b00635942f5a639cbff7885'
    clickhouse:
      nodes:
        - name: node1
          url: *******************************************************************
        - name: node2
          url: *******************************************************************
        - name: node3
          url: *******************************************************************
        - name: node1bak
          url: *******************************************************************
        - name: node2bak
          url: *******************************************************************
        - name: node3bak
          url: *******************************************************************
      username: pcc
      password: '{qoscipher}a90eb3d61a8c411854697bc7f5788a0148caa93d0e42b771f5e2ffc213dd14c3'

xxl:
  job:
    admin:
      addresses:
    accessToken: '{qoscipher}e5e90cbd1baec4e4f65f799ee193407b1ee2a24adb27a903fe8599300917f00584bd72ededb262c5f140fde3fda63636'

dubbo:
  application:
    name: qos-api-b
    qos-port: 22223
  registry:
    address: nacos://**************:38848
    username: ${nacos.username}
    password: ${nacos.password}
    parameters:
      namespace: 2038004f-af38-4fad-977d-59f35ff5415e
  protocol:
    name: dubbo
    port: -1
  consumer:
    check: false

nacos:
  username: slice-qos
  password: '{qoscipher}a0fa42ec7450a3ca5465e4a8c79902f895d13afddae9a2940801e5f89c0c36be'
  config:
    server-addr: **************:38848

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com:
      chinaunicom:
        qos:
          api:
            common:
              infrastructure:
                dal:
                  mapper:
                    ck: DEBUG
    org:
      springframework:
        transaction=DEBUG:

redis:
  nodes: **************:7001,**************:7001,**************:7001,**************:7001,**************:7001,**************:7001
  password: '{qoscipher}13402739b2406fb2459d6d282cd84ab4a95c4da647fd4580c378b4d7719cada2'

qosredisson:
  nodeAddresses:
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
  password: '{qoscipher}e2090e9e570725330d50c203d73b234e7434764e44c2000d114419bc1b50a802'

ftp:
  host: **************
  useport: 3222
  user: slice-qos
  password: '{qoscipher}7862f8fb9140314f220f85af8d63b0fb0df79b63550ab01d3bd45b231a33a4f2'
  path: update/netcloud/