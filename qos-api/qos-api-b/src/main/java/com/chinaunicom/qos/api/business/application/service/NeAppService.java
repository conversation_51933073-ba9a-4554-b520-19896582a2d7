package com.chinaunicom.qos.api.business.application.service;

import com.chinaunicom.qos.api.business.exception.ApiBServiceException;
import com.chinaunicom.qos.api.business.starter.dto.NeInfoDTO;
import com.chinaunicom.qos.api.business.starter.dto.NeNameDTO;
import com.chinaunicom.qos.api.business.starter.reuqest.AddNeReq;
import com.chinaunicom.qos.api.business.starter.reuqest.ListNeReq;
import com.chinaunicom.qos.api.business.starter.reuqest.ModifyNeReq;
import com.chinaunicom.qos.api.common.application.enums.ApiResponseCodeEnum;
import com.chinaunicom.qos.api.common.infrastructure.dal.mapper.mysql.NeInfoMapper;
import com.chinaunicom.qos.api.common.infrastructure.dal.po.NeInfoPO;
import com.chinaunicom.qos.common.enums.NodeTypeEnum;
import com.chinaunicom.qos.common.enums.StatusEnum;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.DateUtils;
import com.chinaunicom.qos.common.utils.JsonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-3-5 16:14
 */

@Service
@Slf4j
public class NeAppService {

    @Resource
    private NeInfoMapper neInfoMapper;

    public Resp<Void> addNe(AddNeReq req) {

        if (checkUnique(req.getNeId())) {
            log.error("新增网元请求，网元ID已存在，req = {}", JsonUtil.obj2String(req));
            throw new ApiBServiceException(ApiResponseCodeEnum.R_PARAM_ERROR.getCode(),
                    ApiResponseCodeEnum.R_PARAM_ERROR.getMessage()+": 网元ID已存在");
        }

        var neInfo = toNeInfoPO(req);
        neInfoMapper.insert(neInfo);
        log.info("neInfo插入成功");

        return Resp.<Void>builder()
                .code(ApiResponseCodeEnum.R_SUCCESS.getCode())
                .msg(ApiResponseCodeEnum.R_SUCCESS.getMessage())
                .build();
    }

    boolean checkUnique(String neId) {
        Integer count = neInfoMapper.selectCount(neId);
        return count != null && count > 0;
    }

    public Resp<Void> modifyNe(ModifyNeReq req) {

        if (StringUtils.isBlank(req.getInterfaceUrl()) && Objects.isNull(req.getStatus()) && Objects.isNull(req.getOperationStatus())) {
            log.info("无需更新网元信息，req = {}", JsonUtil.obj2String(req));
        }
        else {
            var neInfo = new NeInfoPO();
            neInfo.setNeId(req.getNeId());
            neInfo.setInterfaceUrl(req.getInterfaceUrl());
            neInfo.setStatus(req.getStatus());
            neInfo.setOperationStatus(req.getOperationStatus());

            if (neInfoMapper.update(neInfo) <= 0) {
                log.error("Modify请求，网元ID不存在，req = {}", JsonUtil.obj2String(req));
                throw new ApiBServiceException(ApiResponseCodeEnum.R_PARAM_ERROR.getCode(),
                        ApiResponseCodeEnum.R_PARAM_ERROR.getMessage()+": 网元ID不存在");
            }
        }

        return Resp.<Void>builder()
                .code(ApiResponseCodeEnum.R_SUCCESS.getCode())
                .msg(ApiResponseCodeEnum.R_SUCCESS.getMessage())
                .build();
    }

    public Resp<List<NeInfoDTO>> listNeInfo(ListNeReq req) {
        List<NeInfoDTO> neList = new ArrayList<>();

        var neInfoPOs = neInfoMapper.list(req.getNeIds(), req.getNeType(), req.getNodeType());
        if (Objects.nonNull(neInfoPOs) && !neInfoPOs.isEmpty()) {
            for (NeInfoPO neInfoPO : neInfoPOs) {
                var neInfoDTO = new NeInfoDTO();
                neInfoDTO.setNeId(neInfoPO.getNeId());
                neInfoDTO.setNeName(neInfoPO.getNeName());
                neInfoDTO.setRegion(neInfoPO.getRegion());
                neInfoDTO.setInterfaceUrl(neInfoPO.getInterfaceUrl());
                neInfoDTO.setOperationStatus(neInfoPO.getOperationStatus());
                neInfoDTO.setStatus(neInfoPO.getStatus());
                neInfoDTO.setCreateTime(DateUtils.formatDate(neInfoPO.getCreateTime()));
                neInfoDTO.setUpdateTime(DateUtils.formatDate(neInfoPO.getUpdateTime()));
                neList.add(neInfoDTO);
            }
        }

        return Resp.<List<NeInfoDTO>>builder()
                .code(ApiResponseCodeEnum.R_SUCCESS.getCode())
                .msg(ApiResponseCodeEnum.R_SUCCESS.getMessage())
                .data(neList)
                .build();
    }

    public Resp<List<NeNameDTO>> listNeNames(ListNeReq req) {
        List<NeNameDTO> neNameDTOs = new ArrayList<>();

        var neInfoPOs = neInfoMapper.list(req.getNeIds(), req.getNeType(), req.getNodeType());
        if (Objects.nonNull(neInfoPOs) && !neInfoPOs.isEmpty()) {
            for (NeInfoPO neInfoPO : neInfoPOs) {
                var neNameDTO = new NeNameDTO();
                neNameDTO.setNeId(neInfoPO.getNeId());
                neNameDTO.setNeName(neInfoPO.getNeName());
                neNameDTOs.add(neNameDTO);
            }
        }

        return Resp.<List<NeNameDTO>>builder()
                .code(ApiResponseCodeEnum.R_SUCCESS.getCode())
                .msg(ApiResponseCodeEnum.R_SUCCESS.getMessage())
                .data(neNameDTOs)
                .build();
    }

    NeInfoPO toNeInfoPO(AddNeReq req) {
        var neInfoPO = new NeInfoPO();

        neInfoPO.setNeId(req.getNeId());
        neInfoPO.setNeName(req.getNeName());
        neInfoPO.setNeType(req.getNeType());
        neInfoPO.setInterfaceUrl(req.getInterfaceUrl());
        neInfoPO.setRegion(req.getRegion());

        if (Objects.isNull(req.getStatus())) {
            neInfoPO.setStatus(StatusEnum.ACTIVE.getStatus());
        } else {
            neInfoPO.setStatus(req.getStatus());
        }

        if(Objects.isNull(req.getOperationStatus())) {
            neInfoPO.setOperationStatus(StatusEnum.ACTIVE.getStatus());
        } else {
            neInfoPO.setOperationStatus(req.getOperationStatus());
        }

        neInfoPO.setNodeType(NodeTypeEnum.REAL_NODE.getType());

        return neInfoPO;
    }
}
