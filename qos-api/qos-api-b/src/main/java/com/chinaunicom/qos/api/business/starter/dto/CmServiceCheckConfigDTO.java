package com.chinaunicom.qos.api.business.starter.dto;

import com.chinaunicom.qos.common.enums.CmServiceCheckKeyEnum;
import com.chinaunicom.qos.common.enums.StatusEnum;
import jakarta.validation.constraints.AssertFalse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-2-25 10:32
 */

@Data
public class CmServiceCheckConfigDTO {

    @NotBlank(message = "checkConfig.checkKey不能为空")
    private String checkKey;

    private String checkValue;

    @NotNull(message = "checkConfig.status不能为空")
    private Integer status;

    @AssertFalse(message = "status的值不可识别")
    public boolean isStatusValid() {
        return this.status != null && StatusEnum.findByCode(this.status) == null;
    }

    @AssertFalse(message = "checkKey的值不可识别")
    public boolean isCheckKeyValid() {
        return StringUtils.isNotBlank(this.checkKey) && CmServiceCheckKeyEnum.findByKey(this.checkKey) == null;
    }

    @AssertFalse(message = "目标IP个数限制的checkValue的值必须是数字")
    public boolean isTargetIpMaxCntCheckValueValid() {
        return CmServiceCheckKeyEnum.TARGET_IP_MAX_COUNT.getKey().equals(this.checkKey)
                && StatusEnum.ACTIVE.getStatus().equals(this.status) && !StringUtils.isNumeric(this.checkValue);
    }

}
