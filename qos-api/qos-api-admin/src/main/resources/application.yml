server:
  port: 31200
spring:
  profiles:
    active: @profile.env@
  application:
    name: qos-api-admin
  jackson:
    time_zone: GMT+8
    date_format: yyyy-MM-dd HH:mm:ss
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      pool-name: mysqlHikariPool
      keepalive-time: 60000
      mapper-package: "com.chinaunicom.qos.api.common.infrastructure.dal.mapper.mysql"
      xml-location: "classpath*:mapper/*.xml"
    clickhouse:
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
      pool-name: ClickHouseHikariPool
      keepalive-time: 60000
      mapper-package: "com.chinaunicom.qos.api.common.infrastructure.dal.mapper.ck"
      xml-location: "classpath*:ckmapper/*.xml"
  kafka:
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        max.poll.interval.ms: 300000
        session.timeout.ms: 300000
      max-poll-records: 30
    listener:
      ack-mode: manual
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        connections.max.idle.ms: -1
    properties:
      security:
        protocol: SASL_PLAINTEXT
      sasl:
        mechanism: SCRAM-SHA-256
        jaas:
          config: org.apache.kafka.common.security.scram.ScramLoginModule required username="${kafka.username}" password="${kafka.password}";

mybatis:
  configuration:
    map-underscore-to-camel-case: true

qosredisson:
  enable: true

# QoS权限配置
qos:
  permission:
    enabled: true

elasticsearch:
  hlc:
    scheme: http
    connection-timeout-millis: 10000
    socket-timeout-millis: 5000

qoshttp:
  enable: true
  configs:
    primaryRestTemplate:
      protocol: http
      connect-timeout: 20
      read-timeout: 20
      write-timeout: 20

dubbo:
  application:
    name: qos-api-admin
    check-serializable: false
    serialize-check-status: DISABLE
  protocol:
    name: dubbo
    port: -1
    threads: 400
    dispatcher: message
  metrics:
    protocol: prometheus

xxl:
  job:
    autoconfig:
      enable: true
    admin:
      addresses: ${xxljobadmin.address}
    executor:
      appname: xxl-job-executor-admin-server
    accessToken: ${xxljobadmin.accessToken}

admin:
  query-user-info-qps:
    max: 200
    min: 10

management:
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
        http.client.requests: 0.5,0.9,0.95,0.99
      percentiles-histogram:
        http.server.requests: true
        http.client.requests: true

white:
  url:
    securities:
      - /actuator/prometheus
      - /qos/admin/auth/login
      - /qos/admin/auth/captcha/image
      - /qos/admin/auth/sms/send
      - /qos/admin/auth/sms/verify
      - /qos/admin/auth/login
    api_logs:
      - /actuator/prometheus

mobInfo:
  pwd: UjYzY1RVQWpvd0JI