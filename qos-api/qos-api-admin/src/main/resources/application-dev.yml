spring:
  kafka:
    bootstrap-servers: *************:32059,*************:32059,*************:32059
  datasource:
    mysql:
      url: ************************************************************************************************************************
      username: slice_dynamic
      password: '{qoscipher}b2c93de171ec60c6c5e95aa2bac2f4ec3186da419b117aeb0fc8f28a8becc75d9dc65c2695d54cda853f96ba02b1bb04'
    clickhouse:
      url: ***************************************************
      username: pcc
      password: '{qoscipher}a90eb3d61a8c411854697bc7f5788a0148caa93d0e42b771f5e2ffc213dd14c3'
dubbo:
  registry:
    address: nacos://*************:38848
    username: ${nacos.username}
    password: ${nacos.password}
  consumer:
    check: false
    mock: true

nacos:
  username: slice-qos
  password: '{qoscipher}a0fa42ec7450a3ca5465e4a8c79902f895d13afddae9a2940801e5f89c0c36be'
  config:
    server-addr: *************:38848

redis:
  nodes: **************:7001,**************:7001,**************:7001,**************:7001,**************:7001,**************:7001
  password: '{qoscipher}13402739b2406fb2459d6d282cd84ab4a95c4da647fd4580c378b4d7719cada2'

qosredisson:
  nodeAddresses:
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
  password: '{qoscipher}e2090e9e570725330d50c203d73b234e7434764e44c2000d114419bc1b50a802'

qosdb:
  ip: *************
  port: 33061
  username: slice_dynamic
  password: '{qoscipher}a35dc127af732809ce1866916ecce72a1aadc9dedf0501bba8743649e5758810bf22e4ed2b00635942f5a639cbff7885'
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

kafka:
  username: csmftest-kafka
  password: '{qoscipher}9e2a52e3aaf03221578650ea1761f069aa192e9b13faa745a6e42de3356a34c5165c832a8112621927c01182b11f68a6'

sftp:
  host: *************4
  useport: 3222
  user: slice-qos
  password: '{qoscipher}7862f8fb9140314f220f85af8d63b0fb0df79b63550ab01d3bd45b231a33a4f2'
qos_sftp:
  host: *************4
  useport: 3222
  user: slice-qos
  password: '{qoscipher}a947a6a8416bfd93a3ec88d53a1bf3664c7d63b82c6b8eaaab79395b9f2dc5df'

esbsftp:
  host: ${sftp.host}
  useport: ${sftp.useport}
  user: ${sftp.user}
  password: ${sftp.password}

xxljobadmin:
  address: http://*************4:38088/xxl-job-admin
  accessToken: '{qoscipher}70531ff15cab47c480b787bca74a3a83f30fc7fd93bd3c91e32acfa2e9dc6e2c961a5c589650699a76564c7786d17fd4'

admin:
  upload-dir: /update/

batch:
  beijing:
    msisdn_key: '{qoscipher}9222748dd31fd6660856f8cb7a176357df794498cb66843945a721c7abaa4f03a3c847acd90d99813d9b3fc30bcf215b'

elasticsearch:
  hlc:
    hosts: **************:31819,**************:31819,**************:31819
    username: elastic
    password: '{qoscipher}7debedc4ba36df8b2c5387f36bbda63b8a7c37df80dd2c302946d824c79118f049d1d351e73281163055ade631faa9a9'
