server:
  port: 31107
spring:
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-dev.yaml?placeholder-type=system_props

qosredisson:
  nodeAddresses:
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
  password: '{qoscipher}e2090e9e570725330d50c203d73b234e7434764e44c2000d114419bc1b50a802'

dubbo:
  application:
    name: QosApiDefBearerApplication
    qos-port: 22223
  registry:
    address: nacos://**************:38848
    username: ${nacos.username}
    password: ${nacos.password}
    parameters:
      namespace: 2038004f-af38-4fad-977d-59f35ff5415e
  protocol:
    name: dubbo
    port: 50010
  consumer:
    check: false

nacos:
  username: slice-qos
  password: '{qoscipher}a0fa42ec7450a3ca5465e4a8c79902f895d13afddae9a2940801e5f89c0c36be'
  config:
    server-addr: **************:38848

feign:
  client:
    config:
      default:
        connect-timeout: 10000
        read-timeout: 10000

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.chinaunicom.qosuser.service.CsService: debug

datasource:
  pcc:
    password: '{qoscipher}7437fb05da76cc79fad385b20cc4bb84f7b6ed74a7ada8253b11f1efaf261b73dc45f2a13f5cae986d020adf983ffa66'
  qos:
    password: '{qoscipher}d2c64796ec04e3738baeef14aa4517330a2f2988df01840109fe98e091231a9f4451c17eac9ac6b57c4a34f56597119f'

sftp:
  host: **************
  useport: 3222
  user: slice-qos
  password: '{qoscipher}7862f8fb9140314f220f85af8d63b0fb0df79b63550ab01d3bd45b231a33a4f2'

system:
  north:
    zfu:
      addPlanNotify: http://qos-mock:8800/qos/common/success
      addResultNotify: http://qos-mock:8800/qos/common/success
      terminatePlanNotify: http://qos-mock:8800/qos/common/success
      terminateResultNotify: http://qos-mock:8800/qos/common/success