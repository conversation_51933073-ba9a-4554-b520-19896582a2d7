package com.chinaunicom.qos.api.common.infrastructure.cache;

import com.chinaunicom.qos.api.common.infrastructure.dal.mapper.mysql.SystemRolePermissionMapper;
import com.chinaunicom.qos.api.common.infrastructure.dal.po.SystemRolePermissionPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 角色权限缓存
 * 只有在启用权限功能时才会装配和初始化
 *
 * <AUTHOR>
 * @date 2025-07-31 16:11
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "qos.permission.enabled")
public class RolePermissionCache {

    @Resource
    private SystemRolePermissionMapper systemRolePermissionMapper;

    /**
     * 角色权限映射缓存 Map<roleCode, Set<permissionCode>>
     */
    private Map<String, Set<String>> cache = new ConcurrentHashMap<>();

    /**
     * 初始化权限缓存
     */
    @PostConstruct
    public void init() {
        log.info("权限功能已启用，开始初始化权限缓存...");
        loadCache();
        log.info("权限缓存初始化完成");
    }

    /**
     * 加载缓存数据
     */
    public void loadCache() {
        try {
            List<SystemRolePermissionPO> rolePermissionList = systemRolePermissionMapper.listAll();
            
            if (CollectionUtils.isEmpty(rolePermissionList)) {
                cache.clear();
                return;
            }

            // 按角色编码分组，构建角色权限映射
            Map<String, Set<String>> newCache = rolePermissionList.stream()
                    .filter(rp -> rp.getRoleCode() != null && rp.getPermissionCode() != null)
                    .collect(Collectors.groupingBy(
                            SystemRolePermissionPO::getRoleCode,
                            Collectors.mapping(
                                    SystemRolePermissionPO::getPermissionCode,
                                    Collectors.toSet()
                            )
                    ));

            this.cache = new ConcurrentHashMap<>(newCache);
        } catch (Exception e) {
            log.error("加载角色权限缓存失败", e);
        }
    }

    /**
     * 根据角色编码获取权限集合
     *
     * @param roleCode 角色编码
     * @return 权限编码集合（不可修改视图）
     */
    public Set<String> getPermissionsByRoleCode(String roleCode) {
        if (roleCode == null) {
            return Collections.emptySet();
        }
        Set<String> permissions = cache.get(roleCode);
        if (permissions == null) {
            return Collections.emptySet();
        }
        return Collections.unmodifiableSet(permissions);
    }

    /**
     * 获取所有角色权限映射
     *
     * @return 角色权限映射（不可修改视图）
     */
    public Map<String, Set<String>> getAllRolePermissions() {
        return Collections.unmodifiableMap(cache);
    }

    /**
     * 每5分钟更新一次缓存
     */
    @Scheduled(initialDelay = 300, fixedRate = 300, timeUnit = TimeUnit.SECONDS)
    public void updateCache() {
        loadCache();
    }
}
