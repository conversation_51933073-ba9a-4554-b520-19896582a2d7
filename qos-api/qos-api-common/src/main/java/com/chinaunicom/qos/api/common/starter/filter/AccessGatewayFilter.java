package com.chinaunicom.qos.api.common.starter.filter;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.chinaunicom.qos.api.common.application.enums.ApiResponseCodeEnum;
import com.chinaunicom.qos.api.common.application.model.LogBodyBO;
import com.chinaunicom.qos.api.common.application.service.ApiLogService;
import com.chinaunicom.qos.api.common.application.service.NorthSystemService;
import com.chinaunicom.qos.api.common.starter.config.FlowControlConfig;
import com.chinaunicom.qos.api.common.starter.config.UrlWhiteConfig;
import com.chinaunicom.qos.api.common.utils.JwtUtil;
import com.chinaunicom.qos.api.common.utils.SystemInfoUtil;
import com.chinaunicom.qos.api.common.utils.UserContext;
import com.chinaunicom.qos.api.common.utils.UserContextUtil;
import com.chinaunicom.qos.common.response.Resp;
import com.chinaunicom.qos.common.utils.JsonUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;



@WebFilter
@Slf4j
@SuppressWarnings("all")
public class AccessGatewayFilter implements Filter, Ordered {

    private final static String QOS_PRODUCT_ID = "qosProductId";
    public static final String OPERATOR_INFO = "operatorInfo";
    public static final String USER_NAME = "userName";
    public static final String PHONE_NUMBER = "phoneNumber";

    @Value("${jwt.exp:604800000}")
    private Long lastJwtExp;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private NorthSystemService northSystemService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private FlowControlConfig flowControlConfig;

    @Resource
    private ApiLogService apiLogService;

    @Resource
    private UrlWhiteConfig urlWhiteConfig;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;

        var logBody = new LogBodyBO();
        logBody.setCallerIp(request.getRemoteAddr());
        logBody.setRequestMethod(request.getMethod());
        logBody.setRequestUri(request.getRequestURI());
        logBody.setBeginTime(new Date());

        // 接入鉴权，从请求头获取token
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            token = request.getHeader("Authorization");
        }
        String uri = request.getRequestURI();

        // 预检请求不鉴权
        if (!"OPTIONS".equals(request.getMethod()) && !verifyUrlAndToken(token, uri)) {
            HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;

            Resp<Void> resp = buildHttpResp((HttpServletResponse) servletResponse,
                    ApiResponseCodeEnum.R_AUTH_NOT_PASS.getCode(), ApiResponseCodeEnum.R_AUTH_NOT_PASS.getMessage());
            logBody.setHttpStatus(((HttpServletResponse) servletResponse).getStatus());
            logBody.setRespCode(resp.getCode());
            logBody.setRespMsg(resp.getMsg());
            logBody.setTraceId(resp.getTraceId());
            logBody.setEndTime(new Date());
            var duration = logBody.getEndTime().getTime() - logBody.getBeginTime().getTime();
            logBody.setDuration(duration);
            apiLogService.writeApiLog(logBody);

            return;
        }

        try {
            SystemInfoUtil.setSystemId(getSystemId(token));
            logBody.setCaller(SystemInfoUtil.getSystemId());

            final ObjectNode requestInfo = getRequestObjectNode(request);
            String qosProductId = StringUtils.EMPTY;
            if (requestInfo != null && requestInfo.has(QOS_PRODUCT_ID)) {
                qosProductId = requestInfo.get(QOS_PRODUCT_ID).asText();
            }
            logBody.setQosProductId(qosProductId);

            // 赋值请求里operatorInfo操作人员信息
            setOperatorInfo(requestInfo, logBody);

            //解析JWT里的操作用户信息
            setUserContextFromToken(token);
            //获取操作用户信息复制给logBody，JWT优先覆盖请求体信息
            if (UserContextUtil.getUserContext() != null) {
                String jwtUserName = UserContextUtil.getUserName();
                String jwtPhone = UserContextUtil.getPhone();
                if (StringUtils.isNotBlank(jwtUserName)) {
                    logBody.setOperatorUserName(jwtUserName);
                }
                if (StringUtils.isNotBlank(jwtPhone)) {
                    logBody.setOperatorPhone(jwtPhone);
                }
            }

            // 入口流控
            if (!tryAcquire(request, qosProductId)) {
                Resp<Void> resp = buildHttpResp((HttpServletResponse) servletResponse,
                        ApiResponseCodeEnum.R_FLOW_CONTROL.getCode(), ApiResponseCodeEnum.R_FLOW_CONTROL.getMessage());
                logBody.setHttpStatus(((HttpServletResponse) servletResponse).getStatus());
                logBody.setRespCode(resp.getCode());
                logBody.setRespMsg(resp.getMsg());
                logBody.setTraceId(resp.getTraceId());
                logBody.setEndTime(new Date());
                var duration = logBody.getEndTime().getTime() - logBody.getBeginTime().getTime();
                logBody.setDuration(duration);
                apiLogService.writeApiLog(logBody);
                return;
            }

            // 判断是否是二进制响应请求（文件下载、图片验证码等）
            boolean isBinaryResponse = isBinaryResponseRequest(request);
            // 如果是二进制响应，直接放行并记录基础信息
            if (isBinaryResponse) {
                try {
                    HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;

                    filterChain.doFilter(request, httpServletResponse);
                    logBody.setHttpStatus(httpServletResponse.getStatus());
                    // 根据请求类型设置不同的日志消息
                    String respMsg = getBinaryResponseMessage(request);
                    logBody.setRespMsg(respMsg);
                } finally {
                    logBody.setEndTime(new Date());
                    logBody.setDuration(logBody.getEndTime().getTime() - logBody.getBeginTime().getTime());
                    apiLogService.writeApiLog(logBody);
                }
            } else {
                var response = new BodyReaderResponseWrapper((HttpServletResponse) servletResponse);
                filterChain.doFilter(servletRequest, response);
                var textContent = response.getTextContent();
                servletResponse.getOutputStream().write(textContent.getBytes());

                logBody.setHttpStatus(response.getStatus());
                if (HttpServletResponse.SC_OK == response.getStatus() && MimeTypeUtils.APPLICATION_JSON_VALUE.equals(response.getContentType())) {
                    var resp = JsonUtil.string2Obj(textContent, Resp.class);
                    if (resp != null) {
                        logBody.setRespCode(resp.getCode());
                        logBody.setRespMsg(resp.getMsg());
                        logBody.setTraceId(resp.getTraceId());
                    } else {
                        log.info("resp is null");
                    }
                }
                logBody.setEndTime(new Date());
                var duration = logBody.getEndTime().getTime() - logBody.getBeginTime().getTime();
                logBody.setDuration(duration);

                apiLogService.writeApiLog(logBody);
            }
        } finally {
            SystemInfoUtil.clear();
            UserContextUtil.clear();
        }
    }

    private void setOperatorInfo(ObjectNode requestInfo, LogBodyBO logBody) {
        if (requestInfo != null && requestInfo.has(OPERATOR_INFO)) {
            var operatorInfoNode = requestInfo.get(OPERATOR_INFO);
            if (operatorInfoNode != null) {
                if (operatorInfoNode.has(USER_NAME)) {
                    // URL解码处理，防止中文用户名被编码
                    logBody.setOperatorUserName(urlDecode(operatorInfoNode.get(USER_NAME).asText()));
                }
                if (operatorInfoNode.has(PHONE_NUMBER)) {
                    logBody.setOperatorPhone(operatorInfoNode.get(PHONE_NUMBER).asText());
                }
            }
        }
    }

    // 判断是否是二进制响应请求（需要绕过 BodyReaderResponseWrapper）
    private boolean isBinaryResponseRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String contentType = request.getContentType();
        return uri.contains("/download")
            || uri.contains("/captcha/image")
            || "application/octet-stream".equals(contentType);
    }

    // 根据请求类型获取二进制响应的日志消息
    private String getBinaryResponseMessage(HttpServletRequest request) {
        String uri = request.getRequestURI();
        if (uri.contains("/captcha/image")) {
            return "CAPTCHA_IMAGE";
        } else if (uri.contains("/download")) {
            return "FILE_DOWNLOAD";
        } else {
            return "BINARY_RESPONSE";
        }
    }

    private boolean verifyUrlAndToken(String token, String uri) {
        if (urlWhiteConfig.getSecurities().contains(uri)) {
            return true;
        }
        try {
            String systemIdKey = "systemId";
            DecodedJWT jwt = JWT.decode(token);
            String systemId = jwt.getClaim(systemIdKey).asString();
            Date exp = jwt.getExpiresAt();
            Date currentDate = new Date();
            Date lastJwtExpDate = new Date(currentDate.getTime() + lastJwtExp);
            if (systemId == null || exp == null || exp.after(lastJwtExpDate)) {
                log.info("verifyUrlAndToken systemId is null or exp is invalid");
                return false;
            }
            String systemSecret = northSystemService.getSystemSecret(systemId);
            if (systemSecret == null) {
                log.info("verifyUrlAndToken systemSecret is null");
                return false;
            }
            Algorithm algorithm = Algorithm.HMAC256(systemSecret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim(systemIdKey, systemId).build();
            verifier.verify(token);
            return true;
        } catch (Exception e) {
            log.info("token verifier error, token = {}", token, e);
            return false;
        }
    }

    private String getSystemId(String token) {
        if (StringUtils.isBlank(token)) {
            return token;
        }
        String systemIdKey = "systemId";
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim(systemIdKey).asString();
    }

    /**
     * 从JWT中解析用户上下文并设置到线程本地存储
     *
     * @param token JWT token
     */
    private void setUserContextFromToken(String token) {
        if (StringUtils.isBlank(token)) {
            return;
        }

        try {
            UserContext userContext = JwtUtil.parseUserContext(token);
            if (userContext != null) {
                UserContextUtil.setUserContext(userContext);
                log.debug("成功设置用户上下文: {}", userContext);
            }
        } catch (Exception e) {
            log.warn("从JWT中解析用户上下文时发生异常，将跳过用户上下文设置", e);
        }
    }

    private boolean tryAcquire(HttpServletRequest request, String qosProductId) throws IOException {
        String uri = request.getRequestURI().toString();
        if (StringUtils.isBlank(qosProductId) || StringUtils.isBlank(uri)) {
            return true;
        }

        final String key = genKey(uri, qosProductId);
        try {
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
            Integer limitQps = flowControlConfig.getLimitQps(uri, qosProductId);
            RateLimiterConfig config = rateLimiter.getConfig();
            if (config != null && Objects.nonNull(limitQps)) {
                long rate = config.getRate();
                if (rate <= 0) { // 判断是否存在限流器
                    rateLimiter.trySetRate(RateType.OVERALL, limitQps, 1, RateIntervalUnit.SECONDS);
                } else if (rate != limitQps) { // 动态更新限流规则
                    rateLimiter.setRate(RateType.OVERALL, limitQps, 1, RateIntervalUnit.SECONDS);
                }
                boolean acquired = rateLimiter.tryAcquire(50, TimeUnit.MILLISECONDS);
                log.info("rateLimiter acquire result, key:{}, acquired:{}. limitQps={}, rate={}", key, acquired, limitQps, rate);
                return acquired;
            }
        } catch (Exception e) {
            log.error("rateLimiter acquire failed, 请求流控放通. key:{}", key, e);
        }

        return true;
    }


    private ObjectNode getRequestObjectNode(HttpServletRequest request) throws IOException {
        try {
            String contentType = request.getContentType();
            if (StringUtils.isNotBlank(contentType) && !"application/json".equalsIgnoreCase(contentType)) {
                return null;
            }
            String body = StreamUtils.copyToString(request.getInputStream(), Charset.forName("UTF-8"));
            // 解析body
            return JsonUtil.string2Obj(body, ObjectNode.class);
        } catch (Exception e) {
            log.warn("api access gateway getQosProductId error", e);
        }
        return null;
    }

    private String genKey(String uri, String qosProductId) {
        return uri + "@" + qosProductId;
    }

    private Resp<Void> buildHttpResp(HttpServletResponse response, int code, String msg) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        PrintWriter out = null;
        try {
            Resp<Void> resp = new Resp<>();
            resp.setCode(code);
            resp.setMsg(msg);

            out = response.getWriter();
            out.append(JsonUtil.obj2String(resp));
            return resp;
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    /**
     * URL解码，安全处理URL编码的字符串
     *
     * @param value 待解码的字符串
     * @return 解码后的字符串，如果解码失败则返回原字符串
     */
    private String urlDecode(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        try {
            // 使用UTF-8编码进行URL解码
            return URLDecoder.decode(value, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            // 解码失败时记录警告并返回原值，确保系统稳定性
            log.warn("URL解码失败，使用原值: {}", value, e);
            return value;
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
