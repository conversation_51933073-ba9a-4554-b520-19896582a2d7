package com.chinaunicom.qos.api.common.starter.config;

import com.chinaunicom.qos.api.common.infrastructure.cache.RolePermissionCache;
import com.chinaunicom.qos.api.common.application.annotation.QosCheckPermission;
import com.chinaunicom.qos.api.common.application.enums.ApiResponseCodeEnum;
import com.chinaunicom.qos.api.common.utils.UserContextUtil;
import com.chinaunicom.qos.common.response.Resp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * QoS权限检查切面
 * 支持基于角色和权限的访问控制，使用Nacos动态配置
 * 只有在启用权限功能时才会装配
 *
 * <AUTHOR>
 * @date 2025-08-01 09:13
 */
@Slf4j
@Component
@Aspect
@ConditionalOnProperty(name = "qos.permission.enabled")
public class QosPermissionCheckAop {

    @Resource
    private RolePermissionCache rolePermissionCache;

    @Resource
    private DynamicPermissionConfig dynamicPermissionConfig;

    @Around("@annotation(com.chinaunicom.qos.api.common.application.annotation.QosCheckPermission)")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        QosCheckPermission annotation = method.getAnnotation(QosCheckPermission.class);

        // 生成权限键
        String permissionKey = generatePermissionKey(annotation, method);
        log.info("开始执行权限检查，方法: {}, 权限键: {}", signature.getName(), permissionKey);

        // 执行权限检查
        boolean hasPermission = checkPermission(permissionKey);

        // 权限检查未通过，返回403错误
        if (!hasPermission) {
            log.warn("权限检查失败，方法: {}, 权限键: {}, 用户角色: {}",
                    signature.getName(), permissionKey, UserContextUtil.getRoles());
            Resp<Void> resp = new Resp<>();
            resp.setCode(ApiResponseCodeEnum.R_AUTH_FORBIDDEN.getCode());
            resp.setMsg(ApiResponseCodeEnum.R_AUTH_FORBIDDEN.getMessage());
            resp.setTraceId(TraceContext.traceId());
            return resp;
        }

        // 执行目标方法
        return joinPoint.proceed();
    }

    /**
     * 生成权限键
     *
     * @param annotation 权限注解
     * @param method 方法对象
     * @return 权限键
     */
    private String generatePermissionKey(QosCheckPermission annotation, Method method) {
        // 1. 如果注解中指定了 value，优先使用
        if (StringUtils.isNotBlank(annotation.value())) {
            return annotation.value();
        }

        // 2. 自动生成：类名_方法名
        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        return className + "_" + methodName;
    }

    /**
     * 检查用户权限
     *
     * @param permissionKey 权限键
     * @return 是否有权限
     */
    private boolean checkPermission(String permissionKey) {
        // 从Nacos动态配置获取权限配置
        DynamicPermissionConfig.PermissionConfig dynamicConfig = dynamicPermissionConfig.getRules().get(permissionKey);
        if (dynamicConfig != null && Boolean.TRUE.equals(dynamicConfig.getEnabled())) {
            log.debug("使用Nacos动态配置进行权限检查: {}", permissionKey);
            return checkWithDynamicConfig(dynamicConfig);
        }else{
            log.info("权限配置不存在或未启用，直接通过 - permissionKey: {}", permissionKey);
            return true;
        }
    }

    /**
     * 使用Nacos动态配置进行权限检查
     *
     * @param dynamicConfig 动态权限配置
     * @return 是否有权限
     */
    private boolean checkWithDynamicConfig(DynamicPermissionConfig.PermissionConfig dynamicConfig) {
        List<String> requiredRoles = dynamicConfig.getRoles();
        List<String> requiredPermissions = dynamicConfig.getPermissions();

        // 如果都没有指定要求，直接通过
        if (CollectionUtils.isEmpty(requiredRoles) && CollectionUtils.isEmpty(requiredPermissions)) {
            log.debug("动态配置未指定角色或权限要求，直接通过");
            return true;
        }

        // 获取用户角色
        List<String> userRoles = UserContextUtil.getRoles();
        if (CollectionUtils.isEmpty(userRoles)) {
            //因存在qos-api-b的调用，但是调用qos-api-b的调用是不会带roles的，如果这个位置拦截了，会存在问题，后续创建平台账号的时候，必须配置角色，后续观察看看
            log.debug("用户角色为空，直接通过");
            return true;
        }

        log.debug("用户角色: {}, 动态配置要求角色: {}, 要求权限: {}", userRoles, requiredRoles, requiredPermissions);

        return checkUserPermission(userRoles, requiredRoles, requiredPermissions);
    }

    private boolean checkUserPermission(List<String> userRoles, List<String> requiredRoles, List<String> requiredPermissions) {
        // 检查角色匹配
        for (String requiredRole : requiredRoles) {
            if (StringUtils.isNotBlank(requiredRole) && userRoles.contains(requiredRole)) {
                log.debug("动态配置角色匹配成功: {}", requiredRole);
                return true;
            }
        }

        // 检查权限匹配
        if (!CollectionUtils.isEmpty(requiredPermissions)) {
            Set<String> userPermissions = getUserPermissions(userRoles);
            for (String requiredPermission : requiredPermissions) {
                if (StringUtils.isNotBlank(requiredPermission) && userPermissions.contains(requiredPermission)) {
                    log.debug("动态配置权限匹配成功: {}", requiredPermission);
                    return true;
                }
            }
        }
        return false;
    }



    /**
     * 获取用户所有权限
     *
     * @param userRoles 用户角色列表
     * @return 用户权限集合
     */
    private Set<String> getUserPermissions(List<String> userRoles) {
        Set<String> userPermissions = new HashSet<>();

        for (String userRole : userRoles) {
            if (StringUtils.isNotBlank(userRole)) {
                Set<String> rolePermissions = rolePermissionCache.getPermissionsByRoleCode(userRole);
                if (!CollectionUtils.isEmpty(rolePermissions)) {
                    userPermissions.addAll(rolePermissions);
                }
            }
        }

        log.debug("用户总权限: {}", userPermissions);
        return userPermissions;
    }
}
