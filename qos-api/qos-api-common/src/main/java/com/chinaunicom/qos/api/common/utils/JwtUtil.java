package com.chinaunicom.qos.api.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.chinaunicom.qos.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Slf4j
public class JwtUtil {

    // JWT Claim 常量
    private static final String CLAIM_SYSTEM_ID = "systemId";
    private static final String CLAIM_USER_NAME = "userName";
    private static final String CLAIM_PHONE = "phone";
    private static final String CLAIM_ROLES = "roles";

    // 其他常量
    private static final String BEARER_PREFIX = "Bearer ";
    private static final int BEARER_PREFIX_LENGTH = 7;

    private JwtUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static String create(String systemId, String password) {
        return create(systemId, password, false);
    }

    /**
     * 创建JWT token
     *
     * @param systemId 系统ID
     * @param password 密码
     * @return JWT token
     */
    public static String create(String systemId, String password, boolean rememberMe) {
        try {
            // 指定过期时间
            final long expireTime = (rememberMe ? 7L : 1L) * 24 * 60 * 60 * 1000L;
            Date date = new Date(System.currentTimeMillis() + expireTime);
            Algorithm algorithm = Algorithm.HMAC256(password);
            return JWT.create()
                    .withClaim(CLAIM_SYSTEM_ID, systemId)
                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("create token error", e);
            return null;
        }
    }

    /**
     * 创建增强JWT token
     *
     * @param systemId 系统ID
     * @param password 密码
     * @param userName 用户名
     * @param phone 手机号
     * @param roles 角色列表
     * @param rememberMe 是否记住我
     * @return JWT token
     */
    public static String createEnhancedToken(String systemId, String password, String userName, String phone, List<String> roles, boolean rememberMe) {
        try {
            // 指定过期时间
            final long expireTime = (rememberMe ? 7L : 1L) * 24 * 60 * 60 * 1000L;
            Date date = new Date(System.currentTimeMillis() + expireTime);
            Algorithm algorithm = Algorithm.HMAC256(password);
            return JWT.create()
                    .withClaim(CLAIM_SYSTEM_ID, systemId)
                    .withClaim(CLAIM_USER_NAME, userName)
                    .withClaim(CLAIM_PHONE, phone)
                    .withClaim(CLAIM_ROLES, JsonUtil.obj2String(roles))
                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("create enhanced token error", e);
            return null;
        }
    }

    /**
     * 解析token获取systemId（不验证有效性）
     *
     * @param token JWT token
     * @return systemId
     */
    public static String getSystemId(String token) {
        try {
            if (token == null || token.isEmpty()) {
                return null;
            }

            // 去掉Bearer前缀
            if (token.startsWith(BEARER_PREFIX)) {
                token = token.substring(BEARER_PREFIX_LENGTH);
            }

            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(CLAIM_SYSTEM_ID).asString();
        } catch (Exception e) {
            log.error("get systemId from token error", e);
            return null;
        }
    }

    /**
     * 解析token获取用户上下文（不验证有效性）
     *
     * @param token JWT token
     * @return 用户上下文
     */
    public static UserContext parseUserContext(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        DecodedJWT jwt = JWT.decode(token);
        String userName = jwt.getClaim(CLAIM_USER_NAME).asString();
        String phone = jwt.getClaim(CLAIM_PHONE).asString();
        String rolesJson = jwt.getClaim(CLAIM_ROLES).asString();
        //检查userName、phone、rolesJson不能都为空，都为空则返回null
        if (StringUtils.isBlank(userName) && StringUtils.isBlank(phone) && StringUtils.isBlank(rolesJson)) {
            return null;
        }
        // 从JSON字符串解析角色列表
        List<String> roles = JsonUtil.parseList(rolesJson, String.class);

        return new UserContext(userName, phone, roles);
    }
}
