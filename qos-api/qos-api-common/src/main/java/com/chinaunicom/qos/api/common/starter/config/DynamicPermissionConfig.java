package com.chinaunicom.qos.api.common.starter.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态权限配置类
 * 从Nacos读取方法级权限配置，支持热更新
 * 只有在启用权限功能时才会装配
 *
 * <AUTHOR>
 * @date 2025-08-01 09:13
 */
@Component
@ConditionalOnProperty(name = "qos.permission.enabled")
@NacosConfigurationProperties(dataId = "qos-dynamic-permission-config", type = ConfigType.YAML, autoRefreshed = true)
@Data
public class DynamicPermissionConfig {

    /**
     * 权限配置映射
     * key: 权限键（value 或 类名_方法名）
     * value: 权限配置详情
     */
    private Map<String, PermissionConfig> rules = new HashMap<>();

    /**
     * 权限配置详情
     */
    @Data
    public static class PermissionConfig {
        
        /**
         * 需要的角色列表
         * 用户拥有其中任一角色即可通过验证
         */
        private List<String> roles = new ArrayList<>();
        
        /**
         * 需要的权限列表
         * 用户拥有其中任一权限即可通过验证
         */
        private List<String> permissions = new ArrayList<>();

        /**
         * 是否启用该权限配置
         */
        private Boolean enabled = true;
    }
}
