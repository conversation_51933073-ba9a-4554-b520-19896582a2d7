server:
  port: 31107
spring:
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-prod.yaml?placeholder-type=system_props

qosredisson:
  nodeAddresses:
    - redis://**************:32514
    - redis://**************:32536
    - redis://**************:32512
    - redis://**************:32514
    - redis://**************:32579
    - redis://**************:32580
  password: '{qoscipher}7cd60ead2929463071aa03c01bc529fc4bfe9ce77e7f7dbb9b75ed8a7c944753'

dubbo:
  application:
    name: QosApiDefBearerApplication
    check-serializable: false
    serialize-check-status: DISABLE
  consumer:
    timeout: 30000
    check: false
  registry:
    address: nacos://nacos-server-headless.slice-qos:8848
    username: ${nacos.username}
    password: ${nacos.password}
  protocol:
    name: dubbo
    port: 50010
nacos:
  username: slice-qos
  password: '{qoscipher}d85e9f01561ca81f7a4b138fb6da09e624c87281ed8db66ee066d1371e728160'
  config:
    server-addr: nacos-server-headless.slice-qos:8848

feign:
  client:
    config:
      default:
        connect-timeout: 10000
        read-timeout: 10000

datasource:
  pcc:
    password: '{qoscipher}42ce2d8e084ca9d2361ab896f7dfd49e72cf6c04abfe8d696c9644c8689dc92a7c34deb7eac2281488be28647412d11d'
  qos:
    password: '{qoscipher}3c81a312fb12749fb93adabbcae43e85c71800a72a066d78e7fcb93c175bd961878976fb5dc09b57197cf0c753caee72'

sftp:
  host: slice-qos-proftpd.transmit
  useport: 22
  user: slice-qos
  password: '{qoscipher}a215ecb4df7eb5551438dda5b88722936bc4c3986dc4c13e78f95691bdd26bf5'

system:
  north:
    bj-batch:
      addPlanNotify: needless
      addResultNotify: needless
      terminatePlanNotify: needless
      terminateResultNotify: needless
    gd-mlts-platform:
      addPlanNotify: http://************:8888/qos/defbearer/notify/batchApplyPlan
      addResultNotify: http://************:8888/qos/defbearer/notify/batchApplyCall
      terminatePlanNotify: http://************:8888/qos/defbearer/notify/batchTerminatePlan
      terminateResultNotify: http://************:8888/qos/defbearer/notify/batchTerminateResult
    quanke:
      addResultNotify: http://pcc-self-oper-recv.slice.svc.cluster.local:31901/api/qos/callback/sync-result