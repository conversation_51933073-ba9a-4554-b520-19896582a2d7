databaseName: sharding

mode:
  type: Standalone
  repository:
    type: JDBC

dataSources:
  qos:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************
    username: slice_dynamic
    password: $${datasource.qos.password::}
  pcc:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: slice_dynamic
    password: $${datasource.pcc.password::}
rules:
- !SHARDING
  tables: # 数据分片规则配置
    north_system_info:
      actualDataNodes: qos.north_system_info
      tableStrategy:
        none:
          shardingColumn: none
    qos_notify_url:
      actualDataNodes: qos.qos_notify_url
      tableStrategy:
        none:
          shardingColumn: none

    qos_order_id:
      actualDataNodes: pcc.qos_order_id
      tableStrategy:
        none:
          shardingColumn: none
    user_user_bear_curinfo:
      actualDataNodes: pcc.user_user_bear_curinfo
      tableStrategy:
        none:
          shardingColumn: none
props:
  sql-show: true