server:
  port: 31107
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver              # mysql驱动包
    url: *****************************************************************************************************************
    username: slice_dynamic
    password: '{qoscipher}801aa6bd2f4f7266ee7741a29b2f8aa50355df1c51b71cc176dab368dcd27e17858bf4e0c210c6b35620cb070f9f3f13'

qosredisson:
  nodeAddresses:
    - redis://*************:6379
    - redis://************0:6379
    - redis://*************:6379
    - redis://*************:6379
    - redis://************:6379
    - redis://*************:6379
  password: '{qoscipher}0adda5ffa887fca6d02660a58e11d26241bc67fcd144d9c743bf395ed7e904a3'

dubbo:
  application:
    name: QosApiDefBearerApplication
    check-serializable: false
    serialize-check-status: DISABLE
  consumer:
    timeout: 30000
    check: false
  registry:
    address: nacos://nacos-server-headless.slice-qos:38848
    username: ${nacos.username}
    password: ${nacos.password}
  protocol:
    name: dubbo
    port: -1

nacos:
  username: slice-qos
  password: '{qoscipher}2fb62a5843ee317e7802ef5774a192c4b5c410e73126078c768fbb854fe7c594'
  config:
    server-addr: nacos-server-headless.slice-qos:38848
