server:
  port: 31107
spring:

qosredisson:
  nodeAddresses:
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
    - redis://**************:7001
  password: '{qoscipher}e2090e9e570725330d50c203d73b234e7434764e44c2000d114419bc1b50a802'

dubbo:
  application:
    name: QosApiDefBearerApplication
    check-serializable: false
    serialize-check-status: DISABLE
  consumer:
    timeout: 30000
    check: false
  registry:
    address: nacos://nacos-server-headless.slice-qos:8848
    username: ${nacos.username}
    password: ${nacos.password}
  protocol:
    name: dubbo
    port: 50010
nacos:
  username: slice-qos
  password: '{qoscipher}a0fa42ec7450a3ca5465e4a8c79902f895d13afddae9a2940801e5f89c0c36be'
  config:
    server-addr: nacos-server-headless.slice-qos:8848

feign:
  client:
    config:
      default:
        connect-timeout: 10000
        read-timeout: 10000

logging:
  level:
    com.chinaunicom.qosuser.service.CsService: debug

datasource:
  pcc:
    password: '{qoscipher}64e7df92889e8b7151512daf838049e49ad478b830ceef24c8272a42e59946673a514cd23f74ddeea175f98b34f5e11f'
  qos:
    password: '{qoscipher}a35dc127af732809ce1866916ecce72a1aadc9dedf0501bba8743649e5758810bf22e4ed2b00635942f5a639cbff7885'

sftp:
  host: **************
  useport: 3222
  user: slice-qos
  password: '{qoscipher}a947a6a8416bfd93a3ec88d53a1bf3664c7d63b82c6b8eaaab79395b9f2dc5df'

system:
  north:
    bj-batch:
      addPlanNotify: needless
      addResultNotify: needless
      terminatePlanNotify: needless
      terminateResultNotify: needless
    zfu:
      addPlanNotify: http://qos-mock:8800/qos/common/success
      addResultNotify: http://qos-mock:8800/qos/common/success
      terminatePlanNotify: http://qos-mock:8800/qos/common/success
      terminateResultNotify: http://qos-mock:8800/qos/common/success
    ysh:
      addPlanNotify: http://qos-mock:8800/qos/common/success
      addResultNotify: http://qos-mock:8800/qos/common/success
      terminatePlanNotify: http://qos-mock:8800/qos/common/success
      terminateResultNotify: http://qos-mock:8800/qos/common/success
    gd-mlts-platform:
      addPlanNotify: http://*************:8888/qos/defbearer/notify/batchApplyPlan
      addResultNotify: http://*************:8888/qos/defbearer/notify/batchApplyCall
      terminatePlanNotify: http://*************:8888/qos/defbearer/notify/batchTerminatePlan
      terminateResultNotify: http://*************:8888/qos/defbearer/notify/batchTerminateResult
    quanke:
      addPlanNotify: needless
      addResultNotify: http://pcc-self-oper-recv.slice.svc.cluster.local:31901/api/qos/callback/sync-result
      terminatePlanNotify: needless
      terminateResultNotify: needless

mock-local:
  enable: true