package com.chinaunicom.qos.api.defbearer.starter.request;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddDefBearerDurationReq extends BaseDefBearerReq implements Serializable {
    @NotNull(message = "qosProductId不能为空")
    @ApiModelProperty("QoS产品ID")
    private Integer qosProductId;

    @NotBlank(message = "cmServiceId不能为空")
    @ApiModelProperty("通信服务ID")
    private String cmServiceId;

    @NotNull(message = "duration不能为空")
    @ApiModelProperty("时长，单位：秒")
    private Integer duration;
}
