package com.chinaunicom.qos.api.defbearer.starter.dto;

import com.chinaunicom.qos.common.annotations.FieldDesc;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
public class ApiDefBearerBatchSearchDTO implements Serializable {
    public static final String DATE_TIME_FORMATTER_14 = "yyyy-MM-dd HH:mm:ss";

    /**
     * 预期结束调用时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime expectEndTime;

    /**
     * 订单结束时间（按订单结束时间进行调用此字段不为空）
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime orderEndTime;

    @FieldDesc(desc = "归属省（两位数字）")
    private Integer homeProvince;

    /**
     * 计划任务ID
     */
    private Long planId;

    /**
     * 计划名称
     */
    private String batchName;

    /**
     * 请求来源（英文）
     */
    private String reqSource;

    /**
     * 执行号码数量
     */
    private Integer executeMsisdnCount;

    /**
     * 实际开始调用时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime relStartTime;

    @FieldDesc(desc = "订单时长(单位秒)(按时长进行调用此字段不为空)")
    private Integer duration;

    /**
     * 预期开始调用时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime expectStartTime;

    /**
     * 消息序号
     */
    private String messageId;

    @FieldDesc(desc = "终止结果文件路径")
    private String terminateResultFilePath;

    /**
     * 实际结束调用时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime relEndTime;

    /**
     * 预期终止完成时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime expectTerminateFinishTime;

    @FieldDesc(desc = "通信服务id")
    private String cmServiceId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime createTime;

    /**
     * 执行结果文件路径
     */
    private String executeResultFilePath;

    /**
     * 请求预置开始调用时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime reqExpectStartTime;

    @FieldDesc(desc = "qos产品id")
    private Integer qosProductId;

    /**
     * 状态 0-已接收请求待解析文件 1-待执行 2-执行完成待反馈执行结果 3-已反馈执行结果 4-待终止 5-终止完成待反馈终止结果 6-已反馈终止结果 7-解析失败
     */
    private Integer status;

    /**
     * 实际终止完成时间
     */
    @JsonFormat(pattern = DATE_TIME_FORMATTER_14)
    private LocalDateTime relTerminateFinishTime;
}
