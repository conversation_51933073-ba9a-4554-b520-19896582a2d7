package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-6-24 9:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QosUserOpenAccountRecordPO {

    private Long id;

    private String messageId;

    private String operSource;

    private String userId;

    private String msisdn;

    private String imsi;

    private Integer userType;

    private Integer signNetworkType;

    private Integer homeProvince;

    private Integer operType;

    private Date orderTime;

    private Integer respCode;

    private String respDesc;
}
