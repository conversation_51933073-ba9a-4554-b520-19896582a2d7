package com.chinaunicom.qos.user.common.dal.po;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductPO {
    private Integer qosProductId;
    private String qosProductName;
    private String businessOwnership;
    private Integer existPlatProduct;
    private Integer status;
    private String systemCode;
    private String qosProductDesc;
    private Integer conflictCheckFlag;
    private BigDecimal orderPriority;
    private Date createTime;
    private Date updateTime;
}
