package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.QosUserPO;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2025-8-14 09:30
 */
public class QosUserRowMapper implements RowMapper<QosUserPO> {
    @Override
    public QosUserPO mapRow(@NotNull ResultSet rs, int rowNum) throws SQLException {
        return QosUserPO.builder()
            .userId(rs.getString("user_id"))
            .msisdn(rs.getString("msisdn"))
            .imsi(rs.getString("imsi"))
            .homeProvince(rs.getObject("home_province", Integer.class))
            .userType(rs.getObject("user_type", Integer.class))
            .signNetworkType(rs.getObject("sign_network_type", Integer.class))
            .openStatus(rs.getObject("open_status", Integer.class))
            .openTime(rs.getTimestamp("open_time"))
            .closeTime(rs.getTimestamp("close_time"))
            .updateTime(rs.getTimestamp("update_time"))
            .createTime(rs.getTimestamp("create_time"))
            .qosLevel5g(rs.getObject("qos_level_5g", Integer.class))
            .qosLevel4g(rs.getObject("qos_level_4g", Integer.class))
            .build();
    }
}
