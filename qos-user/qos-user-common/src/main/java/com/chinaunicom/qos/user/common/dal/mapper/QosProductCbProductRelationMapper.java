package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.QosProductCbProductRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * QOS产品与CB产品关联关系Mapper
 *
 * <AUTHOR>
 * @date 2025/4/10 14:45
 */
public interface QosProductCbProductRelationMapper {

    /**
     * 批量插入QOS产品与CB产品关联关系
     */
    void batchInsertQosProductCbProductRelation(@Param("addList") List<QosProductCbProductRelationPO> addList);

    /**
     * 根据CB产品ID查询QOS产品与CB产品关联关系
     */
    List<QosProductCbProductRelationPO> selectByCbProductIdOrQosProductId(@Param("cbProductId") Integer cbProductId,
                                                                          @Param("qosProductId") Integer qosProductId);
}