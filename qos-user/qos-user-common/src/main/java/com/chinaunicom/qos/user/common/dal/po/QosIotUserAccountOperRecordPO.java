package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QosIotUserAccountOperRecordPO {
    
    private Long id;
    
    private String messageId;
    
    private String operSource;
    
    private String userId;
    
    private String msisdn;
    
    private String imsi;
    
    private Integer operType;
    
    private Date orderTime;
    
    private Integer respCode;
    
    private String respDesc;
    
    private Date createTime;
    
    private String traceId;
} 