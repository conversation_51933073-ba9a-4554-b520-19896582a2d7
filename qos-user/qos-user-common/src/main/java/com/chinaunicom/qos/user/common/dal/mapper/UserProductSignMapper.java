package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.api.dto.CmServiceProductCountDTO;
import com.chinaunicom.qos.user.common.dal.po.SubscribeDetailPO;
import com.chinaunicom.qos.user.common.dal.po.UserProductSignPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface UserProductSignMapper  {
    int insertUpdate(UserProductSignPO userProductSign);

    List<CmServiceProductCountDTO> queryCmServiceProductCount(@Param("list") List<String> cmServiceIdList);

    /**
     * 根据产品ID和用户ID查询订购记录
     * @param qosProductId 产品ID
     * @param userId 用户ID
     * @return 订购记录
     */
    UserProductSignPO selectByProductIdAndUserId(@Param("qosProductId") Integer qosProductId,
                                               @Param("userId") String userId);

    /**
     * 查询用户产品订购列表
     * @param msisdn 用户ID
     * @param excludeProduct 需要排除的产品ID
     * @param effectiveTime 生效时间
     * @param expireTime 失效时间
     * @param currentTime 当前时间
     * @return 产品订购列表
     */
    List<UserProductSignPO> listUserProductSigns(
            @Param("msisdn") String msisdn,
            @Param("excludeProduct") Integer excludeProduct,
            @Param("effectiveTime") Date effectiveTime,
            @Param("expireTime") Date expireTime,
            @Param("currentTime") Date currentTime
    );

    /**
     * 查询有效的用户产品订购记录
     * @param msisdn 用户ID
     * @param qosProductId 产品ID
     * @param currentTime 当前时间
     * @return 产品订购记录
     */
    UserProductSignPO selectValidUserProduct(
            @Param("msisdn") String msisdn,
            @Param("qosProductId") Integer qosProductId,
            @Param("currentTime") Date currentTime
    );

    /**
     * 更新用户产品订购的失效时间
     * @param userId 用户ID
     * @param qosProductId 产品ID
     * @param expireTime 失效时间
     * @return 影响行数
     */
    int updateExpireTime(
            @Param("userId") String userId,
            @Param("qosProductId") Integer qosProductId,
            @Param("expireTime") Date expireTime
    );

    /**
     * 查询用户订购信息及关联的通信服务ID
     */
    SubscribeDetailPO selectSubscribeDetail(@Param("qosProductId") Integer qosProductId, 
                                          @Param("userId") String userId);

    /**
     * 查询用户产品订购列表
     * @param userId 用户ID
     * @param qosProductId 产品ID
     * @return 用户产品订购列表
     */
    List<UserProductSignPO> selectUserSubscribedProducts(
            @Param("userId") String userId,
            @Param("qosProductId") Integer qosProductId,
            @Param("expireStatus") Integer expireStatus
    );
}
