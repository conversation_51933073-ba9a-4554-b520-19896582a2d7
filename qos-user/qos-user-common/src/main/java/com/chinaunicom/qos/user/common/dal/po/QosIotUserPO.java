package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QosIotUserPO {

    /**
     * 用户ID，值为物网用户IMSI
     */
    private String userId;

    /**
     * 物网用户号码
     */
    private String msisdn;

    /**
     * 物网用户IMSI
     */
    private String imsi;

    /**
     * 0已开户,-1销户
     */
    private Integer openStatus;

    /**
     * 开户时间
     */
    private Date openTime;

    /**
     * 销户时间
     */
    private Date closeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
} 