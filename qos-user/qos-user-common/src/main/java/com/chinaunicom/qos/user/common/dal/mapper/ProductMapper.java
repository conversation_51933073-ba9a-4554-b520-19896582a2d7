package com.chinaunicom.qos.user.common.dal.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.chinaunicom.qos.user.api.dto.ProductWithSystemAndUserCountDTO;
import com.chinaunicom.qos.user.common.dal.po.ProductPO;
import com.chinaunicom.qos.user.common.model.ProductQueryBO;

public interface ProductMapper {
    List<String> queryProductNameListByKeyword(String keyword);
    List<ProductWithSystemAndUserCountDTO> queryProduct(ProductQueryBO req);
    List<Integer> getProductIdByCmServiceAndSystem(@Param("cmServiceIdList") List<String> cmserviceIdList, @Param("systemIdList") List<String> systemIdList);
    List<ProductPO> queryProductsMatchingAllCmServices(@Param("cmServiceIdList") List<String> cmserviceIdList, @Param("size") Integer size);
    /**
     * 根据产品ID查询产品信息
     * @param productId 产品ID
     * @return 产品信息
     */
    ProductPO selectById(Integer productId);
    /**
     * 根据产品ID列表批量查询产品信息
     * @param productIds 产品ID列表
     * @param qosProductNameKeyWords 产品名称关键字
     * @return 产品信息列表
     */
    List<ProductPO> listByProductIdsAndName(@Param("productIds")List<Integer> productIds, @Param("qosProductNameKeyWords") String qosProductNameKeyWords);
    /**
     * 插入产品信息
     * @param product 产品信息
     * @return 影响行数
     */
    int insert(ProductPO product);
    /**
     * 根据ID更新产品信息
     * @param product 产品信息
     * @return 影响行数
     */
    int updateById(ProductPO product);

    /**
     * 查询产品调用优先级列表
     * @Param productIds：QOS产品ID列表
     * @Param hasOrderPriorityConfig: 0-配置了产品调用优先级
     */
    List<ProductPO> queryProductOrderPriorities(@Param("productIds")List<Integer> productIds,
                                                @Param("hasOrderPriorityConfig") Integer hasOrderPriorityConfig);

    int updateOrderPriority(@Param("qosProductId") Integer qosProductId, @Param("orderPriority") BigDecimal orderPriority);

}
