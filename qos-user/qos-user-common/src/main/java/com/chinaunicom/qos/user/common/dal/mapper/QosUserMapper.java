package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.QosUserPO;

/**
 * <AUTHOR>
 * @date 2024-5-21 15:41
 */

public interface QosUserMapper {

    int insertQosUser(QosUserPO qosUser);
    QosUserPO selectByUserId(String userId);
    /**
     * 根据用户ID更新用户信息
     * @param qosUserInfo 用户信息
     * @return 更新的记录数
     */
    int updateByUserId(QosUserPO qosUserInfo);

}
