package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscribeDetailPO {
    private Integer qosProductId;
    private Date effectiveTime;
    private Date expireTime;
    private String cmServiceIds; // 逗号分隔的通信服务ID
} 