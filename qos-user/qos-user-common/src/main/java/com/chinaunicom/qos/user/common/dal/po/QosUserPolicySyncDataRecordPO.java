package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 
* 
* <AUTHOR>
* @date 2024/12/10 09:56
*/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QosUserPolicySyncDataRecordPO {
    /**
    * 同步来源
    */
    private String syncSource;

    /**
    * 手机号码
    */
    private String msisdn;

    /**
    * 旧手机号码
    */
    private String oldmsisdn;

    /**
    * IMSI
    */
    private String userImsi;

    /**
    * 策略编号
    */
    private String policyId;

    /**
    * 被替换的策略编号
    */
    private String oldPolicyId;

    /**
    * 业务Qos级别
    */
    private String qosLevel;

    /**
    * 生效时间 格式：YYYYMMDDHH24MISS
    */
    private String startTime;

    /**
    * 失效时间 格式：YYYYMMDDHH24MISS
    */
    private String endTime;

    /**
    * 省分编码
    */
    private String provinceCode;

    /**
    * 用于4升5策略迁移时判断，UDM用户不做优化：0：2/3/4G用户 1：5G SA用户 HSS用户不携带
    */
    private Integer usrStatus;

    /**
    * BSS操作时间
    */
    private String operateTime;

    /**
    * 客户级别编码，统一数据模型编码CUI_000015。当前场景不需要该参数，PCC不处理
    */
    private String custClassType;

    /**
    * 计费方式0：离线计费，1：在线计费
    */
    private String chargeType;

    /**
    * 账期切换日格式：YYYYMMDD
    */
    private String billCycleDate;

    /**
    * 版本号（该接口协议版本号，此处为’01’）
    */
    private String version;

    /**
    * 同步类型 0-新增用户 1-修改用户 2-删除用户 3-新增策略 4-修改策略 5-删除策略
    */
    private Integer operType;

    /**
    * 审批状态 0000-下发 0001-不下发
    */
    private String approvalStatus;

    /**
    * 下发网元应答编码
    */
    private String respCode;

    /**
    * 应答描述，如审批状态为1，代表不下发的描述
    */
    private String respDesc;

}