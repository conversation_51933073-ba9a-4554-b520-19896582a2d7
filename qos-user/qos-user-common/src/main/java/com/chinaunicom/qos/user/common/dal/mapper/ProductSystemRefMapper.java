package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.api.dto.SystemProductAssociationCountDTO;
import com.chinaunicom.qos.user.common.dal.po.ProductIdSystemPO;
import com.chinaunicom.qos.user.common.dal.po.ProductSystemRefPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductSystemRefMapper {
    /**
     * 插入产品系统关联信息
     * @param productSystemRef 产品系统关联信息
     * @return 影响行数
     */
    int insert(ProductSystemRefPO productSystemRef);

    /**
     * 根据产品ID列表查询系统关联信息
     * @param productIds 产品ID列表
     * @return 产品系统关联信息列表
     */
    List<ProductIdSystemPO> selectProductIdAndSystemByProductIds(@Param("productIds") List<Integer> productIds);

    /**
     * 查询系统关联的产品数量
     * @param systemIds 系统ID列表
     * @return 系统产品关联数量列表
     */
    List<SystemProductAssociationCountDTO> querySystemProductAssociationCount(@Param("systemIds") List<String> systemIds);
}
