package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-5-21 15:41
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QosUserPO {

    private String userId;

    private String msisdn;

    private String imsi;

    private Integer homeProvince;

    private Integer userType;

    private Integer signNetworkType;

    private Integer openStatus;

    private Date openTime;

    private Date closeTime;

    private Date updateTime;

    private Date createTime;

    private Integer qosLevel5g;

    private Integer qosLevel4g;
}
