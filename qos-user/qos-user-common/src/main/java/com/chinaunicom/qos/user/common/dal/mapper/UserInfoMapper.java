package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.QosUserPO;


/**
 * <AUTHOR>
 * @date 2025-8-12 09:45
 */
public interface UserInfoMapper {

    int insertUser(QosUserPO qosUser);

    QosUserPO selectByUserId(String userId);

    /**
     * 根据用户ID更新用户信息
     * @param qosUserInfo 用户信息
     * @return 更新的记录数
     */
    int updateByUserId(QosUserPO qosUserInfo);

}
