package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.PccUserInfoPO;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class UserInfoRowMapper implements RowMapper<PccUserInfoPO> {
    @Override
    public PccUserInfoPO mapRow(@NotNull ResultSet rs, int rowNum) throws SQLException {
        return PccUserInfoPO.builder().userId(rs.getString("user_id"))
                .msisdn(rs.getString("msisdn"))
                .imsi(rs.getString("imsi"))
                .provinceCode(rs.getInt("province_code"))
                .netType(rs.getInt("net_type"))
                .networkType(rs.getInt("net_type"))
                .operStatus(rs.getInt("oper_status"))
                .netOperTime(rs.getTimestamp("net_oper_time"))
                .createTime(rs.getTimestamp("create_time"))
                .updateTime(rs.getTimestamp("modify_time"))
                .build();
    }
}
