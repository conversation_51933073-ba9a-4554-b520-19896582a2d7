package com.chinaunicom.qos.user.common.dal.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* 
* 
* <AUTHOR>
* @date 2024/12/9 17:03
*/

/**
 * 非qos用户表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserNonqosUserinfoPO {
    /**
    * 用户id，物网imsi，人网msisdn
    */
    private String userId;

    /**
    * 手机号
    */
    private String msisdn;

    /**
    * 物网imsi
    */
    private String imsi;

    /**
    * 数据状态，0启用，-1删除
    */
    private Integer status;

    /**
    * ICCID
    */
    private String iccid;

    /**
    * 数据来源  CBSS-CBSS，non_qos_iot-物网非qos
    */
    private String source;

    /**
    * 0-4G|1-5G|2-4&5G
    */
    private Integer netType;

    /**
    * 0-人网|1-物网
    */
    private Integer networkType;

    /**
    * OTT平台开户,CBSS开户
    */
    private String operSource;

    /**
    * cb开户时间或者ott开户时间
    */
    private LocalDateTime platOperTime;

    /**
    * 0已开户,1延迟开户,-1销户
    */
    private Integer operStatus;

    /**
    * 0开户,-1销户
    */
    private Integer netOperStatus;

    /**
    * 网络开户时间
    */
    private LocalDateTime netOperTime;

    /**
    * 归属省
    */
    private String provinceCode;

    private Integer ifTrans;

    /**
    * 最后调用时间
    */
    private LocalDateTime transLasttime;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 序列码
    */
    private String serialNumber;

    /**
    * 老手机号
    */
    private String oldmsisdn;

    private String custClassType;

    private String chargeType;

    private String billCycleDate;

    private String version;
}