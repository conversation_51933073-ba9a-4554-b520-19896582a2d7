package com.chinaunicom.qos.user.common.dal.repo;

import com.chinaunicom.qos.common.utils.JsonUtil;
import com.chinaunicom.qos.user.common.dal.IUserInfoRepo;
import com.chinaunicom.qos.user.common.dal.mapper.QosUserMapper;
import com.chinaunicom.qos.user.common.dal.mapper.UserInfoMapper;
import com.chinaunicom.qos.user.common.dal.po.QosUserPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-8-12 09:40
 */
@Slf4j
@Repository
public class UserInfoRepo implements IUserInfoRepo {

    @Resource
    private QosUserMapper qosUserMapper;

    @Resource
    private UserInfoMapper userInfoMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUser(QosUserPO qosUser) {
        qosUserMapper.insertQosUser(qosUser);
        userInfoMapper.insertUser(qosUser);
    }

    @Override
    public QosUserPO selectByUserId(String userId) {
        return qosUserMapper.selectByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByUserId(QosUserPO qosUserInfo) {
        qosUserMapper.updateByUserId(qosUserInfo);
        if (userInfoMapper.updateByUserId(qosUserInfo) == 0) {
            log.info("user_info表不存在记录，从qos_user_info表中读取记录插入user_info");
            var qosUser = qosUserMapper.selectByUserId(qosUserInfo.getUserId());
            if (qosUser != null) {
                userInfoMapper.insertUser(qosUser);
                log.info("user_info表插入成功 {}", JsonUtil.obj2String(qosUser));
            }
        }
    }
}
