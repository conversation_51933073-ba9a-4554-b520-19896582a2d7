package com.chinaunicom.qos.user.common.dal.mapper;

import com.chinaunicom.qos.user.common.dal.po.ProductCsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCsMapper {
    /**
     * 根据产品ID列表批量查询通信服务
     * @param productIds 产品ID列表
     * @return 通信服务列表
     */
    List<ProductCsPO> listByProductIds(@Param("productIds")List<Integer> productIds);

    /**
     * 插入产品通信服务关联信息
     * @param productCmServiceRef 产品通信服务关联信息
     * @return 影响行数
     */
    int insert( ProductCsPO productCmServiceRef);

    /**
     * 根据通信服务ID和产品ID列表查询关联
     * @param cmServiceId 通信服务ID
     * @param productIds 产品ID列表
     * @return 通信服务关联列表
     */
    List<ProductCsPO> listByCmServiceIdAndProductIds(@Param("cmServiceId")String cmServiceId, @Param("productIds")List<Integer> productIds);
}
