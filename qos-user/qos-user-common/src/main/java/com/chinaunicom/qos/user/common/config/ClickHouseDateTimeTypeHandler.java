package com.chinaunicom.qos.user.common.config;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/19 17:03
 */
public class ClickHouseDateTimeTypeHandler implements TypeHandler<Date> {
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    @Override
    public void setParameter(PreparedStatement ps, int i, Date parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            ps.setString(i, sdf.format(parameter));
        } else {
            ps.setNull(i, Types.VARCHAR);
        }
    }

    @Override
    public Date getResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        if (value != null) {
            try {
                return sdf.parse(value);
            } catch (ParseException e) {
                throw new SQLException(e);
            }
        }
        return null;
    }

    @Override
    public Date getResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        if (value != null) {
            try {
                return sdf.parse(value);
            } catch (ParseException e) {
                throw new SQLException(e);
            }
        }
        return null;
    }

    @Override
    public Date getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        if (value != null) {
            try {
                return sdf.parse(value);
            } catch (ParseException e) {
                throw new SQLException(e);
            }
        }
        return null;
    }
}