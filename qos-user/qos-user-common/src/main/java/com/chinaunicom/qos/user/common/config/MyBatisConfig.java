package com.chinaunicom.qos.user.common.config;

import org.apache.ibatis.type.TypeHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/19 16:59
 */
@Configuration
@MapperScan("com.chinaunicom.qos.user.common.dal.mapper")
public class MyBatisConfig {
    @Bean
    public TypeHandler<Date> clickHouseDateTimeTypeHandler() {
        return new ClickHouseDateTimeTypeHandler();
    }



}