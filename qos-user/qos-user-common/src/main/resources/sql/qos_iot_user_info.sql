CREATE TABLE user.`qos_iot_user_info`
(
    `user_id`     varchar(20) NOT NULL COMMENT '用户ID，值为物网用户IMSI',
    `msisdn`      varchar(20)          DEFAULT NULL COMMENT '物网用户号码',
    `imsi`        varchar(20) NOT NULL COMMENT '物网用户IMSI',
    `open_status` int(11)              DEFAULT NULL COMMENT '0已开户,-1销户',
    `open_time`   datetime             DEFAULT NULL COMMENT '开户时间',
    `close_time`  datetime             DEFAULT NULL COMMENT '销户时间',
    `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time` datetime(3)          DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='物网用户信息表'