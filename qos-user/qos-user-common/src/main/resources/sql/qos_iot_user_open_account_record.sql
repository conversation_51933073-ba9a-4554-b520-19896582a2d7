CREATE TABLE qos_user.qos_iot_user_open_account_record
(
    `id` Int64 COMMENT '主键 13位时间戳+类型2位+3位随机数',
    `message_id` String COMMENT '消息串Id',
    `oper_source` String COMMENT '请求来源',
    `user_id` String COMMENT '用户id，值为imsi',
    `msisdn` String COMMENT '物网用户号码',
    `imsi` String COMMENT '物网imis信息',
    `oper_type` Int8 COMMENT '操作类型 0-开户 1-销户',
    `order_time` DateTime64(3) COMMENT '请求时间',
    `resp_code` Int32 COMMENT '响应码-归并前',
    `resp_desc` String COMMENT '响应描述-归并前',
    `create_time` DateTime64(3) COMMENT '创建时间',
    `trace_id` String COMMENT '链路追踪ID',
    `reserved_column1` String COMMENT '备用字段1',
    `reserved_column2` String COMMENT '备用字段2',
    `reserved_column3` String COMMENT '备用字段3',
    `reserved_column4` String COMMENT '备用字段4',
    `reserved_column5` String COMMENT '备用字段5',
    `reserved_column6` String COMMENT '备用字段6',
    `reserved_column7` String COMMENT '备用字段7',
    `reserved_column8` String COMMENT '备用字段8',
    `reserved_column9` String COMMENT '备用字段9',
    `reserved_column10` String COMMENT '备用字段10'
) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_iot_user_open_account_record','{replica}') PARTITION BY toYYYYMM(order_time)
    PRIMARY KEY (user_id)
    ORDER BY (user_id,order_time)


CREATE TABLE qos_user.qos_iot_user_open_account_record_all ON
    CLUSTER 'ck-qos' AS qos_user.qos_iot_user_open_account_record ENGINE = Distributed(
        'ck-qos',
        'qos_user',
        'qos_iot_user_open_account_record',
        intHash64(toYYYYMM(order_time))
    )