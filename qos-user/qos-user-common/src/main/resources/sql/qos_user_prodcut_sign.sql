CREATE TABLE qos_user_product_sign_0 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_1 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_2 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_3 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_4 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_5 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_6 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_7 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_8 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_9 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_10 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_11 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_12 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_13 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_14 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_15 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_16 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_17 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_18 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_19 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_20 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_21 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_22 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_23 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_24 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_25 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_26 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_27 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_28 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_29 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_30 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_31 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_32 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_33 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_34 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_35 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_36 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_37 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_38 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_39 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_40 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_41 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_42 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_43 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_44 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_45 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_46 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_47 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_48 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_49 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_50 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_51 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_52 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_53 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_54 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign55 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_56 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_57 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_58 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_59 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_60 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_61 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_62 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'

CREATE TABLE qos_user_product_sign_63 (
  user_id varchar(36) NOT NULL COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  effective_time datetime NOT NULL COMMENT '生效时间',
  expire_time datetime NOT NULL COMMENT '失效时间',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE
  PRIMARY KEY (user_id,qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品签约表'