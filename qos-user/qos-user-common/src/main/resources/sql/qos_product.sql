CREATE TABLE qos_product (
  qos_product_id int(11) NOT NULL AUTO_INCREMENT COMMENT 'qos产品id',
  biz_product_id varchar(100) NOT NULL COMMENT '业务产品id',
  qos_product_name varchar(100) DEFAULT '' COMMENT 'qos产品名称',
  status tinyint NOT NULL COMMENT '产品状态 0-可用  1-不可用',
  -- product_type tinyint NOT NULL COMMENT '产品类型 0-商用 1-试验',
  -- bearer_mode tinyint NOT NULL COMMENT '网络承载类型 0-专载 1-默载',
  -- speed_type tinyint NOT NULL COMMENT '速率类型 0-加速 1-限速',
  system_code varchar(50) NOT NULL COMMENT '创建系统编码',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  update_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (qos_product_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT=880000 COMMENT='qos产品表';


--20241119
ALTER TABLE qos_product
ADD qos_product_desc varchar(255) DEFAULT NULL COMMENT 'QoS产品描述'
AFTER system_code;