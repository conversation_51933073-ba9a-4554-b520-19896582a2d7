CREATE TABLE qos_product_cmservice_ref (
  qos_product_id int(11) NOT NULL COMMENT 'qos产品id',
  cm_service_id varchar(50) NOT NULL COMMENT '通信服务id',
  cm_service_flag tinyint NOT NULL COMMENT '通信服务类型 0-商用 1-试验',
  create_time datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (qos_product_id, cm_service_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品和通信服务关联表'