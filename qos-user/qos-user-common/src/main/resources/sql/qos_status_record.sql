CREATE TABLE qos_user.qos_status_record ON CLUSTER 'ck-qos' (
    data_id String COMMENT '作为用户信息传递的唯一标志（可供IoM查询），最大长度48位',
    msisdn String COMMENT '11位用户手机号码',
    prov_code Int32  COMMENT '2位cb省份编码',
    city_code Int32  COMMENT '3位cb市编码',
    update_time DateTime64(3)  COMMENT '用户变更状态时间',
    create_time DateTime64(3)  COMMENT 'IoM入表记录时间',
    record_time DateTime64(3)  COMMENT '入库时间',
    online_tag Int8 COMMENT '在网标识（0 未在网 ,1 在网）',
    ngc_qos_tpl_id String COMMENT '5GC QOS模板ID',
    apn_qos_tpl_Id String COMMENT 'EPS QOS模板ID',
    reserved_column1 String COMMENT '备用字段1',
    reserved_column2 String COMMENT '备用字段2',
    reserved_column3 String COMMENT '备用字段3',
    reserved_column4 String COMMENT '备用字段4',
    reserved_column5 String COMMENT '备用字段5',
    reserved_column6 String COMMENT '备用字段6',
    reserved_column7 String COMMENT '备用字段7',
    reserved_column8 String COMMENT '备用字段8',
    reserved_column9 String COMMENT '备用字段9',
    reserved_column10 String COMMENT '备用字段10'
    ) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_status_record','{replica}')
    PARTITION BY toYYYYMM(record_time)
    PRIMARY KEY (msisdn)
    ORDER BY (msisdn, record_time, create_time,ngc_qos_tpl_id,apn_qos_tpl_Id)
    TTL toStartOfDay(record_time) + INTERVAL 6 MONTH DELETE;

CREATE TABLE qos_user.qos_status_record_all ON CLUSTER 'ck-qos' AS qos_user.qos_status_record ENGINE = Distributed('ck-qos',
                                                                                                                   'qos_user',
                                                                                                                   'qos_status_record',
                                                                                                                   intHash64(toYYYYMM(record_time)))
