CREATE TABLE qos_product_system_ref
(
    qos_product_id    int(11) NOT NULL COMMENT 'qos产品id',
    system_id     varchar(16) NOT NULL COMMENT '使用系统id',
    create_time       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    update_time       datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '修改时间',
    PRIMARY KEY (qos_product_id, system_id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品系统关联表';