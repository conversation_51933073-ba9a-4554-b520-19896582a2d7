CREATE TABLE pcc_slicepro.qos_user_policy_sync_data_record_local on cluster 'ck-share-pod1'
(
    sync_source     String COMMENT '同步来源',
    msisdn          String COMMENT '手机号码',
    oldmsisdn       String COMMENT '旧手机号码',
    user_imsi       String COMMENT 'IMSI',
    policy_id       String COMMENT '策略编号',
    old_policy_id   String COMMENT '被替换的策略编号',
    qos_level       String COMMENT '业务Qos级别',
    start_time      String COMMENT '生效时间 格式：YYYYMMDDHH24MISS',
    end_time        String COMMENT '失效时间 格式：YYYYMMDDHH24MISS',
    province_code   String COMMENT '省分编码',
    usr_status      Int8 COMMENT '用于4升5策略迁移时判断，UDM用户不做优化：0：2/3/4G用户 1：5G SA用户 HSS用户不携带',
    operate_time    String COMMENT 'BSS操作时间 格式：YYYYMMDDHH24MISS',
    cust_class_type String COMMENT '客户级别编码，统一数据模型编码CUI_000015。当前场景不需要该参数，PCC不处理',
    charge_type     String COMMENT '计费方式0：离线计费，1：在线计费',
    bill_cycle_date String COMMENT '账期切换日格式：YYYYMMDD',
    version         String COMMENT '版本号（该接口协议版本号，此处为’01’）',
    oper_type       Int8 COMMENT '同步类型 0-新增用户 1-修改用户 2-删除用户 3-新增策略 4-修改策略 5-删除策略',
    approval_status String COMMENT '审批状态 0000-下发 0001-不下发',
    resp_code       String COMMENT '下发网元应答编码',
    resp_desc       String COMMENT '应答描述，如审批状态为1，代表不下发的描述',
    create_time     DateTime64(3) COMMENT '创建时间'
)
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_user_policy_sync_data_record_local', '{replica}')
        PARTITION BY toYYYYMM(create_time)
        PRIMARY KEY (msisdn, policy_id)
        ORDER BY (msisdn, policy_id, oper_type, operate_time, province_code, start_time, end_time, resp_code);

CREATE TABLE pcc_slicepro.qos_user_policy_sync_data_record_all on cluster 'ck-share-pod1' AS pcc_slicepro.qos_user_policy_sync_data_record_local ENGINE = Distributed('ck-share-pod1',
                                                                                                                                                                      'pcc_slicepro',
                                                                                                                                                                      'qos_user_policy_sync_data_record_local',
                                                                                                                                                                      intHash64(toYYYYMM(create_time)));