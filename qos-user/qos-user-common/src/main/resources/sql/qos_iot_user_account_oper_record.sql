CREATE TABLE qos_user.qos_iot_user_account_oper_record ON CLUSTER 'ck-qos' (
    id Int64 COMMENT '主键 13位时间戳+类型2位+3位随机数',
    message_id String COMMENT '消息串Id',
    oper_source String COMMENT '请求来源',
    user_id String  COMMENT '用户id',
    msisdn String COMMENT '用户手机号码',
    imsi String COMMENT 'imis信息',
    user_type Int8  COMMENT '用户类型 0人网 1物网',
    sign_network_type Int8 COMMENT '用户签约的网络类型 0-4G 1-5G',
    home_province Int32 COMMENT '归属省',
    oper_type Int8  COMMENT '操作类型 0-开户',
    order_time DateTime64(3) COMMENT '请求时间',
    resp_code Int32  COMMENT '响应码-归并前',
    resp_desc String  COMMENT '响应描述-归并前',
    create_time DateTime64(3)  COMMENT '创建时间',
    reserved_column1 String COMMENT '备用字段1',
    reserved_column2 String COMMENT '备用字段2',
    reserved_column3 String COMMENT '备用字段3',
    reserved_column4 String COMMENT '备用字段4',
    reserved_column5 String COMMENT '备用字段5',
    reserved_column6 String COMMENT '备用字段6',
    reserved_column7 String COMMENT '备用字段7',
    reserved_column8 String COMMENT '备用字段8',
    reserved_column9 String COMMENT '备用字段9',
    reserved_column10 String COMMENT '备用字段10'
    ) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_iot_user_account_oper_record','{replica}')
    PARTITION BY toYYYYMM(order_time)
    PRIMARY KEY (user_id)
    ORDER BY (user_id,order_time)


CREATE TABLE qos_user.qos_iot_user_account_oper_record_all ON
    CLUSTER 'ck-qos' AS qos_user.qos_iot_user_account_oper_record ENGINE = Distributed('ck-qos',
                                                                                   'qos_user',
                                                                                   'qos_iot_user_account_oper_record',
                                                                                   intHash64(toYYYYMM(order_time)))