CREATE TABLE pcc_slicepro.qos_user_product_sign_record_local ON CLUSTER 'ck-share-pod1'(
  tid Int64 COMMENT '主键',
  message_id String COMMENT '消息串号',
  oper_type Int8 COMMENT '操作类型 0-签约 1-解约',
  user_id String COMMENT '用户ID 人网为msisdn 物网为imsi',
  qos_product_id Int32 COMMENT 'qos产品id',
  effective_time DateTime64 COMMENT '生效时间',
  expire_time DateTime64 COMMENT '失效时间',
  resp_code Int32 COMMENT '返回码',
  resp_desc String COMMENT '返回描述',
  create_time DateTime64 DEFAULT toDateTime(now()) COMMENT '创建时间',
  reserved_column1 String COMMENT '备用字段1',
  reserved_column2 String COMMENT '备用字段2',
  reserved_column3 String COMMENT '备用字段3',
  reserved_column4 String COMMENT '备用字段4',
  reserved_column5 String COMMENT '备用字段5',
  reserved_column6 String COMMENT '备用字段6',
  reserved_column7 String COMMENT '备用字段7',
  reserved_column8 String COMMENT '备用字段8',
  reserved_column9 String COMMENT '备用字段9',
  reserved_column10 String COMMENT '备用字段10'
) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/qos_user_product_sign_record_local','{replica}')
PARTITION BY toYYYYMM(create_time)
PRIMARY KEY (tid)
ORDER BY (tid,user_id,qos_product_id,create_time,oper_type,resp_code);

CREATE TABLE qos_user_product_sign_record on cluster ENGINE = Distributed('',
 '',
 'qos_user_product_sign_record_local',
 intHash64(toYYYYMM(create_time)))