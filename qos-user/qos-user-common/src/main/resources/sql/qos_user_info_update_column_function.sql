CREATE DEFINER=`slice_dynamic`@`%` PROCEDURE `AddColumn`()
BEGIN
	#Routine body goes here...
	DECLARE v_counter INT DEFAULT 0;
  DECLARE v_max_tables INT DEFAULT 32;

  WHILE v_counter < v_max_tables DO
		SET @table_name = CONCAT('qos_user_info_', v_counter);

-- 		SET @stmt = CONCAT('ALTER TABLE ', @table_name, ' ADD COLUMN qos_level_5g int(11) DEFAULT NULL COMMENT "5G等级", ADD COLUMN qos_level_4g  int(11) DEFAULT NULL COMMENT "4G等级";');
		SET @stmt = CONCAT('ALTER TABLE ', @table_name, ' MODIFY `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT "更新时间";');

    PREPARE stmt FROM @stmt;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET v_counter = v_counter + 1;
  END WHILE;
END