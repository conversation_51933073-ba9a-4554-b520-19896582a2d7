CREATE TABLE `qos_user_info_0` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_1` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_2` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_3` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_4` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_5` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_6` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_7` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_8` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_9` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_10` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_11` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_12` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_13` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_14` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_15` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_16` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_17` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_18` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_19` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_20` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_21` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_22` (
                                    `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                    `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                    `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                    `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                    `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                    `home_province` int(11) NOT NULL COMMENT '归属省',
                                    `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                    `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                    `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                    `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                    `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_23` (
                                    `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                    `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                    `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                    `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                    `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                    `home_province` int(11) NOT NULL COMMENT '归属省',
                                    `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                    `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                    `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                    `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                    `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';



CREATE TABLE `qos_user_info_24` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_25` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_26` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_27` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';


CREATE TABLE `qos_user_info_28` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_29` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_30` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

CREATE TABLE `qos_user_info_31` (
                                   `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
                                   `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
                                   `user_id` varchar(20) NOT NULL COMMENT '用户ID',
                                   `msisdn` varchar(20) NOT NULL COMMENT '用户手机号码，不带86',
                                   `imsi` varchar(20) DEFAULT NULL COMMENT '用户IMSI',
                                   `home_province` int(11) NOT NULL COMMENT '归属省',
                                   `user_type` int(11) NOT NULL COMMENT '0-人网|1-物网',
                                   `sign_network_type` int(11) NOT NULL COMMENT '0-4G|1-5G',
                                   `open_status` int(11) DEFAULT NULL COMMENT '0已开户,-1销户',
                                   `open_time` datetime DEFAULT NULL COMMENT '开户时间',
                                   `close_time` datetime DEFAULT NULL COMMENT '销户时间',
                                   PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

