<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.UserProductSignRecordMapper">

    <insert id="insertBatch">
        INSERT INTO qos_user_product_sign_record (tid, message_id, oper_type, user_id, qos_product_id, effective_time,expire_time, resp_code, resp_desc, reserved_column1)VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.tid}, #{record.messageId}, #{record.operType}, #{record.userId}, #{record.qosProductId}, #{record.effectiveTime}, #{record.expireTime}, #{record.respCode}, #{record.respDesc}, #{record.operBy})
        </foreach>
    </insert>
</mapper>