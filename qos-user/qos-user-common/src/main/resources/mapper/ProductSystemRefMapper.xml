<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.ProductSystemRefMapper">

    <select id="selectProductIdAndSystemByProductIds"
            resultType="com.chinaunicom.qos.user.common.dal.po.ProductIdSystemPO">
        SELECT ps.qos_product_id,
        ps.system_id
        FROM qos_product_system_ref ps
        <where>
            ps.qos_product_id IN (
            <foreach collection="productIds" item="id" separator=",">
                #{id}
            </foreach>)
        </where>
    </select>

    <select id="querySystemProductAssociationCount" resultType="com.chinaunicom.qos.user.api.dto.SystemProductAssociationCountDTO">
        SELECT system_id,
        COUNT(*) product_count
        FROM qos_product_system_ref
        WHERE system_id IN(
        <foreach collection="systemIds" item="id" separator=",">
            #{id}
        </foreach>)
        GROUP BY system_id
    </select>

    <insert id="insert" parameterType="com.chinaunicom.qos.user.common.dal.po.ProductSystemRefPO">
        INSERT INTO qos_product_system_ref (
            qos_product_id,
            system_id,
            create_time,
            update_time
        ) VALUES (
            #{qosProductId},
            #{systemId},
            NOW(),
            NOW()
        )
    </insert>
</mapper>