<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosIotUserMapper">

    <resultMap id="BaseResultMap" type="com.chinaunicom.qos.user.common.dal.po.QosIotUserPO">
        <id column="user_id" property="userId"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="open_status" property="openStatus"/>
        <result column="open_time" property="openTime"/>
        <result column="close_time" property="closeTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT * FROM qos_iot_user_info WHERE user_id = #{userId}
    </select>

    <insert id="insert">
        INSERT INTO qos_iot_user_info (
            user_id, msisdn, imsi, open_status, open_time, 
            close_time, create_time, update_time
        ) VALUES (
            #{userId}, #{msisdn}, #{imsi}, #{openStatus}, #{openTime},
            #{closeTime}, NOW(), NOW()
        )
    </insert>

    <update id="updateOpenStatus">
        UPDATE qos_iot_user_info
        SET open_status = #{openStatus},
            <if test="openTime != null">
                open_time = #{openTime},
            </if>
            <if test="closeTime != null">
                close_time = #{closeTime},
            </if>
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>
</mapper> 