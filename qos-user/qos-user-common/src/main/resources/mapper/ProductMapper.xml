<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.ProductMapper">

    <select id="queryProductNameListByKeyword" resultType="java.lang.String">
        SELECT qos_product_name FROM qos_product
        <where>
            <if test="keyword != null and keyword != ''">
                qos_product_name LIKE CONCAT('%', #{keyword},'%')
            </if>
        </where>
    </select>
    <select id="queryProduct" resultType="com.chinaunicom.qos.user.api.dto.ProductWithSystemAndUserCountDTO">
        SELECT p.qos_product_id,
        qos_product_name,
        `status`,
        exist_plat_product,
        qos_product_desc,
        conflict_check_flag,
        order_priority,
        p.create_time,
        p.update_time,
        p.business_ownership
        FROM qos_product p
        <where>
            <if test="qosProductIdList != null and qosProductIdList.size() > 0">
                p.qos_product_id IN (
                <foreach collection="qosProductIdList" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="status != null">
                and `status` = #{status}
            </if>
            <if test="qosProductNameKeyWords != null and qosProductNameKeyWords != ''">
                and qos_product_name LIKE CONCAT('%', #{qosProductNameKeyWords},'%')
            </if>
            <if test="businessOwnershipList != null and businessOwnershipList.size() > 0">
                and p.business_ownership IN (
                <foreach collection="businessOwnershipList" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="createTimeBegin != null">
                and p.create_time > #{createTimeBegin}
            </if>
            <if test="createTimeEnd != null">
                and #{createTimeEnd} > p.create_time
            </if>
        </where>
    </select>
    <select id="getProductIdByCmServiceAndSystem" resultType="java.lang.Integer">
        SELECT DISTINCT s.qos_product_id FROM qos_product_system_ref s
        JOIN qos_product_cmservice_ref c ON s.qos_product_id = c.qos_product_id
        <where>
            <if test="cmServiceIdList != null and cmServiceIdList.size()>0">
                cm_service_id IN(
                <foreach collection="cmServiceIdList" item="id" separator=",">
                    #{id}
                </foreach>)
            </if>
            <if test="systemIdList != null and systemIdList.size() > 0">
                AND system_id IN(
                <foreach collection="systemIdList" item="id" separator=",">
                    #{id}
                </foreach>)
            </if>
        </where>
    </select>
    <select id="queryProductsMatchingAllCmServices"
            resultMap="productResultMap">
        SELECT p.qos_product_id,
        qos_product_name
        FROM qos_product p
        JOIN qos_product_cmservice_ref r
        ON p.qos_product_id = r.qos_product_id
        WHERE r.cm_service_id
        IN (
        <foreach collection="cmServiceIdList" item="id" separator=",">
            #{id}
        </foreach>)
        GROUP BY p.qos_product_id
        HAVING COUNT(DISTINCT r.cm_service_id) = #{size}
    </select>

    <resultMap id="productResultMap" type="com.chinaunicom.qos.user.common.dal.po.ProductPO">
        <id column="qos_product_id" property="qosProductId"/>
        <result column="qos_product_name" property="qosProductName"/>
        <result column="business_ownership" property="businessOwnership"/>
        <result column="status" property="status"/>
        <result column="system_code" property="systemCode"/>
        <result column="qos_product_desc" property="qosProductDesc"/>
        <result column="conflict_check_flag" property="conflictCheckFlag"/>
        <result column="order_priority" property="orderPriority" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectById" resultMap="productResultMap">
        SELECT
            qos_product_id,
            qos_product_name,
            business_ownership,
            status,
            system_code,
            qos_product_desc,
            conflict_check_flag,
            order_priority,
            create_time,
            update_time
        FROM qos_product
        WHERE qos_product_id = #{productId}
    </select>

    <select id="listByProductIdsAndName" resultMap="productResultMap">
        SELECT
            qos_product_id,
            qos_product_name,
            business_ownership,
            status,
            system_code,
            qos_product_desc,
            conflict_check_flag,
            create_time,
            update_time
        FROM qos_product
        WHERE qos_product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="qosProductNameKeyWords != null and qosProductNameKeyWords != ''">
            AND qos_product_name LIKE CONCAT('%',#{qosProductNameKeyWords}, '%')
        </if>
    </select>

    <insert id="insert" parameterType="com.chinaunicom.qos.user.common.dal.po.ProductPO"
            useGeneratedKeys="true" keyProperty="qosProductId">
        INSERT INTO qos_product (
            qos_product_name,
            business_ownership,
            exist_plat_product,
            status,
            system_code,
            qos_product_desc,
            conflict_check_flag,
            create_time,
            update_time
        ) VALUES (
            #{qosProductName},
            #{businessOwnership},
            #{existPlatProduct},
            #{status},
            #{systemCode},
            #{qosProductDesc},
            #{conflictCheckFlag},
            NOW(),
            NOW()
        )
    </insert>

    <update id="updateById" parameterType="com.chinaunicom.qos.user.common.dal.po.ProductPO">
        UPDATE qos_product
        <set>
            <if test="qosProductName != null">qos_product_name = #{qosProductName},</if>
            <if test="businessOwnership != null">business_ownership = #{businessOwnership},</if>
            <if test="status != null">status = #{status},</if>
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="qosProductDesc != null">qos_product_desc = #{qosProductDesc},</if>
            <if test="conflictCheckFlag != null">conflict_check_flag = #{conflictCheckFlag},</if>
            update_time = NOW()
        </set>
        WHERE qos_product_id = #{qosProductId}
    </update>

    <select id="queryProductOrderPriorities" resultMap="productResultMap">
        select
            qos_product_id,
            qos_product_name,
            order_priority
        from
            qos_product
        <where>
            <if test="productIds != null and productIds.size() != 0">
                qos_product_id in (
                <foreach collection="productIds" item="id" separator=",">
                     #{id}
                </foreach>
               )
            </if>
            <if test="hasOrderPriorityConfig == 0">
                and order_priority is not null
            </if>
        </where>
        order by order_priority
    </select>

    <update id="updateOrderPriority">
        update
            qos_product
        set
            order_priority = #{orderPriority,jdbcType=DECIMAL}
        where
            qos_product_id = #{qosProductId}
    </update>
</mapper>