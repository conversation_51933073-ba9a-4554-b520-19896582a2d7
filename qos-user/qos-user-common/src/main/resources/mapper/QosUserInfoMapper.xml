<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosUserInfoMapper">

  <select id="selectCountByUserId" resultType="java.lang.Integer">
    <!--@mbg.generated-->
    select 
    count(user_id)
    from qos_user_info
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
</mapper>