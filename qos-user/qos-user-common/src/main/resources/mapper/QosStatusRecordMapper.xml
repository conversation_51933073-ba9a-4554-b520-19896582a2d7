<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosStatusRecordMapper">

    <insert id="insertRecordList" parameterType="com.chinaunicom.qos.user.api.request.QosStatusReq">
        INSERT INTO qos_status_record (data_id, msisdn, prov_code, city_code, online_tag,
        ngc_qos_tpl_id, apn_qos_tpl_Id, update_time, create_time, record_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dataId}, #{item.msisdn}, #{item.provCode}, #{item.cityCode}, #{item.onlineTag},
            #{item.ngcQosTplId}, #{item.apnQosTplId},
            toDateTime64(#{item.updateTime,typeHandler=com.chinaunicom.qos.user.common.config.ClickHouseDateTimeTypeHandler}, 3),
            toDateTime64(#{item.createTime,typeHandler=com.chinaunicom.qos.user.common.config.ClickHouseDateTimeTypeHandler}, 3),
            now64()
            )
        </foreach>
    </insert>


</mapper>