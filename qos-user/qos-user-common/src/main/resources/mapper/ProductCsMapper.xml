<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.ProductCsMapper">

    <resultMap id="productCsResultMap" type="com.chinaunicom.qos.user.common.dal.po.ProductCsPO">
        <result column="qos_product_id" property="qosProductId"/>
        <result column="cm_service_id" property="cmServiceId"/>
        <result column="cm_service_flag" property="cmServiceFlag"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="listByProductIds" resultMap="productCsResultMap">
        SELECT
        qos_product_id,
        cm_service_id,
        cm_service_flag,
        create_time
        FROM qos_product_cmservice_ref
        WHERE qos_product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.chinaunicom.qos.user.common.dal.po.ProductCsPO">
        INSERT INTO qos_product_cmservice_ref (
            qos_product_id,
            cm_service_id,
            cm_service_flag,
            create_time
        ) VALUES (
            #{qosProductId},
            #{cmServiceId},
            #{cmServiceFlag},
            NOW()
        )
    </insert>

    <select id="listByCmServiceIdAndProductIds" resultMap="productCsResultMap">
        SELECT
            qos_product_id,
            cm_service_id,
            cm_service_flag,
            create_time
        FROM qos_product_cmservice_ref
        WHERE cm_service_id = #{cmServiceId}
        AND qos_product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
</mapper>