<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosUserPolicySyncDataRecordMapper">
    <insert id="batchInsert">
        <!--@mbg.generated-->
        INSERT INTO qos_user_policy_sync_data_record_local (
        sync_source,
        msisdn,
        oldmsisdn,
        user_imsi,
        policy_id,
        old_policy_id,
        qos_level,
        start_time,
        end_time,
        province_code,
        usr_status,
        operate_time,
        cust_class_type,
        charge_type,
        bill_cycle_date,
        version,
        oper_type,
        approval_status,
        resp_code,
        resp_desc,
        create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.syncSource},
             #{item.msisdn},
             #{item.oldmsisdn},
             #{item.userImsi},
             #{item.policyId},
             #{item.oldPolicyId},
             #{item.qosLevel},
             #{item.startTime},
             #{item.endTime},
             #{item.provinceCode},
             #{item.usrStatus},
             #{item.operateTime},
             #{item.custClassType},
             #{item.chargeType},
             #{item.billCycleDate},
             #{item.version},
             #{item.operType},
             #{item.approvalStatus},
             #{item.respCode},
             #{item.respDesc},
             now64())
        </foreach>
    </insert>
</mapper>