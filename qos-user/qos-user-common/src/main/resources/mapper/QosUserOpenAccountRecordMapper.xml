<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosUserOpenAccountRecordMapper">
    <insert id="insertBatch" >
        INSERT INTO qos_user_open_account_record
        (
        id,
        message_id,
        oper_source,
        user_id,
        msisdn,
        imsi,
        user_type,
        sign_network_type,
        home_province,
        oper_type,
        order_time,
        resp_code,
        resp_desc,
        create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.messageId},
            #{item.operSource},
            #{item.userId},
            #{item.msisdn},
            #{item.imsi},
            #{item.userType},
            #{item.signNetworkType},
            #{item.homeProvince},
            #{item.operType},
            #{item.orderTime},
            #{item.respCode},
            #{item.respDesc},
            now()
            )
        </foreach>
    </insert>

</mapper>
