<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.UserOpenAccountRecordMapper">

    <resultMap id="UserOpenAccountRecordResult" type="com.chinaunicom.qos.user.api.dto.UserOpenAccountRecordDTO">
        <result column="message_id" property="messageId"/>
        <result column="oper_source" property="operSource"/>
        <result column="user_id" property="userId"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="user_type" property="userType"/>
        <result column="oper_source" property="operSource"/>
        <result column="sign_network_type" property="signNetworkType"/>
        <result column="home_province" property="homeProvince"/>
        <result column="oper_type" property="operType"/>
        <result column="order_time" property="orderTime"/>
        <result column="resp_code" property="respCode"/>
        <result column="resp_desc" property="respDesc"/>
    </resultMap>


    <select id="queryRecordByUserId" resultMap="UserOpenAccountRecordResult">
        select message_id,
               oper_source,
               user_id,
               msisdn,
               imsi,
               user_type,
               sign_network_type,
               home_province,
               oper_type,
               order_time,
               resp_code,
               resp_desc
        from qos_user_open_account_record where user_id = #{userId}
        order by order_time desc
    </select>


</mapper>