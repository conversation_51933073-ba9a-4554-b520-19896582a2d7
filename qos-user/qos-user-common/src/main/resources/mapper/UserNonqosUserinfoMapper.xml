<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.UserNonqosUserinfoMapper">
  <resultMap id="BaseResultMap" type="com.chinaunicom.qos.user.common.dal.po.UserNonqosUserinfoPO">
    <!--@mbg.generated-->
    <!--@Table user_nonqos_userinfo_0-->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="imsi" jdbcType="VARCHAR" property="imsi" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ICCID" jdbcType="VARCHAR" property="iccid" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="net_type" jdbcType="INTEGER" property="netType" />
    <result column="network_type" jdbcType="INTEGER" property="networkType" />
    <result column="oper_source" jdbcType="VARCHAR" property="operSource" />
    <result column="plat_oper_time" jdbcType="TIMESTAMP" property="platOperTime" />
    <result column="oper_status" jdbcType="INTEGER" property="operStatus" />
    <result column="net_oper_status" jdbcType="INTEGER" property="netOperStatus" />
    <result column="net_oper_time" jdbcType="TIMESTAMP" property="netOperTime" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="if_trans" jdbcType="INTEGER" property="ifTrans" />
    <result column="trans_lasttime" jdbcType="TIMESTAMP" property="transLasttime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="oldmsisdn" jdbcType="VARCHAR" property="oldmsisdn" />
    <result column="cust_class_type" jdbcType="VARCHAR" property="custClassType" />
    <result column="charge_type" jdbcType="VARCHAR" property="chargeType" />
    <result column="bill_cycle_date" jdbcType="VARCHAR" property="billCycleDate" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    user_id, msisdn, imsi, `status`, ICCID, `source`, net_type, network_type, oper_source, 
    plat_oper_time, oper_status, net_oper_status, net_oper_time, province_code, if_trans, 
    trans_lasttime, create_time, update_time, serial_number, oldmsisdn, cust_class_type, 
    charge_type, bill_cycle_date, version
  </sql>
  <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from user_nonqos_userinfo
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
</mapper>