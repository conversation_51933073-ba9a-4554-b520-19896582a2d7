<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosProductCbProductRelationMapper">

    <!-- 插入QOS产品与CB产品关联关系 -->
    <insert id="batchInsertQosProductCbProductRelation" parameterType="java.util.List">
        INSERT INTO qos_product_cb_product_ref (cb_product_id, qos_product_id, cb_product_name)
        VALUES
        <foreach collection="addList" item="item" separator=",">
            (#{item.cbProductId}, #{item.qosProductId}, #{item.cbProductName})
        </foreach>
    </insert>

    <!-- 查询QOS产品与CB产品关联关系 -->
    <select id="selectByCbProductIdOrQosProductId" parameterType="int" resultType="com.chinaunicom.qos.user.common.dal.po.QosProductCbProductRelationPO">
        SELECT cb_product_id,
               qos_product_id,
               cb_product_name
        FROM qos_product_cb_product_ref
        <where>
            <if test="cbProductId != null">
                AND cb_product_id = #{cbProductId}
            </if>
            <if test="qosProductId != null">
                AND qos_product_id = #{qosProductId}
            </if>
        </where>
    </select>

</mapper>