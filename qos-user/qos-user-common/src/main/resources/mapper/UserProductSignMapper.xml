<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.UserProductSignMapper">

    <insert id="insertUpdate">
        INSERT INTO qos_user_product_sign(user_id, qos_product_id, effective_time, expire_time)
        VALUES(
        #{userId},
        #{qosProductId},
        #{effectiveTime},
        #{expireTime})
        ON DUPLICATE KEY
        UPDATE
        <trim suffixOverrides=",">
            <if test="effectiveTime != null">
                effective_time = #{effectiveTime},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
        </trim>
    </insert>
    <select id="queryCmServiceProductCount" resultType="com.chinaunicom.qos.user.api.dto.CmServiceProductCountDTO">
        SELECT cm_service_id,
        COUNT(*) AS productCount,
        SUM(`status`) AS disabledProductCount
        FROM qos_product p
        JOIN qos_product_cmservice_ref c
        ON p.qos_product_id = c.qos_product_id
        WHERE cm_service_id IN (
        <foreach collection="list" item="cmServiceId" separator=",">
            #{cmServiceId}
        </foreach>
        )
        GROUP BY cm_service_id
    </select>

    <resultMap id="productSignResultMap" type="com.chinaunicom.qos.user.common.dal.po.UserProductSignPO">
        <result column="user_id" property="userId"/>
        <result column="qos_product_id" property="qosProductId"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectByProductIdAndUserId" resultMap="productSignResultMap">
        SELECT
            user_id,
            qos_product_id,
            effective_time,
            expire_time,
            create_time,
            update_time
        FROM qos_user_product_sign
        WHERE qos_product_id = #{qosProductId}
        AND user_id = #{userId}
    </select>

    <select id="listUserProductSigns" resultMap="productSignResultMap">
        SELECT
            user_id,
            qos_product_id,
            effective_time,
            expire_time,
            create_time,
            update_time
        FROM qos_user_product_sign
        WHERE user_id = #{msisdn}
        <if test="excludeProduct != null">
            AND qos_product_id != #{excludeProduct}
        </if>
        <if test="expireTime != null">
            AND effective_time &lt; #{expireTime}
        </if>
        <if test="effectiveTime != null">
            AND expire_time &gt; #{effectiveTime}
        </if>
        AND expire_time &gt; #{currentTime}
    </select>

    <select id="selectValidUserProduct" resultMap="productSignResultMap">
        SELECT
            user_id,
            qos_product_id,
            effective_time,
            expire_time,
            create_time,
            update_time
        FROM qos_user_product_sign
        WHERE user_id = #{msisdn}
        AND qos_product_id = #{qosProductId}
        AND expire_time &gt; #{currentTime}
    </select>

    <update id="updateExpireTime">
        UPDATE qos_user_product_sign
        SET
            expire_time = #{expireTime},
            update_time = NOW()
        WHERE user_id = #{userId}
        AND qos_product_id = #{qosProductId}
    </update>

    <select id="selectSubscribeDetail" resultType="com.chinaunicom.qos.user.common.dal.po.SubscribeDetailPO">
        SELECT 
            ups.qos_product_id,
            ups.effective_time,
            ups.expire_time,
            GROUP_CONCAT(pc.cm_service_id) as cmServiceIds
        FROM qos_user_product_sign ups
        LEFT JOIN qos_product_cmservice_ref pc ON ups.qos_product_id = pc.qos_product_id
        WHERE ups.qos_product_id = #{qosProductId} 
        AND ups.user_id = #{userId}
        GROUP BY ups.qos_product_id, ups.effective_time, ups.expire_time
    </select>
    <select id="selectUserSubscribedProducts"
            resultType="com.chinaunicom.qos.user.common.dal.po.UserProductSignPO">
        SELECT
        user_id,
        qos_product_id,
        effective_time,
        expire_time,
        create_time,
        update_time
        FROM qos_user_product_sign
        WHERE user_id = #{userId}
        <if test="qosProductId != null">
            AND qos_product_id = #{qosProductId}
        </if>
        <if test="expireStatus != null">
            <if test="expireStatus == 0">
                AND NOW() > expire_time
            </if>
            <if test="expireStatus != 0">
                AND expire_time > NOW()
            </if>
        </if>
    </select>
</mapper>