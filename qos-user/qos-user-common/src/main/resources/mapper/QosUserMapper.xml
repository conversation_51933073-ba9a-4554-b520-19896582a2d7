<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosUserMapper">

    <insert id="insertQosUser" parameterType="com.chinaunicom.qos.user.common.dal.po.QosUserPO">
        insert ignore into qos_user_info (user_id, msisdn, imsi, home_province, user_type, sign_network_type,
                                          open_status, open_time, close_time,
                                          qos_level_5g, qos_level_4g)
        values (#{userId},
                #{msisdn},
                #{imsi},
                #{homeProvince},
                #{userType},
                #{signNetworkType},
                #{openStatus},
                #{openTime},
                #{closeTime},
                #{qosLevel5g},
                #{qosLevel4g})
    </insert>

    <resultMap id="userResultMap" type="com.chinaunicom.qos.user.common.dal.po.QosUserPO">
        <result column="user_id" property="userId"/>
        <result column="msisdn" property="msisdn"/>
        <result column="imsi" property="imsi"/>
        <result column="home_province" property="homeProvince"/>
        <result column="user_type" property="userType"/>
        <result column="sign_network_type" property="signNetworkType"/>
        <result column="open_status" property="openStatus"/>
        <result column="open_time" property="openTime"/>
        <result column="close_time" property="closeTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="qos_level_5g" property="qosLevel5g"/>
        <result column="qos_level_4g" property="qosLevel4g"/>
    </resultMap>

    <select id="selectByUserId" resultMap="userResultMap">
        SELECT user_id,
               msisdn,
               imsi,
               home_province,
               user_type,
               sign_network_type,
               open_status,
               open_time,
               close_time,
               update_time,
               create_time,
               qos_level_5g,
               qos_level_4g
        FROM qos_user_info
        WHERE user_id = #{userId}
        LIMIT 1
    </select>

    <update id="updateByUserId" parameterType="com.chinaunicom.qos.user.common.dal.po.QosUserPO">
        UPDATE qos_user_info
        <set>
            <if test="msisdn != null">msisdn = #{msisdn},</if>
            <if test="imsi != null">imsi = #{imsi},</if>
            <if test="homeProvince != null">home_province = #{homeProvince},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="signNetworkType != null">sign_network_type = #{signNetworkType},</if>
            <if test="openStatus != null">open_status = #{openStatus},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="closeTime != null">close_time = #{closeTime},</if>
            <if test="qosLevel5g != null">qos_level_5g = #{qosLevel5g},</if>
            <if test="qosLevel4g != null">qos_level_4g = #{qosLevel4g},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        WHERE user_id = #{userId}
    </update>
</mapper>