<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chinaunicom.qos.user.common.dal.mapper.QosIotUserAccountOperRecordMapper">

    <insert id="insertBatch">
        INSERT INTO qos_iot_user_account_oper_record_all (
            id, message_id, oper_source, user_id, msisdn, imsi, 
            oper_type, order_time, resp_code, resp_desc, create_time, trace_id
        ) VALUES 
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.messageId}, #{record.operSource}, 
                #{record.userId}, #{record.msisdn}, #{record.imsi},
                #{record.operType}, #{record.orderTime}, #{record.respCode}, 
                #{record.respDesc}, #{record.createTime}, #{record.traceId}
            )
        </foreach>
    </insert>

</mapper> 