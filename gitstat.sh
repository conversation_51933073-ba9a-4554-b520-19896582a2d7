#!/bin/bash
# 统计代码行数
# 执行前，运行 git fetch 命令，确保 origin/master 和 origin/release 分支代码最新

if [ $# -lt 1 ] || [ $# -gt 2 ]; then
  echo "【ERROR】参数错误，请重新输入: <统计代码分支/commit_id> [<before-day>], eg: origin/release 2024-07-26"
  exit 1
fi

stat_commit=$1
before_day=$2

function cal_before_day() {
    # 获取当前年月日
    current_year=$(date +%Y)
    current_month=$(date +%m)
    current_day=$(date +%d)

    # 移除前导的0（如果有的话）
    current_month="${current_month#0}"

    # 如果在当月26日之前，那么仍在上一个周期，减去一个月份
    # 注意：如果当前月份是1月，需要调整为上一年的12月
    if [ $current_day -lt 26 ]; then
        ((current_month--))
    fi

    if [ $current_month -lt 1 ]; then
        ((current_month += 12))
        ((current_year--))
    fi

    if [ $current_month -lt 10 ]; then
        current_month="0$current_month"
    fi

    echo $current_year-$current_month-26
}

echo -----------------------------------------------------------------------

# 按执行时间查找前一个周期（比如6.26～7.25）
if [ -z "$before_day" ]; then
  before_day=$(cal_before_day)
fi

echo "统计周期: $before_day ～ $(date "+%Y-%m-%d %H:%M:%S")"

# 确定指定日期之前 origin/master 分支最后一次提交的 commit id
prev_commit=$(git log origin/master --max-count=1 --before="$before_day" --pretty=format:"%H")

echo "prev_commit: $prev_commit"
echo "stat_commit: $stat_commit"
echo -----------------------------------------------------------------------

authors=("欧龙" "彭博" "喻星" "易思绍" "杨仕祥")

for name in "${authors[@]}"; do
    echo -en "$name\t";
    git log --author="$name" --pretty=tformat: $prev_commit..$stat_commit --numstat | \
    awk '
    {
        add += $1;
        subs += $2;
        loc += $1 - $2;
    }
    END {
        printf "added lines: %d, removed lines: %d, total lines: %d\n", add, subs, loc;
    }'
done

echo -----------------------------------------------------------------------
git log --pretty=tformat: $prev_commit..$stat_commit --numstat | \
awk '
{
    total += $1 - $2
}
END {
    printf "total lines: %d\n", total;
}'

echo -----------------------------------------------------------------------

# windows环境，退出兼容
# read -p "press enter to end"