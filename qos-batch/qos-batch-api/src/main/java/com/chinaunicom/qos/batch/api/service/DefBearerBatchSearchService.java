package com.chinaunicom.qos.batch.api.service;

import com.chinaunicom.qos.batch.api.dto.DefBearerBatchSearchDTO;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchSearchReq;
import com.chinaunicom.qos.common.response.Resp;

import java.util.List;

/**
 * 默载批量任务查询
 *
 * <AUTHOR>
 * @date 2024/8/21 17:43
 */
public interface DefBearerBatchSearchService {

    Resp<List<DefBearerBatchSearchDTO>> searchTask(DefBearerBatchSearchReq req);

}
