package com.chinaunicom.qos.batch.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 默载批量终止
 * @DateTime: 2024/5/20 下午2:42
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchTerminateReq implements Serializable {
    /**
     * 消息序号
     */
    @NotBlank(message = "messageId不可为空")
    private String messageId;

    /**
     * 任务执行ID
     */
    @NotNull(message = "planId不可为空")
    private Long planId;

    /**
     * 请求来源
     */
    @NotBlank(message = "reqSource不可为空")
    private String reqSource;
}
