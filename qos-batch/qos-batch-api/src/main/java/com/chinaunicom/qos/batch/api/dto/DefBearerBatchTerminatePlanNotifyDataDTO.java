package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 默载批量终止计划结果通知Data
 * @DateTime: 2024/5/20 下午3:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchTerminatePlanNotifyDataDTO implements Serializable {
    /**
     * 终止预计结束时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String terminateEndTime;

}
