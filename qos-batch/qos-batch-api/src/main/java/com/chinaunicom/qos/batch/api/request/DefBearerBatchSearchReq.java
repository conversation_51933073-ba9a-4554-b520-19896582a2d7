package com.chinaunicom.qos.batch.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 默载批量任务查询请求
 *
 * <AUTHOR>
 * @date 2024/8/21 17:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchSearchReq implements Serializable {
    /**
     * 消息序号
     */
    @NotBlank(message = "messageId不可为空")
    private String messageId;

    /**
     * 归属省
     */
    private Integer homeProvince;

    /**
     * 创建开始时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createStartTime;

    /**
     * 创建结束时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createEndTime;
}
