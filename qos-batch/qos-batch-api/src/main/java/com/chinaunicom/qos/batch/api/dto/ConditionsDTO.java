package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 调用条件信息
 * @DateTime: 2024/5/17 上午10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConditionsDTO implements Serializable {
    /**
     * 预置开始调用时间，格式为yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "conditions.expectStartTime不可为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "conditions.expectStartTime格式应为yyyy-MM-dd HH:mm:ss")
    private String expectStartTime;

    /**
     * 手机号归属省编码
     */
    @NotNull(message = "conditions.homeProvince不可为空")
    private Integer homeProvince;

}
