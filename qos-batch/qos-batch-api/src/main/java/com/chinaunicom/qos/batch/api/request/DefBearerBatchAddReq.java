package com.chinaunicom.qos.batch.api.request;

import com.chinaunicom.qos.batch.api.dto.ConditionsDTO;
import com.chinaunicom.qos.batch.api.dto.SubscribeInfoDTO;
import com.chinaunicom.qos.batch.api.service.DefBearerBatchService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * @Author: pengb7
 * @Description: 批量默载申请请求参数
 * @DateTime: 2024/5/17 上午10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchAddReq implements Serializable {
    public static final DateTimeFormatter DATE_TIME_FORMATTER_14 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 消息序号
     */
    @NotBlank(message = "messageId不可为空")
    private String messageId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 通信服务ID
     */
    @NotBlank(message = "cmServiceId不可为空")
    private String cmServiceId;

    /**
     * 申请时长，单位为秒
     */
    @NotNull(message = "duration不可为空",groups = DefBearerBatchService.Add.class)
    private Integer duration;

    /**
     * 订单结束时间，格式为yyyy-MM-dd HH:mm:ss
     */
    @NotNull(message = "orderEndTime不可为空",groups = DefBearerBatchService.AddByOrderEndTime.class)
    private String orderEndTime;

    /**
     * 手机号文件路径
     */
    @NotBlank(message = "msisdnFilePath不可为空")
    private String msisdnFilePath;

    /**
     * 请求来源
     */
    @NotBlank(message = "reqSource不可为空")
    private String reqSource;

    /**
     * 调用条件信息
     */
    @NotNull(message = "conditions不可为空")
    @Valid
    private ConditionsDTO conditions;

    /**
     * 订购开通信息
     */
    @NotNull(message = "subscribeInfo不可为空")
    @Valid
    private SubscribeInfoDTO subscribeInfo;

    @AssertTrue(message = "endTime格式必须是yyyy-MM-dd HH:mm:ss且晚于当前时间",groups = DefBearerBatchService.AddByOrderEndTime.class)
    public boolean isExecuteTimeWithinThreeDays() {
        if(this.orderEndTime ==null){
            return true;
        }
        try {
            var endDateTime = LocalDateTime.parse(this.orderEndTime, DATE_TIME_FORMATTER_14);
            return endDateTime.isAfter(LocalDateTime.now());
        } catch (DateTimeParseException e) {
            return false;
        }
    }

}
