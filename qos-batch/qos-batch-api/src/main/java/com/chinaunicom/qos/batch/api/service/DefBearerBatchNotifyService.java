package com.chinaunicom.qos.batch.api.service;

import com.chinaunicom.qos.batch.api.request.*;
import com.chinaunicom.qos.common.response.Resp;

@SuppressWarnings("java:S3740")
public interface DefBearerBatchNotifyService {

    Resp<Void> addPlanNotify(DefBearerBatchAddPlanNotifyReq req);

    Resp<Void> addResultNotify(DefBearerBatchAddNotifyReq req);

    Resp<Void> terminatePlanNotify(DefBearerBatchTerminatePlanNotifyReq req);

    Resp<Void> terminateResultNotify(DefBearerBatchTerminateNotifyReq req);
}
