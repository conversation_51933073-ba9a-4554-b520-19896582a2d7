package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 默载批量申请结果通知Data
 * @DateTime: 2024/5/20 下午3:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchTerminateNotifyDataDTO implements Serializable {

    /**
     * 批量终止调用结果文件路径，可通过文件下载接口获取
     */
    private String terminateResultFilePath;

}
