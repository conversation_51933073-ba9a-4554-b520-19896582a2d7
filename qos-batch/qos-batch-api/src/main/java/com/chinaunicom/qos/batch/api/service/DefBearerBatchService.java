package com.chinaunicom.qos.batch.api.service;

import com.chinaunicom.qos.batch.api.dto.DefBearerBatchAddDTO;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchAddReq;
import com.chinaunicom.qos.batch.api.request.DefBearerBatchTerminateReq;
import com.chinaunicom.qos.common.response.Resp;

public interface DefBearerBatchService {

    @interface Add {}
    Resp<DefBearerBatchAddDTO> add(DefBearerBatchAddReq req);

    @interface AddByOrderEndTime {}
    Resp<DefBearerBatchAddDTO> addByOrderEndTime(DefBearerBatchAddReq req);

    Resp<Void> terminate(DefBearerBatchTerminateReq req);

}
