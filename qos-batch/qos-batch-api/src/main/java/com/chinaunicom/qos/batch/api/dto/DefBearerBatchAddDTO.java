package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 批量默载申请返回参数
 * @DateTime: 2024/5/17 上午10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchAddDTO implements Serializable {

    /**
     * 计划任务ID
     */
    private Long planId;

}
