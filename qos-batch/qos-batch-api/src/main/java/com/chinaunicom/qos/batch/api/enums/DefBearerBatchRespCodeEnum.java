package com.chinaunicom.qos.batch.api.enums;

import lombok.Getter;

/**
 * 批量服务的对外返回描述枚举
 *
 * <AUTHOR>
 * @date 2024/9/24 11:35
 */
@Getter
public enum DefBearerBatchRespCodeEnum {
    R_SUCCESS(0, "成功"),
    R_PARAMETER_ERROR(221000, "请求参数校验失败"),
    FILE_NO_DATA_ERROR(221001, "文件中无可操作的数据,请核实,计划任务ID[%s]"),
    FILE_ANALYSIS_ERROR(221002, "文件内容存在异常解析失败,请核实,计划任务ID[%s]"),
    FILE_NOT_EXISTS_ERROR(221003, "根据路径[%s]查询sftp文件不存在"),
    CALCULATED_END_TIME_ERROR(221004, "批量默载申请订单结束时间[%s]在计划执行完成时间[%s]之前，请核实"),
    R_FILE_NOT_EXISTS_ERROR(222000, "根据请求文件路径查找文件不存在，请核实"),
    R_CM_SERVICE_NOT_USE_ERROR(222001, "通信服务不存在或不在使用中，请核实"),
    R_QOS_PRODUCT_CMS_NOT_EXISTS_ERROR(222002, "产品不存在或与通信服务无关联关系，请核实"),
    R_HOME_PROVINCE_NOT_EXISTS_ERROR(222003, "手机号归属省编码有误，无法转换到具体归属省，请核实"),

    R_REQ_SOURCE_NOT_EQUALS_ERROR(222004, "请求来源校验不一致，请核实"),

    R_PLANID_STATUS_ERROR(222005, "计划ID不存在或状态异常，请核实"),

    R_END_TIME_STATUS_DURATION_ERROR(222006, "已超过结束时间，不允许终止"),
    R_NO_STATUS_IN_ADDSUCCESS_TOBEEXECUTED(222007, "计划下无申请成功或待执行的数据，无需终止，请核实"),
    R_CALL_SNAPSHOT_ERROR(228000, "调用快照服务获取配置信息异常，异常原因[%s]"),
    R_CALL_USER_PRODUCT_ERROR(228001, "调用产品获取服务获取配置信息异常，异常原因[%s]"),
    R_EXECUTE_RESPONSE_DATA_NULL_ERROR(228002, "调用接口返回报文为空"),
    R_CALL_USER_ORDER_ERROR(228006, "调用用户订购异常，异常原因[%s]"),
    R_CALL_DEF_BEARER_ADD_ERROR(228003, "调用默载申请异常，异常原因[%s]"),
    R_CALL_DEF_BEARER_TERMINATE_ERROR(228004, "调用默载终止异常，异常原因[%s]"),
    R_CALL_OLD_DEF_BEARER_ERROR(228005, "调用老系统默载异常，异常原因[%s]"),
    R_SYSTEM_ERROR(228888, "系统异常，请稍后再试");

    final Integer code;
    final String message;

    DefBearerBatchRespCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
