package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 默载批量申请计划结果通知Data
 * @DateTime: 2024/5/20 下午3:55
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchAddPlanNotifyDataDTO implements Serializable {
    /**
     * 计划调用开始时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String planStartTime;
    /**
     * 计划调用结束时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String planEndTime;

}
