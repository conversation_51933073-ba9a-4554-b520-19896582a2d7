package com.chinaunicom.qos.batch.api.request;

import com.chinaunicom.qos.batch.api.dto.DefBearerBatchAddNotifyDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 默载批量申请结果通知请求
 * @DateTime: 2024/5/20 下午4:02
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefBearerBatchAddNotifyReq implements Serializable {
    /**
     * 任务执行ID
     */
    private Long planId;
    
    /**
     * 原始请求来源
     */
    private String originReqSource;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态信息
     */
    private String  msg;

    /**
     * 响应内容，成功返回
     */
    private DefBearerBatchAddNotifyDataDTO data;

}
