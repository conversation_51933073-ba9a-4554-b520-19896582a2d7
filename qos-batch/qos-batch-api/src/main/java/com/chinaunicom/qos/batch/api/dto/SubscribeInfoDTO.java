package com.chinaunicom.qos.batch.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: pengb7
 * @Description: 订购开通信息
 * @DateTime: 2024/5/17 上午10:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubscribeInfoDTO implements Serializable {

    /**
     * QoS系统全局产品ID
     */
    @NotNull(message = "subscribeInfo.qosProductId不可为空")
    private Integer qosProductId;

}
